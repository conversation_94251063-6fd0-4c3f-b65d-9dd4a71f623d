# Python
__pycache__/
*.py[cod]
*$py.class

# Virtual Environment
.venv/
venv/
ENV/
env/

# Media files (large audio files)
*.wav
*.mp3
*.m4a
*.flac
*.ogg
*.aac
*.wma

# Processed data
processed/
output/
temp_audio/
transcriptions/

# Model files
models/
checkpoints/
*.pt
*.pth
*.onnx
*.pb

# Environment variables
.env
.env.*
!.env.example

# Logs
*.log
logs/

# OS
.DS_Store
Thumbs.db

# IDE
.vscode/
.idea/

# Project specific
data/
uploads/
cache/
tmp/