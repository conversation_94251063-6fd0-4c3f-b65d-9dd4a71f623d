# Enterprise AI Search with Agentic Orchestrator

- Agentic Orchestrator using tool use pattern
- Context engineering via `context7` multi-source retrieval
- Simple chat UI (Vite + React) streaming from Azure OpenAI
- Optional Python orchestrator that uses `azure-ai-projects` for Azure AI Foundry

## Monorepo Layout

- `search-frontend/` — React UI and Node server with orchestrator + tools
- `orchestrator/` — FastAPI service using `azure-ai-projects` (optional)
- `text/ doc/ audio/ image/ video/` — existing modality-specific assets/services

## Run locally

1) Create `search-frontend/.env` from `.env.example` and fill Azure settings

2) From `search-frontend/`:

```
pnpm install
pnpm dev
```

Web: http://localhost:5173
API: http://localhost:8787

## Azure AI Projects (AI Foundry)

The Python service in `orchestrator/` uses `azure-ai-projects` to create an agent and tools connected to Azure AI Search + Blob. See `orchestrator/README.md` for setup and deployment guidance.

## MCP Tools (optional)

The Node orchestrator exposes an `mcpCall` tool that forwards to an HTTP bridge if `MCP_HTTP_URL` is set. Implement a minimal bridge that accepts POST `/call-tool` with `{ tool, args }` and returns the tool result.

