# Enterprise AI Search - Setup Guide

## Quick Start

### 1. Start Data Catalog (Optional but Recommended)

```bash
cd data-catalog
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
pip install -r requirements.txt
python app.py
```

The Data Catalog will start on http://localhost:8081

### 2. Configure Search Frontend

```bash
cd search-frontend
cp .env.example .env
```

Edit `.env` and add your Azure credentials:
- `AZURE_OPENAI_API_KEY`
- `AZURE_OPENAI_RESOURCE`
- `AZURE_OPENAI_DEPLOYMENT`
- `AZURE_SEARCH_ENDPOINT`
- `AZURE_SEARCH_KEY`
- `AZURE_SEARCH_INDEX`
- `AZURE_STORAGE_CONNECTION_STRING`

### 3. Start Search Frontend

```bash
cd search-frontend
pnpm install
pnpm dev
```

The application will be available at:
- **Web UI**: http://localhost:5174 (or 5173 if available)
- **API**: http://localhost:8787

### 4. Verify Setup

1. Check the API console for "Auto-registering data sources" messages
2. Open the web UI at http://localhost:5174
3. Try a query like "What documents are available?"

## Architecture Overview

### New Unified Architecture

```
┌─────────────────────┐
│   Data Catalog      │ ← Centralized metadata registry
│   (port 8081)       │   (All data sources register here)
└──────────┬──────────┘
           │
           ↓
┌─────────────────────┐
│  Cross-Modal Search │ ← Intent analysis + multi-source retrieval
│  (replaces context7)│   (Queries catalog, routes to sources)
└──────────┬──────────┘
           │
           ↓
┌─────────────────────┐
│ Unified Processor   │ ← Single entry point for all queries
│  (orchestration)    │   (Tools: retrieveContext, modality tools)
└─────────────────────┘
```

### Services

1. **Data Catalog** (port 8081) - FastAPI service
   - Tracks all data sources and their capabilities
   - Health monitoring
   - Dynamic source discovery

2. **Search Frontend** (port 8787) - Express + React
   - Main API endpoint: `/api/chat`
   - Auto-registers sources on startup
   - Uses unified query processor

3. **Optional Modality Services** - Configure via env vars:
   - `DOC_SEARCH_ENDPOINT` / `DOC_INDEX_ENDPOINT`
   - `IMAGE_SEARCH_ENDPOINT` / `IMAGE_INDEX_ENDPOINT`
   - `AUDIO_SEARCH_ENDPOINT` / `AUDIO_INDEX_ENDPOINT`
   - `VIDEO_SEARCH_ENDPOINT` / `VIDEO_PROCESS_ENDPOINT`

## Without Data Catalog

The system works without Data Catalog using fallback configuration from environment variables. However, you'll lose:
- Dynamic source discovery
- Health monitoring
- Automatic routing optimization

## Troubleshooting

### "Data Catalog service not available"
This is a warning, not an error. The system falls back to env var configuration.

To use Data Catalog:
1. Start the Data Catalog service first
2. Restart the Node server
3. You should see "✓ Registered [Service Name]" messages

### Port already in use
If port 5173 or 8787 is in use, either:
- Stop the conflicting service
- Change `PORT` in `.env`

### TypeScript/ESM errors
Make sure you're using `tsx watch` (already configured in package.json):
```bash
pnpm dev  # Uses tsx internally
```

## Features

✅ **Unified Query Processing** - Single intelligent router
✅ **Cross-Modal Search** - Searches text, docs, images, audio, video
✅ **Intent Analysis** - Auto-detects query type
✅ **Source Citations** - All responses cite sources as [S1], [S2]
✅ **Modality Tags** - Results tagged as [TEXT], [DOCUMENT], [IMAGE], etc.
✅ **File Upload** - Upload and process documents, images, audio, video
✅ **Streaming Responses** - Real-time response streaming
✅ **Graceful Fallback** - Works without Data Catalog

## Development

- Frontend: React + Vite (TypeScript)
- Backend: Express + Azure OpenAI (TypeScript)
- Dev server: Uses `tsx` for hot reloading
- Data Catalog: FastAPI + SQLite (async)

## Next Steps

1. Configure modality service endpoints in `.env`
2. Each service will auto-register with Data Catalog
3. Test cross-modal queries like "find videos about X" or "show images and documents about Y"
