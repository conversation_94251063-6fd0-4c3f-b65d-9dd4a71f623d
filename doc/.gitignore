# Python
__pycache__/
*.py[cod]
*$py.class

# Virtual Environment
.venv/
venv/
ENV/
env/

# Documents (processed documents)
processed_docs/
*.pdf
*.doc
*.docx
*.xls
*.xlsx
*.ppt
*.pptx
!sample*.pdf
!example*.pdf

# DocGPT specific
DocGPT/vector_db/
DocGPT/uploads/
DocGPT/ngrok
DocGPT/ngrok.tgz
DocGPT/.streamlit/

# TableGPT specific
TableGPT/data/
TableGPT/*.db
TableGPT/*.sqlite
TableGPT/*.duckdb
TableGPT/models/

# Vector databases
vector_db/
embeddings/
*.faiss
*.index
chromadb/

# Environment variables
.env
.env.*
!.env.example

# Database files
*.db
*.sqlite
*.sqlite3

# Logs
*.log
logs/

# OS
.DS_Store
Thumbs.db

# IDE
.vscode/
.idea/

# Project specific
data/
uploads/
cache/
tmp/
output/