# Dependencies
node_modules/
.pnp
.pnp.js
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz

# Build outputs
dist/
build/
.vite/
.next/
.nuxt/
.output/
.cache/
public/build/

# Testing
coverage/
.nyc_output/

# TypeScript
*.tsbuildinfo
.tsc-cache/

# Package managers
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
.npm/

# Environment variables
.env
.env.*
!.env.example
.env.local
.env.production

# IDE
.vscode/
.idea/
*.swp
*.swo
*.sublime-project
*.sublime-workspace

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Temporary files
.tmp/
.temp/
tmp/
temp/

# Vite
vite.config.js.timestamp-*
vite.config.ts.timestamp-*

# Project specific
uploads/
data/
*.local