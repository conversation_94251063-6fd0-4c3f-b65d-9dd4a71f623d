{"name": "enterprise-ai-search", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "concurrently -n web,api -c blue,green \"vite dev --host\" \"tsx watch server/index.ts\"", "dev:web": "vite dev --host", "dev:api": "tsx watch server/index.ts", "build": "vite build && tsc -p tsconfig.server.json", "preview": "vite preview --host", "preview:server": "node dist/server/index.js", "lint": "echo 'No linter configured'"}, "dependencies": {"@ai-sdk/azure": "^2.0.20", "@azure/storage-blob": "^12.25.0", "ai": "^5.0.23", "axios": "^1.11.0", "express": "^5.1.0", "multer": "^2.0.2", "openai": "^5.15.0", "react": "^19.1.1", "react-dom": "^19.1.1", "zod": "^3.23.8"}, "devDependencies": {"@types/express": "^5.0.3", "@types/multer": "^2.0.0", "@types/node": "^24.3.0", "@types/react": "^19.1.11", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.1", "concurrently": "^9.2.1", "dotenv": "^17.2.1", "ts-node-dev": "^2.0.0", "tsx": "^4.20.6", "typescript": "^5.5.4", "vite": "^7.1.3"}}