import { streamText, tool } from 'ai'
import { createAzure } from '@ai-sdk/azure'
import { z } from 'zod'
import axios from 'axios'
import { crossModalSearch, getLatestDocuments } from './cross-modal-search.js'

const azure = createAzure({
  apiKey: process.env.AZURE_OPENAI_API_KEY || '',
  resourceName: process.env.AZURE_OPENAI_RESOURCE || '',
})

const chatDeployment = process.env.AZURE_OPENAI_DEPLOYMENT || 'gpt-4o-mini'

type ProcessorOptions = {
  message: string
  imageDataUrl?: string
  blobUrl?: string
  fileName?: string
}

/**
 * Unified Query Processor
 *
 * Combines:
 * - Intent Analysis: Determine query type and target modalities
 * - Cross-Modal Search: Intelligent routing via Data Catalog
 * - Tool Orchestration: Execute appropriate tools based on intent
 * - Result Aggregation: Merge and rank results
 *
 * Replaces the separate orchestrator + context7 pattern
 */

// Tool: Cross-modal retrieval with intent analysis
const retrieveContext = tool({
  description:
    'Retrieve enterprise context across text, docs, images, audio, video using cross-modal search. Automatically routes to appropriate sources. Use latest=true for most recent docs.',
  inputSchema: z.object({
    query: z.string().describe('Natural language query.'),
    latest: z.boolean().optional().describe('Whether to prioritize latest documents'),
    modalities: z
      .array(z.enum(['text', 'document', 'image', 'audio', 'video', 'table']))
      .optional()
      .describe('Specific modalities to search (auto-detected if not provided)'),
  }),
  execute: async ({
    query,
    latest,
    modalities,
  }: {
    query: string
    latest?: boolean
    modalities?: string[]
  }) => {
    const result = latest
      ? await getLatestDocuments(query)
      : await crossModalSearch(query, modalities)

    return {
      contextText: result.contextText,
      sources: result.sources,
      modalities: result.modalities,
      itemCount: result.items.length,
    }
  },
})

// Tool: fetch text from an Azure Blob
const fetchBlobText = tool({
  description:
    'Fetch the textual content of a blob via HTTP(S). Use when a blob URL is provided as a source.',
  inputSchema: z.object({
    url: z.string().url(),
    sasToken: z.string().optional(),
  }),
  execute: async ({ url, sasToken }: { url: string; sasToken?: string }) => {
    const full = sasToken ? `${url}${url.includes('?') ? '&' : '?'}${sasToken}` : url
    try {
      const { data } = await axios.get(full, { responseType: 'text', timeout: 10000 })
      const text = typeof data === 'string' ? data : JSON.stringify(data)
      return { text: text.slice(0, 5000) }
    } catch (e: any) {
      return { error: 'Failed to fetch blob', detail: e?.message }
    }
  },
})

// Tool: MCP bridge over HTTP (optional)
const mcpCall = tool({
  description: 'Call an MCP tool via an HTTP bridge. Requires MCP_HTTP_URL to be set.',
  inputSchema: z.object({
    tool: z.string().describe('MCP tool name to call'),
    args: z.record(z.any()).default({}),
  }),
  execute: async ({ tool, args }: { tool: string; args?: Record<string, unknown> }) => {
    const mcpUrl = process.env.MCP_HTTP_URL
    if (!mcpUrl) return { error: 'MCP bridge not configured' }
    try {
      const { data } = await axios.post(`${mcpUrl}/call-tool`, { tool, args: args ?? {} })
      return data
    } catch (e: any) {
      return { error: 'MCP call failed', detail: e?.message }
    }
  },
})

// Modality-specific tools (dynamic based on env)
const createModalityTools = () => {
  const tools: Record<string, any> = {}

  // Document ingestion
  if (process.env.DOC_INDEX_ENDPOINT) {
    tools.docIngest = tool({
      description:
        'Ingest a document blob for Doc search (Docling, embeddings, index). Returns ingestion status.',
      inputSchema: z.object({
        blobUrl: z.string().url(),
        fileName: z.string().optional(),
      }),
      execute: async ({ blobUrl, fileName }: { blobUrl: string; fileName?: string }) => {
        const ep = process.env.DOC_INDEX_ENDPOINT!
        try {
          const { data } = await axios.post(ep, { blobUrl, fileName }, { timeout: 30000 })
          return data
        } catch (e: any) {
          return { error: 'Doc ingestion failed', detail: e?.message }
        }
      },
    })
  }

  // Document search
  if (process.env.DOC_SEARCH_ENDPOINT) {
    tools.docSearch = tool({
      description: 'Search documents using the doc search service. Returns relevant document chunks.',
      inputSchema: z.object({ query: z.string() }),
      execute: async ({ query }: { query: string }) => {
        const ep = process.env.DOC_SEARCH_ENDPOINT!
        try {
          const { data } = await axios.post(ep, { query }, { timeout: 10000 })
          return data
        } catch (e: any) {
          // Fallback to cross-modal search
          const result = await crossModalSearch(query, ['document', 'text'])
          return { contextText: result.contextText, sources: result.sources }
        }
      },
    })
  }

  // Image ingestion
  if (process.env.IMAGE_INDEX_ENDPOINT) {
    tools.imageIngest = tool({
      description:
        'Ingest an image at a blob URL into the image search system for captioning + embedding. Returns caption and status.',
      inputSchema: z.object({ blobUrl: z.string().url() }),
      execute: async ({ blobUrl }: { blobUrl: string }) => {
        const ep = process.env.IMAGE_INDEX_ENDPOINT!
        try {
          const { data } = await axios.post(ep, { blobUrl }, { timeout: 30000 })
          return data
        } catch (e: any) {
          return { error: 'Image ingestion failed', detail: e?.message }
        }
      },
    })
  }

  // Image search
  if (process.env.IMAGE_SEARCH_ENDPOINT) {
    tools.imageSearch = tool({
      description: 'Search similar images or caption-based search in the image system.',
      inputSchema: z.object({ query: z.string() }),
      execute: async ({ query }: { query: string }) => {
        const ep = process.env.IMAGE_SEARCH_ENDPOINT!
        try {
          const { data } = await axios.post(ep, { query }, { timeout: 10000 })
          return data
        } catch (e: any) {
          return { error: 'Image search failed', detail: e?.message }
        }
      },
    })
  }

  // Audio ingestion
  if (process.env.AUDIO_INDEX_ENDPOINT) {
    tools.audioIngest = tool({
      description:
        'Ingest an audio blob into the audio search pipeline for transcription, tagging, and embedding.',
      inputSchema: z.object({ blobUrl: z.string().url(), fileName: z.string().optional() }),
      execute: async ({ blobUrl, fileName }: { blobUrl: string; fileName?: string }) => {
        const ep = process.env.AUDIO_INDEX_ENDPOINT!
        try {
          const { data } = await axios.post(ep, { blobUrl, fileName }, { timeout: 60000 })
          return data
        } catch (e: any) {
          return { error: 'Audio ingestion failed', detail: e?.message }
        }
      },
    })
  }

  // Audio search
  if (process.env.AUDIO_SEARCH_ENDPOINT) {
    tools.audioSearch = tool({
      description: 'Search audio metadata (transcripts/tags) for a text query.',
      inputSchema: z.object({ query: z.string() }),
      execute: async ({ query }: { query: string }) => {
        const ep = process.env.AUDIO_SEARCH_ENDPOINT!
        try {
          const { data } = await axios.post(ep, { query }, { timeout: 10000 })
          return data
        } catch (e: any) {
          return { error: 'Audio search failed', detail: e?.message }
        }
      },
    })
  }

  // Video processing
  if (process.env.VIDEO_PROCESS_ENDPOINT) {
    tools.videoProcess = tool({
      description:
        'Process a video blob (extract audio, captions, transcript, diarization, embeddings). Returns summary and stores results.',
      inputSchema: z.object({ blobUrl: z.string().url(), fileName: z.string().optional() }),
      execute: async ({ blobUrl, fileName }: { blobUrl: string; fileName?: string }) => {
        const ep = process.env.VIDEO_PROCESS_ENDPOINT!
        try {
          const { data } = await axios.post(ep, { blobUrl, fileName }, { timeout: 120000 })
          return data
        } catch (e: any) {
          return { error: 'Video processing failed', detail: e?.message }
        }
      },
    })
  }

  // Video search
  if (process.env.VIDEO_SEARCH_ENDPOINT) {
    tools.videoSearch = tool({
      description:
        'Ask questions over processed video metadata in SQL; returns JSON with answer, timestamp, and video path.',
      inputSchema: z.object({ query: z.string() }),
      execute: async ({ query }: { query: string }) => {
        const ep = process.env.VIDEO_SEARCH_ENDPOINT!
        try {
          const { data } = await axios.post(ep, { query }, { timeout: 10000 })
          return data
        } catch (e: any) {
          return { error: 'Video search failed', detail: e?.message }
        }
      },
    })
  }

  return tools
}

/**
 * Main unified query processor
 */
export const processQuery = async (options: ProcessorOptions) => {
  const { message, imageDataUrl, blobUrl, fileName } = options

  const model = azure(chatDeployment) as any

  // Enhanced system prompt with unified processing guidance
  const systemText = [
    'You are an enterprise AI Unified Query Processor.',
    'You coordinate intelligent routing across text, document, image, audio, video, and table sources.',
    '',
    'CORE CAPABILITIES:',
    '- Use retrieveContext tool for most queries - it automatically routes to appropriate sources',
    '- Intent is analyzed automatically (text, document, image, audio, video)',
    '- Cross-modal search aggregates results from multiple sources',
    '- Always cite sources as [S1], [S2], etc. from tool responses',
    '',
    'ROUTING STRATEGY:',
    '- For general queries: call retrieveContext without specifying modalities (auto-detect)',
    '- For specific media: specify modalities array (e.g., ["image", "video"])',
    '- For latest/recent queries: use latest=true in retrieveContext',
    '- For file ingestion: detect file type and call appropriate ingest tool',
    '',
    'WHEN ATTACHMENTS PRESENT:',
    '- Images: Can ingest via imageIngest if needed for search',
    '- Audio: Call audioIngest for transcription and search',
    '- Video: Call videoProcess for full multi-modal analysis',
    '- Documents: Call docIngest for semantic search',
    '',
    'CROSS-MODAL QUERIES:',
    '- Example: "find videos discussing X" → retrieveContext with modalities=["video"]',
    '- Example: "show images and documents about Y" → modalities=["image", "document"]',
    '- Always leverage multiple sources when relevant',
    '',
    'If tools return no context, state limitations clearly and answer best-effort.',
  ].join('\n')

  // Build user message with optional image
  const userParts: any[] = []
  if (imageDataUrl) {
    userParts.push({ type: 'image', image: { url: imageDataUrl } })
  }

  // Add blob/file hint if present
  let messageText = message
  if (blobUrl) {
    messageText += `\n\n[Attached file: ${fileName || 'unknown'} at ${blobUrl}]`
  }

  userParts.push({ type: 'text', text: messageText })

  // Create tools dynamically
  const modalityTools = createModalityTools()

  const result = await streamText({
    model,
    messages: [
      { role: 'system', content: systemText },
      { role: 'user', content: userParts },
    ],
    tools: {
      retrieveContext,
      fetchBlobText,
      mcpCall,
      ...modalityTools,
    },
  })

  return result
}
