import dotenv from "dotenv";
import { existsSync } from "fs";
import { fileURLToPath } from "url";
import { dirname, join } from "path";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load .env BEFORE other imports to ensure env vars are available
// Probe multiple locations so dev (tsx), preview, and built (dist) all work
const candidateEnvPaths = [
  // When running with tsx from project root: server -> ../.env (search-frontend/.env)
  join(__dirname, "../.env"),
  // When running built JS: dist/server -> ../../.env (project root .env)
  join(__dirname, "../../.env"),
  // Fallback to CWD
  join(process.cwd(), ".env"),
];

for (const p of candidateEnvPaths) {
  if (existsSync(p)) {
    dotenv.config({ path: p });
    break;
  }
}

// Validate required environment variables
const requiredEnvVars = [
  "AZURE_OPENAI_API_KEY",
  "AZURE_OPENAI_RESOURCE",
  "AZURE_OPENAI_DEPLOYMENT",
] as const;

for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    console.error(`❌ Missing required environment variable: ${envVar}`);
    console.error(`   Check your .env file at: ${join(__dirname, "../.env")}`);
    process.exit(1);
  }
}

console.log(
  `✅ Environment loaded: Azure OpenAI Resource = ${process.env.AZURE_OPENAI_RESOURCE}`
);

import express, { type Request, type Response } from "express";
import multer from "multer";
import { uploadBufferToBlob } from "./blob.js";
import { processQuery } from "./unified-query-processor.js";
import { crossModalSearch, getLatestDocuments } from "./cross-modal-search.js";
import { autoRegisterSources } from "./data-catalog-client.js";

const app = express();
const upload = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: 200 * 1024 * 1024 },
});

const PORT = process.env.PORT ? Number(process.env.PORT) : 8787;

app.get("/health", (_req: Request, res: Response) => {
  res.json({ ok: true });
});

app.post("/api/chat", upload.any(), async (req: Request, res: Response) => {
  try {
    const userMessage = (req.body?.message as string) || "";
    const files = Array.isArray(req.files) ? req.files : undefined;
    const file = files && files.length ? files[0] : undefined;

    // Detect if user wants latest documents
    const wantsLatest = /latest|newest|recent/i.test(userMessage);

    // If a file is provided, upload to Azure Blob
    let imageDataUrl: string | undefined;
    let blobUrl: string | undefined;
    let fileName: string | undefined;

    if (file) {
      try {
        const uploaded = await uploadBufferToBlob(
          file.buffer,
          file.originalname,
          file.mimetype
        );
        blobUrl = uploaded.url;
        fileName = file.originalname;
      } catch (e) {
        console.error("Blob upload failed", e);
      }
      if (file.mimetype?.startsWith("image/")) {
        imageDataUrl = `data:${file.mimetype};base64,${file.buffer.toString(
          "base64"
        )}`;
      }
    }

    res.setHeader("Content-Type", "text/plain; charset=utf-8");
    res.setHeader("Transfer-Encoding", "chunked");

    // Get initial context using cross-modal search (optional priming)
    let seed: any = { contextText: "", sources: [], modalities: [] };
    if (!file || !file.mimetype?.startsWith("image/")) {
      // Only prime with context if no image (let unified processor handle routing)
      try {
        seed = wantsLatest
          ? await getLatestDocuments(userMessage)
          : await crossModalSearch(userMessage);
      } catch (e) {
        console.error("Cross-modal search failed", e);
      }
    }

    // Provide priming context
    const preface: string[] = [];
    if (seed.contextText) {
      preface.push(
        `Context from ${seed.modalities.join(", ") || "search"}\n${
          seed.contextText
        }`
      );
    }
    if (blobUrl && file) {
      preface.push(
        `Uploaded: ${file.originalname} (${file.mimetype})\nBlob URL: ${blobUrl}\n` +
          "Use appropriate ingest/search tools as needed."
      );
    }
    if (preface.length) res.write(preface.join("\n\n") + "\n\n");

    // Process query with unified processor
    const result = await processQuery({
      message: userMessage,
      imageDataUrl,
      blobUrl,
      fileName,
    });

    // Stream response
    for await (const delta of result.textStream) {
      res.write(delta);
    }

    // Add sources footer
    const footer = seed.sources.length
      ? `\n\nSources: ${seed.sources
          .map((s: string, i: number) => `[S${i + 1}] ${s}`)
          .join(" | ")}`
      : "";
    if (footer) res.write(footer);
    res.end();
  } catch (err) {
    console.error(err);
    res.status(500).end("Server error");
  }
});

app.use((_req, res) => {
  res.status(404).json({ error: "Not found" });
});

app.listen(PORT, async () => {
  console.log(`API listening on http://localhost:${PORT}`);

  // Auto-register data sources with catalog on startup
  try {
    await autoRegisterSources();
  } catch (error) {
    console.warn("Failed to auto-register sources:", error);
  }
});
