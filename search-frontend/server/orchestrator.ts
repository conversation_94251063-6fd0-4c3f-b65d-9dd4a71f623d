import { streamText, tool } from 'ai'
import { createAzure } from '@ai-sdk/azure'
import { z } from 'zod'
import axios from 'axios'
import { context7, getLatestDocs } from './context7.js'

const azure = createAzure({
  apiKey: process.env.AZURE_OPENAI_API_KEY || '',
  resourceName: process.env.AZURE_OPENAI_RESOURCE || '',
})

const chatDeployment = process.env.AZURE_OPENAI_DEPLOYMENT || 'gpt-4o-mini'

type RunOptions = {
  message: string
  imageDataUrl?: string
}

// Tool: retrieve context via context7 (multi-source, recency-aware)
const retrieveContext = tool({
  description: 'Retrieve enterprise context across text, docs, images, audio, video using context7. Use latest=true for most recent docs.',
  inputSchema: z.object({
    query: z.string().describe('Natural language query.'),
    latest: z.boolean().optional().describe('Whether to prioritize latest documents'),
  }),
  execute: async ({ query, latest }: { query: string; latest?: boolean }) => {
    const { contextText, sources } = latest ? await getLatestDocs(query) : await context7(query)
    return { contextText, sources }
  },
})

// Tool: fetch text from an Azure Blob (SAS or managed public URL)
const fetchBlobText = tool({
  description: 'Fetch the textual content of a blob via HTTP(S). Use when a blob URL is provided as a source.',
  inputSchema: z.object({
    url: z.string().url(),
    sasToken: z.string().optional(),
  }),
  execute: async ({ url, sasToken }: { url: string; sasToken?: string }) => {
    const full = sasToken ? `${url}${url.includes('?') ? '&' : '?'}${sasToken}` : url
    const { data } = await axios.get(full, { responseType: 'text' })
    const text = typeof data === 'string' ? data : JSON.stringify(data)
    return { text: text.slice(0, 5000) }
  },
})

// Tool: MCP bridge over HTTP (optional)
const mcpCall = tool({
  description: 'Call an MCP tool via an HTTP bridge. Requires MCP_HTTP_URL to be set.',
  inputSchema: z.object({
    tool: z.string().describe('MCP tool name to call'),
    args: z.record(z.any()).default({}),
  }),
  execute: async ({ tool, args }: { tool: string; args?: Record<string, unknown> }) => {
    const mcpUrl = process.env.MCP_HTTP_URL
    if (!mcpUrl) return { error: 'MCP bridge not configured' }
    try {
      const { data } = await axios.post(`${mcpUrl}/call-tool`, { tool, args: args ?? {} })
      return data
    } catch (e: any) {
      return { error: 'MCP call failed', detail: e?.message }
    }
  },
})

export const runAgenticOrchestrator = async ({ message, imageDataUrl }: RunOptions) => {
  const model = azure(chatDeployment) as any

  const systemText = [
    'You are an enterprise AI Agentic Orchestrator.',
    'Select and call tools when needed to gather context before answering.',
    'Prefer retrieveContext to ground your answer with sources. When user asks for latest, use latest=true.',
    'Cite sources as [S1], [S2], etc. from the tool responses when present.',
    'If tools return no context, state limitations and answer best-effort.',
  ].join('\n')

  const userParts: any[] = []
  if (imageDataUrl) {
    userParts.push({ type: 'image', image: { url: imageDataUrl } })
  }
  userParts.push({ type: 'text', text: message })

  const result = await streamText({
    model,
    messages: [
      { role: 'system', content: systemText },
      { role: 'user', content: userParts },
    ],
    tools: {
      retrieveContext,
      fetchBlobText,
      mcpCall,
      // modality-specific optional tools (endpoints provided via env)
      imageIngest: tool({
        description: 'Ingest an image at a blob URL into the image search system for captioning + embedding',
        inputSchema: z.object({ blobUrl: z.string().url() }),
        execute: async ({ blobUrl }: { blobUrl: string }) => {
          const ep = process.env.IMAGE_INDEX_ENDPOINT
          if (!ep) return { error: 'IMAGE_INDEX_ENDPOINT not set' }
          const { data } = await axios.post(ep, { blobUrl })
          return data
        },
      }),
      imageSearch: tool({
        description: 'Search similar images or caption-based search in the image system',
        inputSchema: z.object({ query: z.string() }),
        execute: async ({ query }: { query: string }) => {
          const ep = process.env.IMAGE_SEARCH_ENDPOINT
          if (!ep) return { error: 'IMAGE_SEARCH_ENDPOINT not set' }
          const { data } = await axios.post(ep, { query })
          return data
        },
      }),
      audioIngest: tool({
        description: 'Ingest an audio blob into the audio search pipeline for transcription, tagging, and embedding',
        inputSchema: z.object({ blobUrl: z.string().url(), fileName: z.string().optional() }),
        execute: async ({ blobUrl, fileName }: { blobUrl: string; fileName?: string }) => {
          const ep = process.env.AUDIO_INDEX_ENDPOINT
          if (!ep) return { error: 'AUDIO_INDEX_ENDPOINT not set' }
          const { data } = await axios.post(ep, { blobUrl, fileName })
          return data
        },
      }),
      audioSearch: tool({
        description: 'Search audio metadata (transcripts/tags) for a text query',
        inputSchema: z.object({ query: z.string() }),
        execute: async ({ query }: { query: string }) => {
          const ep = process.env.AUDIO_SEARCH_ENDPOINT
          if (!ep) return { error: 'AUDIO_SEARCH_ENDPOINT not set' }
          const { data } = await axios.post(ep, { query })
          return data
        },
      }),
      videoProcess: tool({
        description: 'Process a video blob (extract audio, captions, transcript, diarization, embeddings). Returns summary and stores results in SQL/Blob.',
        inputSchema: z.object({ blobUrl: z.string().url(), fileName: z.string().optional() }),
        execute: async ({ blobUrl, fileName }: { blobUrl: string; fileName?: string }) => {
          const ep = process.env.VIDEO_PROCESS_ENDPOINT
          if (!ep) return { error: 'VIDEO_PROCESS_ENDPOINT not set' }
          const { data } = await axios.post(ep, { blobUrl, fileName })
          return data
        },
      }),
      videoSearch: tool({
        description: 'Ask questions over processed video metadata in SQL; returns JSON with answer, timestamp, and video path',
        inputSchema: z.object({ query: z.string() }),
        execute: async ({ query }: { query: string }) => {
          const ep = process.env.VIDEO_SEARCH_ENDPOINT
          if (!ep) return { error: 'VIDEO_SEARCH_ENDPOINT not set' }
          const { data } = await axios.post(ep, { query })
          return data
        },
      }),
      docIngest: tool({
        description: 'Ingest a document blob for Doc search (Docling, embeddings, index).',
        inputSchema: z.object({ blobUrl: z.string().url(), fileName: z.string().optional() }),
        execute: async ({ blobUrl, fileName }: { blobUrl: string; fileName?: string }) => {
          const ep = process.env.DOC_INDEX_ENDPOINT
          if (!ep) return { error: 'DOC_INDEX_ENDPOINT not set' }
          const { data } = await axios.post(ep, { blobUrl, fileName })
          return data
        },
      }),
      docSearch: tool({
        description: 'Search documents using the doc search service or Azure AI Search if not set',
        inputSchema: z.object({ query: z.string() }),
        execute: async ({ query }: { query: string }) => {
          const ep = process.env.DOC_SEARCH_ENDPOINT
          if (!ep) {
            // fallback to context7 for text queries
            const { contextText, sources } = await context7(query)
            return { contextText, sources }
          }
          const { data } = await axios.post(ep, { query })
          return data
        },
      }),
    },
  })

  return result
}
