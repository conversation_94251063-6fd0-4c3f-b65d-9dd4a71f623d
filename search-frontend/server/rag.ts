import axios from 'axios'

type SearchResult = {
  content: string
  source: string
}

const AZUR<PERSON>_SEARCH_ENDPOINT = process.env.AZURE_SEARCH_ENDPOINT || ''
const AZURE_SEARCH_KEY = process.env.AZURE_SEARCH_KEY || ''
const AZURE_SEARCH_INDEX = process.env.AZURE_SEARCH_INDEX || ''

export async function getRagContext(
  query: string
): Promise<{ contextText: string; sources: string[] }> {
  if (!AZURE_SEARCH_ENDPOINT || !AZURE_SEARCH_KEY || !AZURE_SEARCH_INDEX) {
    return { contextText: '', sources: [] }
  }
  const url = `${AZURE_SEARCH_ENDPOINT}/indexes/${AZURE_SEARCH_INDEX}/docs/search?api-version=2024-07-01`
  const body = { search: query, top: 5 }
  const { data } = await axios.post(url, body, {
    headers: {
      'Content-Type': 'application/json',
      'api-key': AZURE_SEARCH_KEY,
    },
  })

  const results: SearchResult[] = (data.value || []).map((item: any) => ({
    content: item.content || item.text || JSON.stringify(item).slice(0, 500),
    source: item.source || item.metadata_storage_name || item.id || 'unknown',
  }))

  const contextText = results
    .map((r, i) => `[S${i + 1}] ${r.content}`)
    .join('\n\n')
  const sources = results.map((r) => r.source)
  return { contextText, sources }
}
