import { BlobServiceClient } from '@azure/storage-blob'

const AZURE_STORAGE_CONNECTION_STRING = process.env.AZURE_STORAGE_CONNECTION_STRING || ''
const UPLOAD_CONTAINER = process.env.UPLOAD_CONTAINER || 'uploads'

export type BlobUploadResult = {
  url: string
  blobName: string
}

export const uploadBufferToBlob = async (
  buffer: Buffer,
  blobName: string,
  contentType?: string
): Promise<BlobUploadResult> => {
  if (!AZURE_STORAGE_CONNECTION_STRING) {
    throw new Error('AZURE_STORAGE_CONNECTION_STRING not set')
  }
  const svc = BlobServiceClient.fromConnectionString(AZURE_STORAGE_CONNECTION_STRING)
  const container = svc.getContainerClient(UPLOAD_CONTAINER)
  await container.createIfNotExists()
  const block = container.getBlockBlobClient(blobName)
  const headers = contentType ? { blobHTTPHeaders: { blobContentType: contentType } } : undefined
  await block.uploadData(buffer, headers)
  return { url: block.url, blobName }
}

