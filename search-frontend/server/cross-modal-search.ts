import axios from 'axios'

const DATA_CATALOG_ENDPOINT = process.env.DATA_CATALOG_ENDPOINT || 'http://localhost:8081'
const CONTEXT_MAX_RESULTS = Number(process.env.CONTEXT_MAX_RESULTS || 6)

type SearchDoc = {
  content: string
  source: string
  modality: string
  lastUpdated?: string
  score?: number
}

export type CrossModalResult = {
  contextText: string
  sources: string[]
  items: SearchDoc[]
  modalities: string[]
}

type DataSource = {
  source_id: string
  name: string
  source_type: string
  modality: string
  endpoint: string
  metadata: {
    capabilities: string[]
    record_count?: number
    description?: string
  }
  health_status: string
}

// Lightweight normalization for deduplication
const normalizeText = (t: string) => t.replace(/\s+/g, ' ').trim()

const dedupeByContent = (items: SearchDoc[]) => {
  const seen = new Set<string>()
  const out: SearchDoc[] = []
  for (const it of items) {
    const key = normalizeText((it.content || '').slice(0, 280))
    if (!seen.has(key)) {
      seen.add(key)
      out.push(it)
    }
  }
  return out
}

// Enhanced scoring: relevance length + recency + modality boost
const score = (it: SearchDoc, modalityBoost: number = 0) => {
  const len = Math.min(1000, (it.content || '').length)
  const recency = it.lastUpdated ? Date.parse(it.lastUpdated) / 1e12 : 0
  return len / 1000 + recency + modalityBoost
}

// Query Data Catalog for available sources
const getAvailableSources = async (modalities?: string[]): Promise<DataSource[]> => {
  try {
    const response = await axios.post(`${DATA_CATALOG_ENDPOINT}/query-metadata`, {
      health_status: 'healthy',
      capabilities: ['search'],
    })

    let sources = response.data as DataSource[]

    // Filter by modality if specified
    if (modalities && modalities.length > 0) {
      sources = sources.filter(s => modalities.includes(s.modality))
    }

    return sources
  } catch (error) {
    console.warn('Data Catalog unavailable, using fallback configuration', error)
    return getFallbackSources()
  }
}

// Fallback sources from environment variables (legacy compatibility)
const getFallbackSources = (): DataSource[] => {
  const sources: DataSource[] = []

  // Azure Search (text/document)
  if (process.env.AZURE_SEARCH_ENDPOINT) {
    sources.push({
      source_id: 'azure-search-main',
      name: 'Azure AI Search',
      source_type: 'azure-search',
      modality: 'text',
      endpoint: process.env.AZURE_SEARCH_ENDPOINT,
      metadata: { capabilities: ['search', 'vectorize'] },
      health_status: 'healthy',
    })
  }

  // Document search
  if (process.env.DOC_SEARCH_ENDPOINT) {
    sources.push({
      source_id: 'doc-search',
      name: 'Document Search',
      source_type: 'endpoint',
      modality: 'document',
      endpoint: process.env.DOC_SEARCH_ENDPOINT,
      metadata: { capabilities: ['search'] },
      health_status: 'healthy',
    })
  }

  // Image search
  if (process.env.IMAGE_SEARCH_ENDPOINT) {
    sources.push({
      source_id: 'image-search',
      name: 'Image Search',
      source_type: 'endpoint',
      modality: 'image',
      endpoint: process.env.IMAGE_SEARCH_ENDPOINT,
      metadata: { capabilities: ['search'] },
      health_status: 'healthy',
    })
  }

  // Audio search
  if (process.env.AUDIO_SEARCH_ENDPOINT) {
    sources.push({
      source_id: 'audio-search',
      name: 'Audio Search',
      source_type: 'endpoint',
      modality: 'audio',
      endpoint: process.env.AUDIO_SEARCH_ENDPOINT,
      metadata: { capabilities: ['search'] },
      health_status: 'healthy',
    })
  }

  // Video search
  if (process.env.VIDEO_SEARCH_ENDPOINT) {
    sources.push({
      source_id: 'video-search',
      name: 'Video Search',
      source_type: 'endpoint',
      modality: 'video',
      endpoint: process.env.VIDEO_SEARCH_ENDPOINT,
      metadata: { capabilities: ['search'] },
      health_status: 'healthy',
    })
  }

  return sources
}

// Execute search against Azure AI Search
const searchAzureSearch = async (query: string, source: DataSource): Promise<SearchDoc[]> => {
  const endpoint = source.endpoint
  const key = process.env.AZURE_SEARCH_KEY
  const index = process.env.AZURE_SEARCH_INDEX

  if (!endpoint || !key || !index) return []

  try {
    const url = `${endpoint}/indexes/${index}/docs/search?api-version=2024-07-01`
    const { data } = await axios.post(
      url,
      { search: query, top: CONTEXT_MAX_RESULTS },
      { headers: { 'Content-Type': 'application/json', 'api-key': key } }
    )

    return (data.value || []).map((item: any) => ({
      content: item.content || item.text || JSON.stringify(item).slice(0, 1000),
      source: item.source || item.metadata_storage_name || item.id || 'unknown',
      modality: source.modality,
      lastUpdated: item.lastUpdated || item['@lastUpdated'] || item.modified || item.timestamp,
    }))
  } catch (error) {
    console.error(`Error searching ${source.source_id}:`, error)
    return []
  }
}

// Execute search against generic endpoint
const searchEndpoint = async (query: string, source: DataSource): Promise<SearchDoc[]> => {
  try {
    const { data } = await axios.post(source.endpoint, { query }, { timeout: 5000 })

    // Handle array response
    if (Array.isArray(data)) {
      return data.slice(0, CONTEXT_MAX_RESULTS).map((d: any) => ({
        content: d.content || d.text || String(d).slice(0, 1000),
        source: d.source || d.url || d.id || source.name,
        modality: source.modality,
        lastUpdated: d.lastUpdated || d.modified,
      }))
    }

    // Handle object with results
    if (data.results && Array.isArray(data.results)) {
      return data.results.slice(0, CONTEXT_MAX_RESULTS).map((d: any) => ({
        content: d.content || d.text || String(d).slice(0, 1000),
        source: d.source || d.url || d.id || source.name,
        modality: source.modality,
        lastUpdated: d.lastUpdated || d.modified,
      }))
    }

    return []
  } catch (error) {
    console.error(`Error searching ${source.source_id}:`, error)
    return []
  }
}

// Analyze query to determine target modalities
const analyzeQueryIntent = (query: string): string[] => {
  const q = query.toLowerCase()
  const modalities: Set<string> = new Set()

  // Default to text
  modalities.add('text')

  // Document keywords
  if (/\b(document|pdf|doc|report|policy|handbook|manual)\b/i.test(q)) {
    modalities.add('document')
  }

  // Image keywords
  if (/\b(image|photo|picture|visual|diagram|chart|screenshot)\b/i.test(q)) {
    modalities.add('image')
  }

  // Audio keywords
  if (/\b(audio|recording|sound|voice|speech|transcript|podcast)\b/i.test(q)) {
    modalities.add('audio')
  }

  // Video keywords
  if (/\b(video|footage|clip|meeting|presentation|webinar)\b/i.test(q)) {
    modalities.add('video')
  }

  // Table keywords
  if (/\b(table|spreadsheet|csv|excel|data|sql|query)\b/i.test(q)) {
    modalities.add('table')
  }

  // If user says "all" or "everything", search all modalities
  if (/\b(all|everything|any|anywhere)\b/i.test(q)) {
    return ['text', 'document', 'image', 'audio', 'video', 'table']
  }

  return Array.from(modalities)
}

// Apply modality-specific boost for ranking
const getModalityBoost = (modality: string, targetModalities: string[]): number => {
  // Boost documents that match target modalities
  return targetModalities.includes(modality) ? 0.5 : 0
}

/**
 * Cross-Modal Search - Replaces context7 with intelligent multi-source retrieval
 *
 * Features:
 * - Discovers sources via Data Catalog
 * - Intent-based modality routing
 * - Parallel search execution
 * - Unified scoring with modality boost
 * - Deduplication across sources
 */
export const crossModalSearch = async (query: string, targetModalities?: string[]): Promise<CrossModalResult> => {
  // Analyze query intent if modalities not specified
  const modalities = targetModalities || analyzeQueryIntent(query)

  // Get available sources from catalog
  const sources = await getAvailableSources(modalities)

  if (sources.length === 0) {
    console.warn('No data sources available for query')
    return { contextText: '', sources: [], items: [], modalities: [] }
  }

  // Execute searches in parallel
  const searchPromises = sources.map(async (source) => {
    if (source.source_type === 'azure-search') {
      return searchAzureSearch(query, source)
    } else {
      return searchEndpoint(query, source)
    }
  })

  const results = await Promise.all(searchPromises)
  const allDocs = results.flat()

  // Deduplicate
  const uniqueDocs = dedupeByContent(allDocs)

  // Score with modality boost
  const scoredDocs = uniqueDocs.map(doc => ({
    ...doc,
    score: score(doc, getModalityBoost(doc.modality, modalities))
  }))

  // Sort by score and limit
  const ranked = scoredDocs.sort((a, b) => (b.score || 0) - (a.score || 0)).slice(0, CONTEXT_MAX_RESULTS)

  // Build context with modality tags
  const contextText = ranked.map((r, i) => `[S${i + 1}] [${r.modality.toUpperCase()}] ${r.content}`).join('\n\n')
  const sourcesList = ranked.map((r) => r.source)
  const modalitiesUsed = Array.from(new Set(ranked.map(r => r.modality)))

  return {
    contextText,
    sources: sourcesList,
    items: ranked,
    modalities: modalitiesUsed,
  }
}

/**
 * Get latest documents with recency priority
 */
export const getLatestDocuments = async (query: string): Promise<CrossModalResult> => {
  // Add recency hint to query
  const recencyQuery = `${query} sort by latest updates recent`

  // Get sources (prefer text/document)
  const sources = await getAvailableSources(['text', 'document'])

  if (sources.length === 0) {
    return { contextText: '', sources: [], items: [], modalities: [] }
  }

  // Search with recency focus
  const searchPromises = sources.map(async (source) => {
    if (source.source_type === 'azure-search') {
      return searchAzureSearch(recencyQuery, source)
    } else {
      return searchEndpoint(recencyQuery, source)
    }
  })

  const results = await Promise.all(searchPromises)
  const allDocs = results.flat()

  // Sort heavily by recency
  const ranked = allDocs
    .sort((a, b) => {
      const aTime = a.lastUpdated ? Date.parse(a.lastUpdated) : 0
      const bTime = b.lastUpdated ? Date.parse(b.lastUpdated) : 0
      return bTime - aTime
    })
    .slice(0, CONTEXT_MAX_RESULTS)

  const contextText = ranked.map((r, i) => `[S${i + 1}] [${r.modality.toUpperCase()}] ${r.content}`).join('\n\n')
  const sourcesList = ranked.map((r) => r.source)
  const modalitiesUsed = Array.from(new Set(ranked.map(r => r.modality)))

  return {
    contextText,
    sources: sourcesList,
    items: ranked,
    modalities: modalitiesUsed,
  }
}
