import axios from 'axios'

const DATA_CATALOG_ENDPOINT = process.env.DATA_CATALOG_ENDPOINT || 'http://localhost:8081'

export type SourceType = 'azure-search' | 'blob' | 'sql' | 'cosmos' | 'endpoint'
export type Modality = 'text' | 'document' | 'image' | 'audio' | 'video' | 'table'
export type HealthStatus = 'healthy' | 'degraded' | 'unavailable'

export type SourceMetadata = {
  schema_info?: any
  last_indexed?: string
  record_count?: number
  capabilities: string[]
  description?: string
}

export type DataSource = {
  source_id: string
  name: string
  source_type: SourceType
  modality: Modality
  endpoint: string
  metadata: SourceMetadata
  health_status: HealthStatus
  created_at?: string
  updated_at?: string
}

export type RegisterSourceRequest = {
  source_id: string
  name: string
  source_type: SourceType
  modality: Modality
  endpoint: string
  metadata: SourceMetadata
}

export type QueryMetadataRequest = {
  modality?: Modality
  source_type?: SourceType
  capabilities?: string[]
  health_status?: HealthStatus
}

/**
 * Data Catalog Client for Node.js
 *
 * Provides methods to interact with the Data Catalog service
 */
export class DataCatalogClient {
  private baseUrl: string

  constructor(baseUrl?: string) {
    this.baseUrl = baseUrl || DATA_CATALOG_ENDPOINT
  }

  /**
   * Register or update a data source
   */
  async registerSource(request: RegisterSourceRequest): Promise<DataSource> {
    try {
      const { data } = await axios.post<DataSource>(`${this.baseUrl}/register-source`, request)
      return data
    } catch (error: any) {
      console.error('Failed to register source:', error?.message)
      throw error
    }
  }

  /**
   * Query sources by filters
   */
  async queryMetadata(request: QueryMetadataRequest): Promise<DataSource[]> {
    try {
      const { data } = await axios.post<DataSource[]>(`${this.baseUrl}/query-metadata`, request)
      return data
    } catch (error: any) {
      console.error('Failed to query metadata:', error?.message)
      return []
    }
  }

  /**
   * List all registered sources
   */
  async listSources(): Promise<DataSource[]> {
    try {
      const { data } = await axios.get<DataSource[]>(`${this.baseUrl}/list-sources`)
      return data
    } catch (error: any) {
      console.error('Failed to list sources:', error?.message)
      return []
    }
  }

  /**
   * Get a specific source by ID
   */
  async getSource(sourceId: string): Promise<DataSource | null> {
    try {
      const { data } = await axios.get<DataSource>(`${this.baseUrl}/get-source/${sourceId}`)
      return data
    } catch (error: any) {
      console.error('Failed to get source:', error?.message)
      return null
    }
  }

  /**
   * Update health status of a source
   */
  async updateHealth(
    sourceId: string,
    healthStatus: HealthStatus,
    errorMessage?: string
  ): Promise<boolean> {
    try {
      await axios.post(`${this.baseUrl}/update-health`, {
        source_id: sourceId,
        health_status: healthStatus,
        error_message: errorMessage,
      })
      return true
    } catch (error: any) {
      console.error('Failed to update health:', error?.message)
      return false
    }
  }

  /**
   * Delete a source from catalog
   */
  async deleteSource(sourceId: string): Promise<boolean> {
    try {
      await axios.delete(`${this.baseUrl}/delete-source/${sourceId}`)
      return true
    } catch (error: any) {
      console.error('Failed to delete source:', error?.message)
      return false
    }
  }

  /**
   * Check if Data Catalog service is available
   */
  async isAvailable(): Promise<boolean> {
    try {
      const { data } = await axios.get(`${this.baseUrl}/health`, { timeout: 2000 })
      return data?.status === 'healthy'
    } catch {
      return false
    }
  }
}

/**
 * Auto-register sources from environment on startup
 */
export const autoRegisterSources = async () => {
  const client = new DataCatalogClient()

  // Check if catalog is available
  const available = await client.isAvailable()
  if (!available) {
    console.log('Data Catalog service not available, skipping auto-registration')
    return
  }

  console.log('Auto-registering data sources with Data Catalog...')

  // Register Azure AI Search
  if (process.env.AZURE_SEARCH_ENDPOINT && process.env.AZURE_SEARCH_INDEX) {
    await client.registerSource({
      source_id: 'azure-search-main',
      name: 'Azure AI Search - Main Index',
      source_type: 'azure-search',
      modality: 'text',
      endpoint: process.env.AZURE_SEARCH_ENDPOINT,
      metadata: {
        capabilities: ['search', 'vectorize'],
        description: 'Primary text and document search index',
      },
    })
    console.log('✓ Registered Azure AI Search')
  }

  // Register Document Search
  if (process.env.DOC_SEARCH_ENDPOINT) {
    await client.registerSource({
      source_id: 'doc-search-service',
      name: 'Document Search Service',
      source_type: 'endpoint',
      modality: 'document',
      endpoint: process.env.DOC_SEARCH_ENDPOINT,
      metadata: {
        capabilities: ['search'],
        description: 'Docling-based document search',
      },
    })
    console.log('✓ Registered Document Search')
  }

  if (process.env.DOC_INDEX_ENDPOINT) {
    await client.registerSource({
      source_id: 'doc-index-service',
      name: 'Document Ingestion Service',
      source_type: 'endpoint',
      modality: 'document',
      endpoint: process.env.DOC_INDEX_ENDPOINT,
      metadata: {
        capabilities: ['ingest'],
        description: 'Document ingestion and indexing',
      },
    })
    console.log('✓ Registered Document Ingestion')
  }

  // Register Image Services
  if (process.env.IMAGE_SEARCH_ENDPOINT) {
    await client.registerSource({
      source_id: 'image-search-service',
      name: 'Image Search Service',
      source_type: 'endpoint',
      modality: 'image',
      endpoint: process.env.IMAGE_SEARCH_ENDPOINT,
      metadata: {
        capabilities: ['search'],
        description: 'Caption-based image search',
      },
    })
    console.log('✓ Registered Image Search')
  }

  if (process.env.IMAGE_INDEX_ENDPOINT) {
    await client.registerSource({
      source_id: 'image-index-service',
      name: 'Image Ingestion Service',
      source_type: 'endpoint',
      modality: 'image',
      endpoint: process.env.IMAGE_INDEX_ENDPOINT,
      metadata: {
        capabilities: ['ingest'],
        description: 'Image captioning and indexing',
      },
    })
    console.log('✓ Registered Image Ingestion')
  }

  // Register Audio Services
  if (process.env.AUDIO_SEARCH_ENDPOINT) {
    await client.registerSource({
      source_id: 'audio-search-service',
      name: 'Audio Search Service',
      source_type: 'endpoint',
      modality: 'audio',
      endpoint: process.env.AUDIO_SEARCH_ENDPOINT,
      metadata: {
        capabilities: ['search'],
        description: 'Audio transcript and metadata search',
      },
    })
    console.log('✓ Registered Audio Search')
  }

  if (process.env.AUDIO_INDEX_ENDPOINT) {
    await client.registerSource({
      source_id: 'audio-index-service',
      name: 'Audio Ingestion Service',
      source_type: 'endpoint',
      modality: 'audio',
      endpoint: process.env.AUDIO_INDEX_ENDPOINT,
      metadata: {
        capabilities: ['ingest'],
        description: 'Audio transcription and indexing',
      },
    })
    console.log('✓ Registered Audio Ingestion')
  }

  // Register Video Services
  if (process.env.VIDEO_SEARCH_ENDPOINT) {
    await client.registerSource({
      source_id: 'video-search-service',
      name: 'Video Search Service',
      source_type: 'endpoint',
      modality: 'video',
      endpoint: process.env.VIDEO_SEARCH_ENDPOINT,
      metadata: {
        capabilities: ['search'],
        description: 'Video metadata and transcript search',
      },
    })
    console.log('✓ Registered Video Search')
  }

  if (process.env.VIDEO_PROCESS_ENDPOINT) {
    await client.registerSource({
      source_id: 'video-process-service',
      name: 'Video Processing Service',
      source_type: 'endpoint',
      modality: 'video',
      endpoint: process.env.VIDEO_PROCESS_ENDPOINT,
      metadata: {
        capabilities: ['ingest'],
        description: 'Video processing and multi-modal extraction',
      },
    })
    console.log('✓ Registered Video Processing')
  }

  console.log('Data source auto-registration completed')
}

// Singleton instance
export const dataCatalog = new DataCatalogClient()
