import axios from 'axios'

type SearchDoc = {
  content: string
  source: string
  lastUpdated?: string
}

export type Context7Result = {
  contextText: string
  sources: string[]
  items: SearchDoc[]
}

const AZURE_SEARCH_ENDPOINT = process.env.AZURE_SEARCH_ENDPOINT || ''
const AZURE_SEARCH_KEY = process.env.AZURE_SEARCH_KEY || ''
const AZURE_SEARCH_INDEX = process.env.AZURE_SEARCH_INDEX || ''

const IMAGE_SEARCH_ENDPOINT = process.env.IMAGE_SEARCH_ENDPOINT || ''
const VIDEO_SEARCH_ENDPOINT = process.env.VIDEO_SEARCH_ENDPOINT || ''
const DOC_SEARCH_ENDPOINT = process.env.DOC_SEARCH_ENDPOINT || ''
const AUDIO_SEARCH_ENDPOINT = process.env.AUDIO_SEARCH_ENDPOINT || ''

const MAX_RESULTS = Number(process.env.CONTEXT7_MAX_RESULTS || 6)

// Lightweight normalization for joining results from multiple sources
const normalizeText = (t: string) => t.replace(/\s+/g, ' ').trim()

const dedupeByContent = (items: SearchDoc[]) => {
  const seen = new Set<string>()
  const out: SearchDoc[] = []
  for (const it of items) {
    const key = normalizeText((it.content || '').slice(0, 280))
    if (!seen.has(key)) {
      seen.add(key)
      out.push(it)
    }
  }
  return out
}

// Simple recency + length heuristic scoring
const score = (it: SearchDoc) => {
  const len = Math.min(1000, (it.content || '').length)
  const recency = it.lastUpdated ? Date.parse(it.lastUpdated) / 1e12 : 0 // ~[0, ~now/1e12]
  return len / 1000 + recency
}

// Azure Cognitive Search retrieval, ordered by relevance then recency
export const azureSearch = async (query: string): Promise<SearchDoc[]> => {
  if (!AZURE_SEARCH_ENDPOINT || !AZURE_SEARCH_KEY || !AZURE_SEARCH_INDEX) return []
  const url = `${AZURE_SEARCH_ENDPOINT}/indexes/${AZURE_SEARCH_INDEX}/docs/search?api-version=2024-07-01`

  const body: any = {
    search: query,
    top: MAX_RESULTS,
  }

  const { data } = await axios.post(url, body, {
    headers: { 'Content-Type': 'application/json', 'api-key': AZURE_SEARCH_KEY },
  })

  const results: SearchDoc[] = (data.value || []).map((item: any) => ({
    content: item.content || item.text || JSON.stringify(item).slice(0, 1000),
    source: item.source || item.metadata_storage_name || item.id || 'unknown',
    lastUpdated: item.lastUpdated || item['@lastUpdated'] || item.modified || item.timestamp,
  }))

  return results
}

// Optional: call modality-specific microservices if provided
const callOptionalService = async (endpoint: string, query: string, mapper: (d: any) => SearchDoc[]): Promise<SearchDoc[]> => {
  if (!endpoint) return []
  try {
    const { data } = await axios.post(endpoint, { query })
    return mapper(data)
  } catch {
    return []
  }
}

const mapGenericArray = (data: any): SearchDoc[] => {
  if (Array.isArray(data)) {
    return data.slice(0, MAX_RESULTS).map((d: any) => ({
      content: d.content || d.text || String(d).slice(0, 1000),
      source: d.source || d.url || d.id || 'unknown',
      lastUpdated: d.lastUpdated || d.modified,
    }))
  }
  return []
}

export const context7 = async (query: string): Promise<Context7Result> => {
  // 1) Gather from multiple sources
  const [textDocs, docDocs, imageDocs, audioDocs, videoDocs] = await Promise.all([
    azureSearch(query),
    callOptionalService(DOC_SEARCH_ENDPOINT, query, mapGenericArray),
    callOptionalService(IMAGE_SEARCH_ENDPOINT, query, mapGenericArray),
    callOptionalService(AUDIO_SEARCH_ENDPOINT, query, mapGenericArray),
    callOptionalService(VIDEO_SEARCH_ENDPOINT, query, mapGenericArray),
  ])

  // 2) Merge and dedupe
  const merged = dedupeByContent([...textDocs, ...docDocs, ...imageDocs, ...audioDocs, ...videoDocs])

  // 3) Sort by simple score favoring relevance length + recency
  const ranked = merged.sort((a, b) => score(b) - score(a)).slice(0, MAX_RESULTS)

  // 4) Build compact context with source anchors
  const contextText = ranked.map((r, i) => `[S${i + 1}] ${r.content}`).join('\n\n')
  const sources = ranked.map((r) => r.source)

  return { contextText, sources, items: ranked }
}

export const getLatestDocs = async (query: string): Promise<Context7Result> => {
  // Emphasize recency by appending a recency hint and relying on search ordering
  const recencyQuery = `${query} sort by latest updates`
  const primary = await azureSearch(recencyQuery)
  const ranked = primary.sort((a, b) => score(b) - score(a)).slice(0, MAX_RESULTS)
  const contextText = ranked.map((r, i) => `[S${i + 1}] ${r.content}`).join('\n\n')
  const sources = ranked.map((r) => r.source)
  return { contextText, sources, items: ranked }
}

