PORT=8787

# Azure OpenAI
AZURE_OPENAI_API_KEY=
AZURE_OPENAI_RESOURCE=
AZURE_OPENAI_DEPLOYMENT=gpt-4o-mini

# Azure AI Search
AZURE_SEARCH_ENDPOINT=
AZURE_SEARCH_KEY=
AZURE_SEARCH_INDEX=

# Data Catalog Service
DATA_CATALOG_ENDPOINT=http://localhost:8081

# Cross-Modal Search Configuration
CONTEXT_MAX_RESULTS=6
ENABLE_CROSS_MODAL_SEARCH=true

# Optional modality services (HTTP endpoints that accept { query })
# These will be auto-registered with Data Catalog on startup
DOC_SEARCH_ENDPOINT=
IMAGE_SEARCH_ENDPOINT=
AUDIO_SEARCH_ENDPOINT=
VIDEO_SEARCH_ENDPOINT=
DOC_INDEX_ENDPOINT=
IMAGE_INDEX_ENDPOINT=
AUDIO_INDEX_ENDPOINT=
VIDEO_PROCESS_ENDPOINT=

# Optional MCP HTTP bridge
MCP_HTTP_URL=

# Azure Storage for uploads
AZURE_STORAGE_CONNECTION_STRING=
UPLOAD_CONTAINER=uploads
