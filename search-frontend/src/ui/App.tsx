import React, { useEffect, useRef, useState } from "react";

type ChatMessage = {
  id: string;
  role: "user" | "assistant" | "system";
  content: string;
  imageUrl?: string;
};

export const App: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [preferLatest, setPreferLatest] = useState(false);
  const endRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    endRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const onSend = async () => {
    if (!input.trim() && !file) return;
    const previewUrl = file && file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined;
    const userMsg: ChatMessage = {
      id: crypto.randomUUID(),
      role: "user",
      content: input,
      imageUrl: previewUrl,
    };
    setMessages((prev) => [...prev, userMsg]);
    setInput("");
    if (previewUrl) {
      setTimeout(() => URL.revokeObjectURL(previewUrl), 30_000);
    }
    setFile(null);
    setLoading(true);

    const form = new FormData();
    const enriched = preferLatest && !/latest|newest|recent/i.test(userMsg.content)
      ? `${userMsg.content} (latest)`
      : userMsg.content;
    form.append("message", enriched);
    if (file) form.append("file", file);

    const res = await fetch("/api/chat", { method: "POST", body: form });

    if (!res.ok || !res.body) {
      setMessages((prev) => [
        ...prev,
        {
          id: crypto.randomUUID(),
          role: "assistant",
          content: "Error from server",
        },
      ]);
      setLoading(false);
      return;
    }

    const reader = res.body.getReader();
    const decoder = new TextDecoder();
    let assistantText = "";
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      assistantText += decoder.decode(value, { stream: true });
      setMessages((prev) => {
        const last = prev[prev.length - 1];
        if (last && last.role === "assistant") {
          return [...prev.slice(0, -1), { ...last, content: assistantText }];
        }
        return [
          ...prev,
          {
            id: crypto.randomUUID(),
            role: "assistant",
            content: assistantText,
          },
        ];
      });
    }
    setLoading(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onSend();
    }
  };

  return (
    <div
      style={{
        minHeight: "100vh",
        background: "linear-gradient(135deg, #266ADC 0%, #1e56b8 100%)",
        display: "flex",
        flexDirection: "column",
        fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
      }}
    >
      {/* Header */}
      <div
        style={{
          background: "rgba(255, 255, 255, 0.98)",
          backdropFilter: "blur(10px)",
          borderBottom: "1px solid rgba(0,0,0,0.08)",
          padding: "16px 24px",
          boxShadow: "0 2px 8px rgba(0,0,0,0.05)",
        }}
      >
        <div style={{ maxWidth: 1200, margin: "0 auto", display: "flex", alignItems: "center", gap: 12 }}>
          <div
            style={{
              width: 40,
              height: 40,
              borderRadius: 10,
              background: "linear-gradient(135deg, #266ADC 0%, #1e56b8 100%)",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              fontSize: 20,
              fontWeight: "bold",
              color: "white",
            }}
          >
            🔍
          </div>
          <div>
            <h1 style={{ margin: 0, fontSize: 24, fontWeight: 700, color: "#1a202c" }}>
              unnanu Enterprise AI Search
            </h1>
            <p style={{ margin: 0, fontSize: 13, color: "#718096" }}>
              Multi-modal RAG with unified query processing
            </p>
          </div>
        </div>
      </div>

      {/* Chat Container */}
      <div
        style={{
          flex: 1,
          maxWidth: 1200,
          width: "100%",
          margin: "0 auto",
          padding: "24px",
          display: "flex",
          flexDirection: "column",
          overflow: "hidden",
        }}
      >
        {/* Messages */}
        <div
          style={{
            flex: 1,
            overflowY: "auto",
            marginBottom: 20,
            padding: "20px 0",
            display: "flex",
            flexDirection: "column",
            gap: 16,
          }}
        >
          {messages.length === 0 && (
            <div
              style={{
                textAlign: "center",
                color: "rgba(255,255,255,0.9)",
                padding: "60px 20px",
              }}
            >
              <h2 style={{ fontSize: 32, marginBottom: 12, fontWeight: 600 }}>
                Welcome to unnanu Enterprise AI Search
              </h2>
              <p style={{ fontSize: 16, opacity: 0.8 }}>
                Ask questions about your enterprise data across text, documents, images, audio, and video
              </p>
            </div>
          )}
          {messages.map((m, idx) => (
            <div
              key={m.id}
              style={{
                display: "flex",
                justifyContent: m.role === "user" ? "flex-end" : "flex-start",
                animation: "slideIn 0.3s ease-out",
              }}
            >
              <div
                style={{
                  maxWidth: "75%",
                  padding: 16,
                  borderRadius: 16,
                  background: m.role === "user"
                    ? "linear-gradient(135deg, #266ADC 0%, #1e56b8 100%)"
                    : "rgba(255, 255, 255, 0.95)",
                  color: m.role === "user" ? "white" : "#1a202c",
                  boxShadow: m.role === "user"
                    ? "0 4px 12px rgba(38, 106, 220, 0.4)"
                    : "0 2px 8px rgba(0,0,0,0.1)",
                  backdropFilter: m.role === "assistant" ? "blur(10px)" : "none",
                }}
              >
                <div
                  style={{
                    fontSize: 11,
                    fontWeight: 600,
                    textTransform: "uppercase",
                    letterSpacing: "0.5px",
                    marginBottom: 8,
                    opacity: 0.7,
                  }}
                >
                  {m.role === "user" ? "You" : "Assistant"}
                </div>
                {m.imageUrl && (
                  <img
                    src={m.imageUrl}
                    alt="upload"
                    style={{
                      maxWidth: "100%",
                      borderRadius: 12,
                      marginBottom: 12,
                      display: "block",
                      boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
                    }}
                  />
                )}
                <div
                  style={{
                    whiteSpace: "pre-wrap",
                    lineHeight: 1.6,
                    fontSize: 15,
                  }}
                >
                  {m.content}
                </div>
              </div>
            </div>
          ))}
          {loading && (
            <div
              style={{
                display: "flex",
                justifyContent: "flex-start",
              }}
            >
              <div
                style={{
                  padding: 16,
                  borderRadius: 16,
                  background: "rgba(255, 255, 255, 0.95)",
                  boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                }}
              >
                <div style={{ display: "flex", gap: 6, alignItems: "center" }}>
                  <div className="dot-pulse" style={{ display: "flex", gap: 4 }}>
                    {[0, 1, 2].map((i) => (
                      <div
                        key={i}
                        style={{
                          width: 8,
                          height: 8,
                          borderRadius: "50%",
                          background: "#266ADC",
                          animation: `pulse 1.4s infinite ease-in-out`,
                          animationDelay: `${i * 0.16}s`,
                        }}
                      />
                    ))}
                  </div>
                  <span style={{ fontSize: 13, color: "#718096", marginLeft: 8 }}>
                    Thinking...
                  </span>
                </div>
              </div>
            </div>
          )}
          <div ref={endRef} />
        </div>

        {/* Input Area */}
        <div
          style={{
            background: "rgba(255, 255, 255, 0.98)",
            backdropFilter: "blur(10px)",
            borderRadius: 20,
            padding: 16,
            boxShadow: "0 8px 32px rgba(0,0,0,0.12)",
            border: "1px solid rgba(255,255,255,0.3)",
          }}
        >
          {file && (
            <div
              style={{
                marginBottom: 12,
                padding: 12,
                background: "rgba(38, 106, 220, 0.1)",
                borderRadius: 12,
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
                <span style={{ fontSize: 20 }}>📎</span>
                <span style={{ fontSize: 14, color: "#4a5568" }}>{file.name}</span>
              </div>
              <button
                onClick={() => setFile(null)}
                style={{
                  background: "transparent",
                  border: "none",
                  cursor: "pointer",
                  fontSize: 18,
                  color: "#e53e3e",
                }}
              >
                ×
              </button>
            </div>
          )}
          <div style={{ display: "flex", gap: 12, alignItems: "flex-end" }}>
            <div style={{ flex: 1 }}>
              <textarea
                placeholder="Ask about your enterprise data..."
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyPress={handleKeyPress}
                rows={1}
                style={{
                  width: "100%",
                  padding: "12px 16px",
                  borderRadius: 12,
                  border: "2px solid transparent",
                  outline: "none",
                  fontSize: 15,
                  fontFamily: "inherit",
                  resize: "none",
                  background: "#f7fafc",
                  transition: "all 0.2s",
                  boxSizing: "border-box",
                }}
                onFocus={(e) => {
                  e.target.style.borderColor = "#266ADC";
                  e.target.style.background = "white";
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = "transparent";
                  e.target.style.background = "#f7fafc";
                }}
              />
              <div style={{ display: "flex", gap: 12, marginTop: 12, alignItems: "center" }}>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="*/*"
                  onChange={(e) => setFile(e.target.files?.[0] || null)}
                  style={{ display: "none" }}
                />
                <button
                  onClick={() => fileInputRef.current?.click()}
                  style={{
                    padding: "8px 16px",
                    borderRadius: 10,
                    border: "2px solid #e2e8f0",
                    background: "white",
                    cursor: "pointer",
                    fontSize: 13,
                    fontWeight: 600,
                    color: "#4a5568",
                    display: "flex",
                    alignItems: "center",
                    gap: 6,
                    transition: "all 0.2s",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = "#266ADC";
                    e.currentTarget.style.color = "#266ADC";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = "#e2e8f0";
                    e.currentTarget.style.color = "#4a5568";
                  }}
                >
                  📎 Attach
                </button>
                <label
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: 8,
                    cursor: "pointer",
                    fontSize: 13,
                    color: "#4a5568",
                    userSelect: "none",
                  }}
                >
                  <input
                    type="checkbox"
                    checked={preferLatest}
                    onChange={(e) => setPreferLatest(e.target.checked)}
                    style={{
                      width: 16,
                      height: 16,
                      cursor: "pointer",
                    }}
                  />
                  Prioritize latest
                </label>
              </div>
            </div>
            <button
              onClick={onSend}
              disabled={loading || (!input.trim() && !file)}
              style={{
                padding: "14px 28px",
                borderRadius: 12,
                border: "none",
                background: loading || (!input.trim() && !file)
                  ? "#cbd5e0"
                  : "linear-gradient(135deg, #266ADC 0%, #1e56b8 100%)",
                color: "white",
                cursor: loading || (!input.trim() && !file) ? "not-allowed" : "pointer",
                fontSize: 15,
                fontWeight: 600,
                transition: "all 0.2s",
                boxShadow: loading || (!input.trim() && !file)
                  ? "none"
                  : "0 4px 12px rgba(38, 106, 220, 0.4)",
              }}
              onMouseEnter={(e) => {
                if (!loading && (input.trim() || file)) {
                  e.currentTarget.style.transform = "translateY(-2px)";
                  e.currentTarget.style.boxShadow = "0 6px 16px rgba(38, 106, 220, 0.5)";
                }
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = "translateY(0)";
                e.currentTarget.style.boxShadow = loading || (!input.trim() && !file)
                  ? "none"
                  : "0 4px 12px rgba(38, 106, 220, 0.4)";
              }}
            >
              {loading ? "⏳ Sending..." : "Send ➤"}
            </button>
          </div>
        </div>
      </div>

      <style>
        {`
          @keyframes slideIn {
            from {
              opacity: 0;
              transform: translateY(10px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
          @keyframes pulse {
            0%, 80%, 100% {
              opacity: 0.3;
              transform: scale(0.8);
            }
            40% {
              opacity: 1;
              transform: scale(1);
            }
          }
        `}
      </style>
    </div>
  );
};
