# MCP HTTP Bridge (optional)

This is an optional integration path. The Node orchestrator exposes an `mcpCall` tool that forwards to an HTTP URL specified by `MCP_HTTP_URL`.

Implement a tiny service exposing:

- `POST /call-tool` with body `{ tool: string, args: object }`
- Returns `{ ...toolResult }` or `{ error }`

You can wire this service to any MCP server (e.g., using `@modelcontextprotocol/sdk`) and translate HTTP requests into MCP tool invocations.

