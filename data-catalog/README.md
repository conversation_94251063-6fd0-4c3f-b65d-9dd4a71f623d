# Data Catalog Service

Centralized metadata catalog for all data sources in the Enterprise AI Search system.

## Features

- Register and discover data sources across all modalities
- Track schemas, capabilities, and health status
- Query sources by type, modality, and capabilities
- SQLite-based storage with async support
- REST API for all catalog operations

## Setup

```bash
cd data-catalog
python -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
```

## Run

```bash
python app.py
# Or with uvicorn
uvicorn app:app --host 0.0.0.0 --port 8081
```

## API Endpoints

### POST /register-source
Register or update a data source

```json
{
  "source_id": "azure-search-main",
  "name": "Azure AI Search - Main Index",
  "source_type": "azure-search",
  "modality": "text",
  "endpoint": "https://search.azure.com/...",
  "metadata": {
    "capabilities": ["search", "vectorize"],
    "record_count": 10000,
    "description": "Primary text search index"
  }
}
```

### POST /query-metadata
Query sources by filters

```json
{
  "modality": "document",
  "capabilities": ["search"],
  "health_status": "healthy"
}
```

### GET /list-sources
List all registered sources

### GET /get-source/{source_id}
Get specific source details

### POST /update-health
Update health status

```json
{
  "source_id": "image-search-service",
  "health_status": "degraded",
  "error_message": "High latency detected"
}
```

### DELETE /delete-source/{source_id}
Remove a source from catalog

## Environment Variables

- `DATABASE_URL` - Database connection string (default: sqlite+aiosqlite:///./data_catalog.db)
- `HOST` - Server host (default: 0.0.0.0)
- `PORT` - Server port (default: 8081)
