import os
from datetime import datetime
from typing import List, Optional
from fastapi import FastAP<PERSON>, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from database import init_db, get_session, DataSourceDB
from models import (
    DataSource,
    RegisterSourceRequest,
    QueryMetadataRequest,
    HealthCheckUpdate,
    HealthStatus,
    SourceMetadata,
)

app = FastAPI(title="Data Catalog Service")


@app.on_event("startup")
async def startup_event():
    await init_db()


@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "data-catalog"}


@app.post("/register-source", response_model=DataSource)
async def register_source(
    request: RegisterSourceRequest,
    session: AsyncSession = Depends(get_session),
):
    """Register a new data source in the catalog"""
    # Check if source already exists
    result = await session.execute(
        select(DataSourceDB).where(DataSourceDB.source_id == request.source_id)
    )
    existing = result.scalar_one_or_none()

    now = datetime.utcnow()

    if existing:
        # Update existing source
        existing.name = request.name
        existing.source_type = request.source_type
        existing.modality = request.modality
        existing.endpoint = request.endpoint
        existing.source_metadata = request.metadata.model_dump()
        existing.updated_at = now
        await session.commit()
        await session.refresh(existing)
        db_source = existing
    else:
        # Create new source
        db_source = DataSourceDB(
            source_id=request.source_id,
            name=request.name,
            source_type=request.source_type,
            modality=request.modality,
            endpoint=request.endpoint,
            source_metadata=request.metadata.model_dump(),
            health_status=HealthStatus.HEALTHY,
            created_at=now,
            updated_at=now,
        )
        session.add(db_source)
        await session.commit()
        await session.refresh(db_source)

    return DataSource(
        source_id=db_source.source_id,
        name=db_source.name,
        source_type=db_source.source_type,
        modality=db_source.modality,
        endpoint=db_source.endpoint,
        metadata=SourceMetadata(**db_source.source_metadata),
        health_status=db_source.health_status,
        created_at=db_source.created_at,
        updated_at=db_source.updated_at,
    )


@app.post("/query-metadata", response_model=List[DataSource])
async def query_metadata(
    request: QueryMetadataRequest,
    session: AsyncSession = Depends(get_session),
):
    """Query data sources by filters"""
    query = select(DataSourceDB)
    conditions = []

    if request.modality:
        conditions.append(DataSourceDB.modality == request.modality)
    if request.source_type:
        conditions.append(DataSourceDB.source_type == request.source_type)
    if request.health_status:
        conditions.append(DataSourceDB.health_status == request.health_status)

    if conditions:
        query = query.where(and_(*conditions))

    result = await session.execute(query)
    db_sources = result.scalars().all()

    sources = []
    for db_source in db_sources:
        # Filter by capabilities if specified
        if request.capabilities:
            source_caps = db_source.source_metadata.get("capabilities", [])
            if not all(cap in source_caps for cap in request.capabilities):
                continue

        sources.append(
            DataSource(
                source_id=db_source.source_id,
                name=db_source.name,
                source_type=db_source.source_type,
                modality=db_source.modality,
                endpoint=db_source.endpoint,
                metadata=SourceMetadata(**db_source.source_metadata),
                health_status=db_source.health_status,
                created_at=db_source.created_at,
                updated_at=db_source.updated_at,
            )
        )

    return sources


@app.get("/list-sources", response_model=List[DataSource])
async def list_sources(
    session: AsyncSession = Depends(get_session),
):
    """List all registered data sources"""
    result = await session.execute(select(DataSourceDB))
    db_sources = result.scalars().all()

    return [
        DataSource(
            source_id=db_source.source_id,
            name=db_source.name,
            source_type=db_source.source_type,
            modality=db_source.modality,
            endpoint=db_source.endpoint,
            metadata=SourceMetadata(**db_source.source_metadata),
            health_status=db_source.health_status,
            created_at=db_source.created_at,
            updated_at=db_source.updated_at,
        )
        for db_source in db_sources
    ]


@app.get("/get-source/{source_id}", response_model=DataSource)
async def get_source(
    source_id: str,
    session: AsyncSession = Depends(get_session),
):
    """Get a specific data source by ID"""
    result = await session.execute(
        select(DataSourceDB).where(DataSourceDB.source_id == source_id)
    )
    db_source = result.scalar_one_or_none()

    if not db_source:
        raise HTTPException(status_code=404, detail="Data source not found")

    return DataSource(
        source_id=db_source.source_id,
        name=db_source.name,
        source_type=db_source.source_type,
        modality=db_source.modality,
        endpoint=db_source.endpoint,
        metadata=SourceMetadata(**db_source.source_metadata),
        health_status=db_source.health_status,
        created_at=db_source.created_at,
        updated_at=db_source.updated_at,
    )


@app.post("/update-health")
async def update_health(
    update: HealthCheckUpdate,
    session: AsyncSession = Depends(get_session),
):
    """Update health status of a data source"""
    result = await session.execute(
        select(DataSourceDB).where(DataSourceDB.source_id == update.source_id)
    )
    db_source = result.scalar_one_or_none()

    if not db_source:
        raise HTTPException(status_code=404, detail="Data source not found")

    db_source.health_status = update.health_status
    db_source.updated_at = datetime.utcnow()

    if update.error_message:
        source_metadata = db_source.source_metadata or {}
        source_metadata["last_error"] = update.error_message
        db_source.source_metadata = source_metadata

    await session.commit()

    return {"status": "updated", "source_id": update.source_id}


@app.delete("/delete-source/{source_id}")
async def delete_source(
    source_id: str,
    session: AsyncSession = Depends(get_session),
):
    """Delete a data source from the catalog"""
    result = await session.execute(
        select(DataSourceDB).where(DataSourceDB.source_id == source_id)
    )
    db_source = result.scalar_one_or_none()

    if not db_source:
        raise HTTPException(status_code=404, detail="Data source not found")

    await session.delete(db_source)
    await session.commit()

    return {"status": "deleted", "source_id": source_id}


def main():
    import uvicorn

    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8081"))
    uvicorn.run("app:app", host=host, port=port, reload=False)


if __name__ == "__main__":
    main()
