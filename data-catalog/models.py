from datetime import datetime
from typing import List, Optional
from enum import Enum
from pydantic import BaseModel


class SourceType(str, Enum):
    AZURE_SEARCH = "azure-search"
    BLOB = "blob"
    SQL = "sql"
    COSMOS = "cosmos"
    ENDPOINT = "endpoint"


class Modality(str, Enum):
    TEXT = "text"
    DOCUMENT = "document"
    IMAGE = "image"
    AUDIO = "audio"
    VIDEO = "video"
    TABLE = "table"


class HealthStatus(str, Enum):
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNAVAILABLE = "unavailable"


class SourceMetadata(BaseModel):
    schema_info: Optional[dict] = None
    last_indexed: Optional[datetime] = None
    record_count: Optional[int] = None
    capabilities: List[str] = []  # ['search', 'ingest', 'vectorize']
    description: Optional[str] = None


class DataSource(BaseModel):
    source_id: str
    name: str
    source_type: SourceType
    modality: Modality
    endpoint: str
    metadata: SourceMetadata
    health_status: HealthStatus = HealthStatus.HEALTHY
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class RegisterSourceRequest(BaseModel):
    source_id: str
    name: str
    source_type: SourceType
    modality: Modality
    endpoint: str
    metadata: SourceMetadata


class QueryMetadataRequest(BaseModel):
    modality: Optional[Modality] = None
    source_type: Optional[SourceType] = None
    capabilities: Optional[List[str]] = None
    health_status: Optional[HealthStatus] = None


class HealthCheckUpdate(BaseModel):
    source_id: str
    health_status: HealthStatus
    error_message: Optional[str] = None
