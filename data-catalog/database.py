from datetime import datetime
from typing import List, Optional
from sqlalchemy import Column, String, DateTime, Integer, JSON, Enum as SQLEnum, create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
import os

from models import SourceType, Modality, HealthStatus

Base = declarative_base()


class DataSourceDB(Base):
    __tablename__ = "data_sources"

    source_id = Column(String, primary_key=True, index=True)
    name = Column(String, nullable=False)
    source_type = Column(SQLEnum(SourceType), nullable=False)
    modality = Column(SQLEnum(Modality), nullable=False)
    endpoint = Column(String, nullable=False)
    source_metadata = Column(JSON, nullable=False)
    health_status = Column(SQLEnum(HealthStatus), default=HealthStatus.HEALTHY)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


# Database setup
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite+aiosqlite:///./data_catalog.db")

engine = create_async_engine(DATABASE_URL, echo=False)
async_session_maker = async_sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)


async def init_db():
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)


async def get_session() -> AsyncSession:
    async with async_session_maker() as session:
        yield session
