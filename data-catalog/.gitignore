# Python
__pycache__/
*.py[cod]
*$py.class
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
.venv/
venv/
ENV/
env/
.virtualenv/

# Testing
.pytest_cache/
.coverage
.tox/
htmlcov/

# Type checking
.mypy_cache/
.pytype/
.pyre/

# IDE
.vscode/
.idea/
*.swp
*.swo

# Environment variables
.env
.env.*
!.env.example

# Database
*.db
*.sqlite
*.sqlite3
instance/

# Logs
*.log
logs/

# OS
.DS_Store
Thumbs.db

# Project specific
data/
temp/
tmp/