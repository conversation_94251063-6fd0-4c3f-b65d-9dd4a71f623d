```mermaid
graph TB
    %% User Interface Layer
    UI[Streamlit Frontend<br/>Search Interface] --> ORCHESTRATOR

    %% Agent Orchestrator - Central Hub
    ORCHESTRATOR[Agent Orchestrator<br/>Decision Router] --> |Document Query| DOC_SERVICE
    ORCHESTRATOR --> |Image Query| IMG_SERVICE
    ORCHESTRATOR --> |Audio Query| AUDIO_SERVICE
    ORCHESTRATOR --> |Video Query| VIDEO_SERVICE
    ORCHESTRATOR --> |Multi-modal Query| MULTI_SERVICE

    %% Azure Storage Layer
    subgraph STORAGE [Azure Storage Layer]
        BLOB[Azure Blob Storage<br/>- Documents Container<br/>- Images Container<br/>- Audio Files Container<br/>- Video Files Container]
        SQLDB[(Azure SQL Database<br/>- ImageCatalog<br/>- ImageQueries<br/>- Audio_metadata<br/>- Video metadata)]
    end

    %% Document Search Service
    subgraph DOC_SERVICE [Document Search Service]
        DOCLING[Docling Preprocessor<br/>IBM Document Processing]
        BGE[BGE-M3 Embeddings<br/>Text Vectorization]
        PHI3[Phi-3 Mini 3.8B<br/>Local LLM Processing]

        DOCLING --> BGE
        BGE --> PHI3
    end

    %% Image Search Service
    subgraph IMG_SERVICE [Image Search Service]
        LLAMA_VISION[Llama-4-Scout-17B<br/>via Groq API<br/>Image Captioning]
        SENT_TRANSFORMER[SentenceTransformer<br/>all-MiniLM-L6-v2<br/>Semantic Similarity]

        LLAMA_VISION --> SENT_TRANSFORMER
    end

    %% Audio Search Service
    subgraph AUDIO_SERVICE [Audio Search Service]
        WHISPER[Whisper small.en<br/>Transcription]
        EMOTION[Emotion Classifier<br/>j-hartmann/emotion-english-distilroberta-base]
        BART[BART Summarization<br/>facebook/bart-large-cnn]
        PYANNOTE[Speaker Diarization<br/>pyannote/speaker-diarization]
        PANNS[PANNs Audio Tagging<br/>Scene Classification]

        WHISPER --> EMOTION
        WHISPER --> BART
        WHISPER --> PYANNOTE
        WHISPER --> PANNS
    end

    %% Video Search Service
    subgraph VIDEO_SERVICE [Video Search Service]
        FFMPEG[FFmpeg Audio Extraction<br/>WAV Processing]
        VIDEOLLAMA[VideoLLaMA3-2B<br/>Object Description<br/>Scene Captioning]
        PYSCENE[PySceneDetect<br/>Scene Segmentation]
        V_WHISPER[Whisper Transcription<br/>Subtitle Generation]
        V_PANNS[PANNs Audio Tags<br/>Audio Classification]
        V_PYANNOTE[Speaker Diarization<br/>Voice Identification]
        MINI_LLM[AI-MiniLLM<br/>Embedding Generation]

        FFMPEG --> V_WHISPER
        FFMPEG --> V_PANNS
        FFMPEG --> V_PYANNOTE
        VIDEOLLAMA --> PYSCENE
        VIDEOLLAMA --> MINI_LLM
    end

    %% Multi-modal Service (uses audio for video)
    subgraph MULTI_SERVICE [Multi-modal Integration]
        CROSS_MODAL[Cross-Modal Search<br/>Unified Query Processing]
        AUDIO_VIDEO_LINK[Audio-Video Link<br/>Shared Audio Processing]
    end

    %% External Services
    subgraph EXTERNAL [External Services]
        GROQ[Groq API<br/>LLama Vision Processing]
        AZURE_AI[Azure Cognitive Services<br/>Additional AI Processing]
        HF_TOKEN[Hugging Face<br/>Model Access Token]
    end

    %% Storage Connections
    DOC_SERVICE --> BLOB
    IMG_SERVICE --> BLOB
    AUDIO_SERVICE --> BLOB
    VIDEO_SERVICE --> BLOB

    IMG_SERVICE --> SQLDB
    AUDIO_SERVICE --> SQLDB
    VIDEO_SERVICE --> SQLDB

    %% External Connections
    IMG_SERVICE --> GROQ
    AUDIO_SERVICE --> HF_TOKEN
    VIDEO_SERVICE --> HF_TOKEN

    %% Multi-modal connections
    MULTI_SERVICE --> DOC_SERVICE
    MULTI_SERVICE --> IMG_SERVICE
    MULTI_SERVICE --> AUDIO_SERVICE
    MULTI_SERVICE --> VIDEO_SERVICE
    AUDIO_VIDEO_LINK --> AUDIO_SERVICE

    %% Chat Interface
    subgraph CHAT [Chat Interface]
        CHAT_ENGINE[Azure SQL Chat Engine<br/>Query Processing & Reasoning]
        GROQ_LLM[Groq LLM<br/>Response Generation]
        HTML_RENDER[HTML Template Renderer<br/>Result Display]
    end

    ORCHESTRATOR --> CHAT_ENGINE
    CHAT_ENGINE --> GROQ_LLM
    GROQ_LLM --> HTML_RENDER
    HTML_RENDER --> UI

    %% Styling
    classDef storage fill:#e1f5fe
    classDef service fill:#f3e5f5
    classDef ai fill:#fff3e0
    classDef external fill:#e8f5e8
    classDef orchestrator fill:#ffebee

    class STORAGE,BLOB,SQLDB storage
    class DOC_SERVICE,IMG_SERVICE,AUDIO_SERVICE,VIDEO_SERVICE,MULTI_SERVICE service
    class DOCLING,BGE,PHI3,LLAMA_VISION,WHISPER,EMOTION,BART,VIDEOLLAMA ai
    class EXTERNAL,GROQ,AZURE_AI,HF_TOKEN external
    class ORCHESTRATOR orchestrator
```
