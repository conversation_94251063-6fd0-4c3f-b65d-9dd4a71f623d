# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

Enterprise AI Search with Unified Query Processor — A multi-modal RAG system using Azure OpenAI with intelligent cross-modal routing, centralized Data Catalog, and optional Azure AI Foundry integration.

**Key Architecture:**
- **Unified Query Processor**: Combines intent analysis, cross-modal routing, and tool orchestration
- **Cross-Modal Search**: Replaces context7 with Data Catalog-aware retrieval
- **Data Catalog**: Centralized registry for all data sources (text, document, image, audio, video, table)

## Common Commands

### Development (Node/TypeScript - search-frontend)

```bash
cd search-frontend
pnpm install                    # Install dependencies
pnpm dev                        # Run dev server (web: 5173, API: 8787)
pnpm build                      # Build frontend + server
pnpm preview                    # Run production build
```

### Python Services

```bash
# Data Catalog Service (Required for full functionality)
cd data-catalog
python -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
python app.py                   # Starts on port 8081

# Orchestrator (Azure AI Foundry - Optional)
cd orchestrator
python -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
python -m orchestrator.app      # Starts FastAPI on port 8080
```

## Architecture

### Core Components

**1. data-catalog/** — Centralized metadata catalog (FastAPI + SQLite)
- **Purpose**: Single source of truth for all data sources
- **Features**: Source registration, health monitoring, capability discovery
- **API**: REST endpoints for register, query, list, update health
- **Database**: SQLite (async) with source metadata, types, modalities
- Auto-registered by Node server on startup

**2. search-frontend/** — Main Node.js application
- **Frontend**: React + Vite (TypeScript)
  - `src/ui/App.tsx` — Simple chat UI with file upload, streaming responses
- **Backend**: Express server (TypeScript)
  - `server/index.ts` — Main API `/api/chat` with auto-registration
  - `server/unified-query-processor.ts` — **NEW**: Unified intent + routing + orchestration
  - `server/cross-modal-search.ts` — **NEW**: Replaces context7, Data Catalog integration
  - `server/data-catalog-client.ts` — **NEW**: Client for catalog service
  - `server/orchestrator.ts` — Legacy agentic orchestrator (now replaced by unified processor)
  - `server/context7.ts` — Legacy multi-source retrieval (now replaced by cross-modal-search)
  - `server/rag.ts` — Basic RAG retrieval (older fallback)
  - `server/blob.ts` — Azure Blob upload for attachments

**3. orchestrator/** — Optional Python FastAPI service
- Uses `azure-ai-projects` package for Azure AI Foundry agents
- Exposes `/orchestrate` endpoint
- Alternative to Node orchestrator for production Azure deployments

**4. Modality Services** (text/doc/audio/image/video directories)
- Independent microservices for specialized data types
- Each auto-registers with Data Catalog on first query
- DocGPT: Docling-based document search (Streamlit app)
- TableGPT: Vanna + DuckDB for SQL generation over tabular data
- Audio/Image/Video: Processing pipelines with transcription, captioning, embeddings

### Request Flow (Updated Architecture)

1. **Startup**: Node server auto-registers all configured data sources with Data Catalog
2. **User Query**: Message (+ optional file) submitted via React UI
3. **API Endpoint** (`server/index.ts`):
   - Uploads file to Azure Blob if present
   - Calls `crossModalSearch()` or `getLatestDocuments()` for initial priming
4. **Cross-Modal Search** (`cross-modal-search.ts`):
   - Queries Data Catalog for available healthy sources
   - Analyzes query intent to determine target modalities
   - Executes parallel searches across relevant sources
   - Deduplicates and scores with modality boost
   - Returns unified context with `[MODALITY]` tags
5. **Unified Query Processor** (`unified-query-processor.ts`):
   - Receives query + primed context + optional image/blob
   - Provides tools: `retrieveContext` (cross-modal), `fetchBlobText`, `mcpCall`, modality tools
   - LLM analyzes intent and calls appropriate tools
   - Streams response using Vercel AI SDK
6. **Response**: Streamed back to UI with source citations and modality tags

### Tool Use Pattern (Unified Processor)

The unified processor exposes tools to the LLM:
- **retrieveContext**: Intelligent cross-modal search with Data Catalog
  - Auto-detects modalities from query (or accepts explicit list)
  - Supports `latest=true` for recency priority
  - Returns context + sources + modalities used
- **fetchBlobText**: Fetch text from Azure Blob URL
- **mcpCall**: Bridge to MCP tools via HTTP (if `MCP_HTTP_URL` set)
- **Modality tools** (dynamic based on env):
  - `docIngest`, `docSearch` — Document processing and search
  - `imageIngest`, `imageSearch` — Image captioning and search
  - `audioIngest`, `audioSearch` — Audio transcription and search
  - `videoProcess`, `videoSearch` — Video multi-modal processing

Tools are implemented in `server/unified-query-processor.ts` using Vercel AI SDK's `tool()` helper with Zod schemas.

### Cross-Modal Search (Replaces Context7)

`crossModalSearch()` in `server/cross-modal-search.ts`:

**Features:**
1. **Data Catalog Integration**: Discovers available sources dynamically
2. **Intent Analysis**: Auto-detects target modalities from query keywords
3. **Parallel Execution**: Searches multiple sources concurrently
4. **Unified Scoring**: `score = length/1000 + recency + modalityBoost`
5. **Modality Tags**: Results tagged as `[TEXT]`, `[DOCUMENT]`, `[IMAGE]`, etc.

**Fallback Behavior:**
- If Data Catalog unavailable, falls back to env vars (legacy compatibility)
- Graceful degradation when individual services are down

**Query Intent Detection:**
- Keywords trigger modality routing (e.g., "video" → video modality)
- "all" or "everything" searches all modalities
- Default: text + any keyword-matched modalities

**Scoring Algorithm:**
```
score = content_length/1000 + timestamp/1e12 + modality_boost
modality_boost = 0.5 if modality matches intent, else 0
```

## Configuration

Create `search-frontend/.env` from `search-frontend/.env.example`:

**Required:**
- `AZURE_OPENAI_API_KEY`, `AZURE_OPENAI_RESOURCE`, `AZURE_OPENAI_DEPLOYMENT`
- `AZURE_SEARCH_ENDPOINT`, `AZURE_SEARCH_KEY`, `AZURE_SEARCH_INDEX`
- `AZURE_STORAGE_CONNECTION_STRING`, `UPLOAD_CONTAINER`

**New (Data Catalog & Cross-Modal):**
- `DATA_CATALOG_ENDPOINT` — Data Catalog service URL (default: http://localhost:8081)
- `CONTEXT_MAX_RESULTS` — Max items returned by cross-modal search (default 6)
- `ENABLE_CROSS_MODAL_SEARCH` — Enable new architecture (default true)

**Optional (Modality Services):**
- `DOC_SEARCH_ENDPOINT`, `IMAGE_SEARCH_ENDPOINT`, `AUDIO_SEARCH_ENDPOINT`, `VIDEO_SEARCH_ENDPOINT`
- `DOC_INDEX_ENDPOINT`, `IMAGE_INDEX_ENDPOINT`, `AUDIO_INDEX_ENDPOINT`, `VIDEO_PROCESS_ENDPOINT`
- These endpoints are auto-registered with Data Catalog on startup

**Optional:**
- `MCP_HTTP_URL` — HTTP bridge for MCP tools
- `PORT` — API server port (default 8787)

## Key Files

**New Architecture:**
- `data-catalog/app.py` — Data Catalog service (FastAPI)
- `data-catalog/models.py` — Data models for sources and metadata
- `data-catalog/database.py` — SQLite async database layer
- `search-frontend/server/unified-query-processor.ts` — **Main processor** (intent + routing + orchestration)
- `search-frontend/server/cross-modal-search.ts` — **Replaces context7** with Data Catalog integration
- `search-frontend/server/data-catalog-client.ts` — Node.js client for catalog service
- `search-frontend/server/index.ts` — Express API with auto-registration

**Legacy (Still Present):**
- `search-frontend/server/orchestrator.ts` — Old agentic orchestrator (replaced by unified processor)
- `search-frontend/server/context7.ts` — Old multi-source retrieval (replaced by cross-modal-search)
- `search-frontend/server/rag.ts` — Basic RAG fallback

**UI & Storage:**
- `search-frontend/src/ui/App.tsx` — React chat UI with streaming + file upload
- `search-frontend/server/blob.ts` — Azure Blob upload for attachments

**Optional:**
- `orchestrator/azure_ai_foundry_orchestrator.py` — Azure AI Projects agent (Python alternative)

## Development Notes

- TypeScript strict mode enabled
- Uses `pnpm` for package management (monorepo compatible)
- Dev server runs concurrent processes: Vite (5173) + Express (8787)
- API uses `concurrently` to run both web and API in dev mode
- Build outputs to `search-frontend/dist/` for both client and server
- Uses Vercel AI SDK for streaming and tool calling

**New Architecture:**
- Data Catalog runs on port 8081 (Python FastAPI)
- Node server auto-registers sources on startup
- Cross-modal search implements scoring: `score = length/1000 + timestamp/1e12 + modalityBoost`
- Unified processor combines intent analysis → routing → execution
- Graceful fallback when Data Catalog unavailable (uses env vars)

## Testing Locally

1. **Start Data Catalog** (recommended):
   ```bash
   cd data-catalog
   python -m venv .venv && source .venv/bin/activate
   pip install -r requirements.txt
   python app.py  # Runs on port 8081
   ```

2. **Configure and run Node server**:
   ```bash
   cd search-frontend
   cp .env.example .env  # Fill in Azure credentials
   pnpm install
   pnpm dev  # Runs web on 5173, API on 8787
   ```

3. **Verify**:
   - Open http://localhost:5173
   - Check console for "Auto-registering data sources" message
   - Upload file or ask questions — response streams with modality tags and source citations

**Without Data Catalog:**
- Node server falls back to env var configuration
- Cross-modal search still works but without dynamic discovery

## Modality Services (Optional)

If running specialized services, set their endpoint env vars:
- **DocGPT**: Document search using Docling + embeddings
- **TableGPT**: Natural language SQL over CSV/XLSX via Vanna + DuckDB + Ollama
- **Image**: Caption-based search and ingest
- **Audio**: Transcription + semantic search
- **Video**: Multi-modal processing (audio + captions + diarization)

Each service is standalone; orchestrator calls them via HTTP POST with `{ query }` or `{ blobUrl }`.

## Production Deployment

**Data Catalog Service:**
1. Deploy FastAPI app: `cd data-catalog`
2. Set `DATABASE_URL` for persistent database (PostgreSQL recommended)
3. Run: `uvicorn app:app --host 0.0.0.0 --port 8081`
4. Set `DATA_CATALOG_ENDPOINT` in Node app to catalog URL

**Node Service:**
1. Build: `cd search-frontend && pnpm build`
2. Set `DATA_CATALOG_ENDPOINT` env var
3. Run: `node dist/server/index.js`
4. Frontend served as static from `dist/`
5. Auto-registration occurs on startup

**Python Orchestrator (Optional):**
1. Set `AZURE_AI_PROJECT_CONNECTION_STRING` or `AZURE_AI_PROJECT_ENDPOINT`
2. Run: `python -m orchestrator.app` (port 8080)
3. Alternative to Node unified processor for Azure AI Foundry deployments

**Deployment Order:**
1. Data Catalog first
2. Modality services (register themselves or use auto-registration)
3. Node/Python main services last