Create an enterprise AI search solution with Agentic AI orchestrator using the Tool Use Pattern and created using the azure-ai-projects package and deploying in azure AI foundry. The Agentic AI orchestrator will connect to different services which handle different data types, different agents, different tools using MCP, and different data sources like azure ai search, azure blob storage, etc. Implement context engineering. Also build a simple search UI to interact with. Use context7 to retrieve the latest docs.

<!-- # AGENTS.md

## Setup commands
- Install deps: `pnpm install`
- Start dev server: `pnpm dev`
- Run tests: `pnpm test`

## Code style
- TypeScript strict mode
- Single quotes, no semicolons
- Use functional patterns where possible -->
