# macOS
.DS_Store
.AppleDouble
.LSOverride

# Node / Frontend
node_modules/
.pnpm-store/
.turbo/
.vite/
dist/
build/
coverage/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Vite/React
*.local
*.cache

# TypeScript
*.tsbuildinfo

# Env
.env
.env.*
!.env.example

# Editor/IDE
.vscode/
.idea/
*.swp
*.swo

# Python
__pycache__/
*.pyc
*.pyo
*.pyd
*.pytest_cache/
.venv/
venv/
.mypy_cache/
.pytype/

# Databases and data files
*.db
*.sqlite
*.sqlite3
*.pkl
*.pickle
*.h5

# Project-specific artifacts
search-frontend/.vite/
search-frontend/dist/
search-frontend/node_modules/
search-frontend/coverage/

# DocGPT artifacts
doc/DocGPT/vector_db/
doc/DocGPT/uploads/
doc/DocGPT/ngrok
doc/DocGPT/ngrok.tgz

# Image/video/audio app caches
image/**/__pycache__/
video/**/__pycache__/
audio/**/__pycache__/

# Misc temp
.tmp/
.temp/
tmp/

# Recursive directory ignores
**/node_modules/
**/dist/
**/build/
**/coverage/
**/.vite/
**/.turbo/
**/.cache/
**/.next/
**/.parcel-cache/
**/.pytest_cache/
**/__pycache__/
**/.venv/
**/venv/
**/logs/
**/tmp/
**/.temp/
**/.mypy_cache/
**/uploads/
**/vector_db/
**/ngrok
**/ngrok.tgz
