# Enterprise AI Search - Architecture Overview

This document provides a visual representation of the system architecture.

## System Architecture Diagram

```mermaid
graph TB
    subgraph "Client Layer"
        UI[React UI<br/>App.tsx<br/>Port 5173]
    end

    subgraph "Main Application - search-frontend"
        API[Express API<br/>server/index.ts<br/>Port 8787]
        UQP[Unified Query Processor<br/>unified-query-processor.ts<br/>Intent + Routing + Orchestration]
        CMS[Cross-Modal Search<br/>cross-modal-search.ts<br/>Data Catalog Integration]
        DCC[Data Catalog Client<br/>data-catalog-client.ts]
        BLOB[Blob Storage<br/>blob.ts<br/>Azure Blob Upload]
    end

    subgraph "Data Catalog Service"
        DC[Data Catalog<br/>FastAPI + SQLite<br/>Port 8081]
        DCDB[(SQLite DB<br/>Source Registry)]
    end

    subgraph "Azure Services"
        AOAI[Azure OpenAI<br/>GPT-4o]
        ASEARCH[Azure AI Search<br/>Text Index]
        ABLOB[Azure Blob Storage<br/>File Uploads]
    end

    subgraph "Optional: Modality Services"
        DOC[DocGPT<br/>Document Search<br/>Docling + Embeddings]
        IMG[ImageGPT<br/>Caption Search]
        AUD[AudioGPT<br/>Transcription Search]
        VID[VideoGPT<br/>Multi-modal Processing]
        TBL[TableGPT<br/>Vanna + DuckDB]
    end

    subgraph "Optional: Azure AI Foundry"
        ORCH[Python Orchestrator<br/>azure_ai_foundry_orchestrator.py<br/>Port 8080]
    end

    subgraph "Optional: MCP Integration"
        MCP[MCP HTTP Bridge<br/>External Tools]
    end

    %% Client to API
    UI -->|POST /api/chat<br/>message + file| API

    %% API Flow
    API -->|1. Upload file| BLOB
    BLOB --> ABLOB
    API -->|2. Get latest docs or<br/>cross-modal search| CMS
    API -->|3. Process query with<br/>context + tools| UQP

    %% Cross-Modal Search
    CMS -->|Query available<br/>sources| DCC
    DCC --> DC
    DC --> DCDB
    CMS -->|Search text| ASEARCH
    CMS -->|Search documents| DOC
    CMS -->|Search images| IMG
    CMS -->|Search audio| AUD
    CMS -->|Search video| VID
    CMS -->|Search tables| TBL

    %% Unified Query Processor Tools
    UQP -->|LLM calls| AOAI
    UQP -->|Tool: retrieveContext| CMS
    UQP -->|Tool: fetchBlobText| ABLOB
    UQP -->|Tool: mcpCall| MCP
    UQP -->|Tool: docSearch/ingest| DOC
    UQP -->|Tool: imageSearch/ingest| IMG
    UQP -->|Tool: audioSearch/ingest| AUD
    UQP -->|Tool: videoSearch/process| VID

    %% Alternative Orchestrator
    API -.->|Alternative:<br/>Azure AI Foundry| ORCH
    ORCH --> AOAI

    %% Auto-registration
    API -->|Startup:<br/>Auto-register sources| DC
    DOC -.->|Self-register| DC
    IMG -.->|Self-register| DC
    AUD -.->|Self-register| DC
    VID -.->|Self-register| DC
    TBL -.->|Self-register| DC

    %% Response Stream
    UQP -->|Stream response<br/>with sources| API
    API -->|SSE stream| UI

    %% Styling
    classDef primary fill:#4CAF50,stroke:#2E7D32,color:#fff
    classDef secondary fill:#2196F3,stroke:#1565C0,color:#fff
    classDef azure fill:#0078D4,stroke:#004578,color:#fff
    classDef optional fill:#FF9800,stroke:#E65100,color:#fff
    classDef storage fill:#9C27B0,stroke:#6A1B9A,color:#fff

    class UI,API,UQP primary
    class CMS,DCC,DC secondary
    class AOAI,ASEARCH,ABLOB azure
    class DOC,IMG,AUD,VID,TBL,ORCH,MCP optional
    class DCDB,BLOB storage
```

## Key Flows

### 1. Startup Flow
- Node server loads `.env` → validates required configuration
- Auto-registers Azure AI Search and optional modality services with Data Catalog
- Data Catalog becomes single source of truth for all data sources

### 2. Query Flow
1. User submits message (+optional file) via React UI
2. API endpoint receives request and uploads file to Azure Blob Storage if present
3. Cross-Modal Search queries Data Catalog for available healthy sources
4. Unified Query Processor analyzes intent and calls appropriate tools
5. Response streams back to UI with `[MODALITY]` tags and source citations

### 3. Tool Orchestration
The Unified Query Processor exposes these tools to the LLM:
- **`retrieveContext`**: Cross-modal search via Data Catalog
- **`fetchBlobText`**: Retrieve text content from Azure Blob URLs
- **`mcpCall`**: Bridge to external MCP tools via HTTP
- **Modality tools**: Document/image/audio/video ingest and search operations

### 4. Data Catalog Benefits
- **Single Source of Truth**: Centralized registry for all data sources
- **Dynamic Discovery**: Services auto-register their capabilities
- **Health Monitoring**: Track availability of each service
- **Graceful Fallback**: Falls back to environment variables if unavailable

## Component Descriptions

### Core Components (Green)
- **React UI**: Chat interface with file upload and streaming responses
- **Express API**: Main HTTP server handling chat requests
- **Unified Query Processor**: Combines intent analysis, routing, and orchestration

### Data Catalog System (Blue)
- **Data Catalog Service**: FastAPI service managing source metadata
- **SQLite Database**: Persistent storage for source registry

### Azure Services (Azure Blue)
- **Azure OpenAI**: LLM for query processing and tool orchestration
- **Azure AI Search**: Text-based semantic search index
- **Azure Blob Storage**: File upload storage

### Optional Modality Services (Orange)
- **DocGPT**: Document processing with Docling + embeddings
- **ImageGPT**: Image captioning and search
- **AudioGPT**: Audio transcription and search
- **VideoGPT**: Multi-modal video processing
- **TableGPT**: Natural language SQL over structured data

### Storage Components (Purple)
- **Blob Upload Handler**: Azure Blob Storage client
- **Source Registry DB**: SQLite database for Data Catalog

## Architecture Principles

1. **Unified Processor Pattern**: Replaces separate orchestrator + context7 modules
2. **Data Catalog First**: Centralized metadata enables dynamic service discovery
3. **Cross-Modal Intelligence**: Automatic routing across text/document/image/audio/video
4. **Extensibility**: New modalities can be added via simple registration
5. **Graceful Degradation**: System operates with reduced functionality if services are unavailable

## Ports

- **5173**: Vite dev server (React UI)
- **8787**: Express API server
- **8081**: Data Catalog service (Python FastAPI)
- **8080**: Azure AI Foundry orchestrator (optional)

## Technology Stack

- **Frontend**: React, TypeScript, Vite
- **Backend**: Node.js, Express, TypeScript
- **AI/ML**: Azure OpenAI (GPT-4o), Vercel AI SDK
- **Search**: Azure AI Search, custom modality services
- **Storage**: Azure Blob Storage, SQLite
- **Data Catalog**: Python FastAPI, SQLAlchemy
- **Optional**: Azure AI Foundry (Python), MCP tools
