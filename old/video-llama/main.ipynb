{"cells": [{"cell_type": "code", "execution_count": 3, "id": "0f7dbd75", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\Desktop\\unnanu\\videochat\\.venv\\Lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import json\n", "import pyodbc\n", "import numpy as np\n", "from sentence_transformers import SentenceTransformer, util\n", "\n", "# ================= CONFIG =================\n", "AZURE_SQL_CONNECTION_STRING = (\n", "    \"Driver={ODBC Driver 18 for SQL Server};\"\n", "    \"Server=tcp:videoprocessingserver.database.windows.net,1433;\"\n", "    \"Database=VideoProcessingDB;\"\n", "    \"Uid=sqladmin;\"\n", "    \"Pwd=Unnanu@2024;\"\n", "    \"Encrypt=yes;\"\n", "    \"TrustServerCertificate=no;\"\n", "    \"Connection Timeout=600;\"\n", ")\n", "\n", "# Load embeddings model (used in retrieve_relevant_rows)\n", "embedder = SentenceTransformer(\"all-MiniLM-L6-v2\")\n", "\n", "# ============== DB FUNCTIONS ==============\n", "def connect_to_azure_sql():\n", "    try:\n", "        conn = pyodbc.connect(AZURE_SQL_CONNECTION_STRING)\n", "        return conn\n", "    except pyodbc.Error as e:\n", "        print(f\"❌ Error connecting to Azure SQL: {e}\")\n", "        raise\n", "\n", "def retrieve_relevant_rows(query: str, top_k: int = 1):\n", "    query_embedding = embedder.encode(query, convert_to_numpy=True).astype(np.float32)\n", "    conn = connect_to_azure_sql()\n", "    cursor = conn.cursor()\n", "\n", "    sql_query = \"\"\"\n", "    SELECT VideoPath, Description, SrtContent, SrtContentObjects, Transcript, \n", "           WordTimestamps, AudioTags, Diarization, Embeddings\n", "    FROM VideoProcessingResults\n", "    \"\"\"\n", "    cursor.execute(sql_query)\n", "    rows = cursor.fetchall()\n", "\n", "    results = []\n", "    for row in rows:\n", "        result = {\n", "            \"VideoPath\": row.VideoPath,\n", "            \"Description\": row.Description or \"\",\n", "            \"SrtContent\": row.SrtContent or \"\",\n", "            \"SrtContentObjects\": row.SrtContentObjects or \"\",\n", "            \"Transcript\": row.Transcript or \"\",\n", "            \"WordTimestamps\": json.loads(row.WordTimestamps) if row.WordTimestamps else [],\n", "            \"AudioTags\": json.loads(row.AudioTags) if row.AudioTags else [],\n", "            \"Diarization\": json.loads(row.Diarization) if row.Diarization else [],\n", "            \"Embeddings\": json.loads(row.Embeddings) if row.Embeddings else {}\n", "        }\n", "\n", "        embeddings = result[\"Embeddings\"]\n", "        max_similarity = 0.0\n", "        for field in [\"description_embedding\", \"transcript_embedding\"]:\n", "            if field in embeddings:\n", "                field_embedding = np.array(embeddings[field], dtype=np.float32)\n", "                similarity = util.cos_sim(query_embedding, field_embedding)[0][0].item()\n", "                max_similarity = max(max_similarity, similarity)\n", "\n", "        result[\"relevance_score\"] = max_similarity\n", "        results.append(result)\n", "\n", "    results = sorted(results, key=lambda x: x[\"relevance_score\"], reverse=True)[:top_k]\n", "    cursor.close()\n", "    conn.close()\n", "    return results"]}, {"cell_type": "code", "execution_count": 12, "id": "d057a6ae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== Top 3 Results ===\n", "\n", "Result 1:\n", "VideoPath: https://strorageaccount123.blob.core.windows.net/videosearch/2ec97da4-5178-47ce-b252-51d6562775e2.mp4\n", "Description: The video showcases a woman in a kitchen, wearing a black t-shirt with the text \"I ❤️ Cooking.\" She begins by spreading peanut butter on slices of bread. The scene transitions to her cutting the bread...\n", "Transcript: Roll ups are super quick and easy to prepare. Take any of your favorite bread and roll. Spread peanut butter, Nutella then some fruits, a little bit of granola, cereal to get some crunch here and then...\n", "Relevance Score: 0.1910\n", "\n", "Result 2:\n", "VideoPath: https://strorageaccount123.blob.core.windows.net/videosearch/29203bd3-d475-4642-8437-e0024cb4c33d.mp4\n", "Description: The video opens with a man in a blue shirt and tie speaking to the camera, discussing job listings on a computer screen. The screen displays various job positions such as \"3097 - Cybersecurity Analyst...\n", "Transcript: Here is the new feature. It's called Invite by AI Matching. What it does is when you click this, it says auto-invite job applicants. Are you sure you want to schedule in relation to all eligible appli...\n", "Relevance Score: 0.1198\n", "\n", "Result 3:\n", "VideoPath: https://strorageaccount123.blob.core.windows.net/videosearch/eb0cc947-d5ba-498d-959a-a97733186ae3.mp4\n", "Description: The video depicts a meeting between two men seated at a wooden table in an office setting. One man is wearing glasses and a black polo shirt, while the other has gray hair and also wears a black polo ...\n", "Transcript: If you're ignoring AI, you might be put out of work. I don't care what your history, I don't care what your grades, your maybe scholar, PhD. Are you solving a real business problem? Every time I had a...\n", "Relevance Score: 0.1066\n"]}], "source": ["import json\n", "import pyodbc\n", "import numpy as np\n", "from sentence_transformers import SentenceTransformer, util\n", "\n", "# ================= CONFIG =================\n", "AZURE_SQL_CONNECTION_STRING = (\n", "    \"Driver={ODBC Driver 18 for SQL Server};\"\n", "    \"Server=tcp:videoprocessingserver.database.windows.net,1433;\"\n", "    \"Database=VideoProcessingDB;\"\n", "    \"Uid=sqladmin;\"\n", "    \"Pwd=Unnanu@2024;\"\n", "    \"Encrypt=yes;\"\n", "    \"TrustServerCertificate=no;\"\n", "    \"Connection Timeout=600;\"\n", ")\n", "\n", "# Load embeddings model (used in retrieve_relevant_rows)\n", "embedder = SentenceTransformer(\"all-MiniLM-L6-v2\")\n", "\n", "# ============== DB FUNCTIONS ==============\n", "def connect_to_azure_sql():\n", "    try:\n", "        conn = pyodbc.connect(AZURE_SQL_CONNECTION_STRING)\n", "        return conn\n", "    except pyodbc.Error as e:\n", "        print(f\"❌ Error connecting to Azure SQL: {e}\")\n", "        raise\n", "\n", "def retrieve_relevant_rows(query: str, top_k: int = 1):\n", "    query_embedding = embedder.encode(query, convert_to_numpy=True).astype(np.float32)\n", "    conn = connect_to_azure_sql()\n", "    cursor = conn.cursor()\n", "\n", "    sql_query = \"\"\"\n", "    SELECT VideoPath, Description, SrtContent, SrtContentObjects, Transcript, \n", "           WordTimestamps, AudioTags, Diarization, Embeddings\n", "    FROM VideoProcessingResults\n", "    \"\"\"\n", "    cursor.execute(sql_query)\n", "    rows = cursor.fetchall()\n", "\n", "    results = []\n", "    for row in rows:\n", "        result = {\n", "            \"VideoPath\": row.VideoPath,\n", "            \"Description\": row.Description or \"\",\n", "            \"SrtContent\": row.SrtContent or \"\",\n", "            \"SrtContentObjects\": row.SrtContentObjects or \"\",\n", "            \"Transcript\": row.Transcript or \"\",\n", "            \"WordTimestamps\": json.loads(row.WordTimestamps) if row.WordTimestamps else [],\n", "            \"AudioTags\": json.loads(row.AudioTags) if row.AudioTags else [],\n", "            \"Diarization\": json.loads(row.Diarization) if row.Diarization else [],\n", "            \"Embeddings\": json.loads(row.Embeddings) if row.Embeddings else {}\n", "        }\n", "\n", "        embeddings = result[\"Embeddings\"]\n", "        max_similarity = 0.0\n", "        for field in [\"description_embedding\", \"transcript_embedding\"]:\n", "            if field in embeddings:\n", "                field_embedding = np.array(embeddings[field], dtype=np.float32)\n", "                similarity = util.cos_sim(query_embedding, field_embedding)[0][0].item()\n", "                max_similarity = max(max_similarity, similarity)\n", "\n", "        result[\"relevance_score\"] = max_similarity\n", "        results.append(result)\n", "\n", "    results = sorted(results, key=lambda x: x[\"relevance_score\"], reverse=True)[:top_k]\n", "    cursor.close()\n", "    conn.close()\n", "    return results\n", "\n", "# ============== MAIN FUNCTION ==============\n", "def main():\n", "    query = input(\"🔍 Enter your query: \")\n", "    top_results = retrieve_relevant_rows(query, top_k=3)\n", "\n", "    print(\"\\n=== Top 3 Results ===\")\n", "    for i, res in enumerate(top_results, start=1):\n", "        print(f\"\\nResult {i}:\")\n", "        print(f\"VideoPath: {res['VideoPath']}\")\n", "        print(f\"Description: {res['Description'][:200]}...\")  # show only first 200 chars\n", "        print(f\"Transcript: {res['Transcript'][:200]}...\")\n", "        print(f\"Relevance Score: {res['relevance_score']:.4f}\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n"]}, {"cell_type": "markdown", "id": "be84b78d", "metadata": {}, "source": ["code with query handling:--------"]}, {"cell_type": "code", "execution_count": 9, "id": "16cecfe7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔧 Processed Query: job matching\n", "\n", "=== Top 3 Results ===\n", "\n", "========== Result 1 ==========\n", "VideoPath       : https://strorageaccount123.blob.core.windows.net/videosearch/29203bd3-d475-4642-8437-e0024cb4c33d.mp4\n", "SrtContentObjects: 1\n", "00:00:00,000 --> 00:01:11,240\n", "The video shows a person clicking on an email address, then scrolling through the list of people.\n", "\n", "2\n", "00:01:11,240 --> 00:03:05,080\n", "The video shows a man speaking about job applications, and then the scene transitions to show different tabs on a website. The website has multiple tabs with various information related to job applications.\n", "\n", "Description     : The video opens with a man in a blue shirt and tie speaking to the camera, discussing job listings on a computer screen. The screen displays various job positions such as \"3097 - Cybersecurity Analyst\" and \"3055 - Systems Analyst,\" along with details like dates of birth and salaries. The man explains how an AI system automatically invites applicants for these jobs based on their resume database. He then shows a list of job invitations sent by users named \"arvind,\" \"samantha,\" and others, detailing the date of invitation and the number of applications received.\n", "\n", "Next, the video focuses on a specific job titled \"3099 Unravens DB Scoring Tags.\" The man highlights different candidates invited through this process, including \"<PERSON>,\" \"<PERSON><PERSON>,\" and others, all scoring above 50 points. He emphasizes that the AI system prioritizes high-scoring candidates while excluding those below the threshold.\n", "\n", "Throughout the video, the man continues to engage with the audience, pointing out various aspects of the job search process, including the criteria used to determine invitees and the importance of meeting minimum score requirements. The video concludes with the man summarizing the key points discussed, reinforcing the benefits of using an automated system for job matching and application tracking.\n", "SrtContent      : 1\n", "00:00:00,000 --> 00:00:01,199\n", "Here is the new feature.\n", "\n", "2\n", "00:00:01,320 --> 00:00:03,419\n", "It's called Invite by AI Matching.\n", "\n", "3\n", "00:00:04,139 --> 00:00:06,879\n", "What it does is when you click this,\n", "\n", "4\n", "00:00:08,040 --> 00:00:10,279\n", "it says auto-invite job applicants.\n", "\n", "5\n", "00:00:10,539 --> 00:00:11,919\n", "Are you sure you want to schedule\n", "\n", "6\n", "00:00:11,919 --> 00:00:14,580\n", "in relation to all eligible applicants for this job?\n", "\n", "7\n", "00:00:15,800 --> 00:00:19,699\n", "When it says schedule, it kicks out schedule here.\n", "\n", "8\n", "00:00:20,439 --> 00:00:22,739\n", "Then it's looking into nano database\n", "\n", "9\n", "00:00:22,739 --> 00:00:25,420\n", "as well as resume database.\n", "\n", "10\n", "00:00:26,019 --> 00:00:29,359\n", "As I said, we have resume, 127,000 resumes.\n", "\n", "11\n", "00:00:30,060 --> 00:00:31,579\n", "It's going to schedule it.\n", "\n", "12\n", "00:00:32,140 --> 00:00:36,700\n", "Once it is processed, we're going to have...\n", "\n", "13\n", "00:00:40,299 --> 00:00:42,119\n", "Let's see. Let's look at it here.\n", "\n", "14\n", "00:00:43,620 --> 00:00:45,420\n", "Let's see. They've done a manual here.\n", "\n", "15\n", "00:00:49,260 --> 00:00:51,280\n", "Yeah, you're going to get like this.\n", "\n", "16\n", "00:00:51,840 --> 00:00:53,500\n", "Auto-invite job resume.\n", "\n", "17\n", "00:00:54,480 --> 00:00:56,020\n", "So these are automated.\n", "\n", "18\n", "00:00:57,960 --> 00:00:59,740\n", "All these people received,\n", "\n", "19\n", "00:01:00,000 --> 00:01:00,719\n", "automated email.\n", "\n", "20\n", "00:01:02,939 --> 00:01:04,620\n", "And they get an email\n", "\n", "21\n", "00:01:05,780 --> 00:01:07,420\n", "to apply to the job.\n", "\n", "22\n", "00:01:09,060 --> 00:01:11,099\n", "So it matched with the score\n", "\n", "23\n", "00:01:11,099 --> 00:01:12,599\n", "and they get it.\n", "\n", "24\n", "00:01:12,920 --> 00:01:14,400\n", "Let's refresh this.\n", "\n", "25\n", "00:01:15,599 --> 00:01:16,819\n", "See if it's run anything.\n", "\n", "26\n", "00:01:20,140 --> 00:01:21,439\n", "Yeah, it did not.\n", "\n", "27\n", "00:01:21,560 --> 00:01:21,780\n", "Not yet.\n", "\n", "28\n", "00:01:25,900 --> 00:01:28,159\n", "So because this is already run,\n", "\n", "29\n", "00:01:30,000 --> 00:01:30,680\n", "it's going to run automatically.\n", "\n", "30\n", "00:01:30,680 --> 00:01:31,920\n", "So let's do a matching here.\n", "\n", "31\n", "00:01:35,460 --> 00:01:37,599\n", "So yeah, they invited everybody.\n", "\n", "32\n", "00:01:38,680 --> 00:01:40,480\n", "But these are the lowest scores.\n", "\n", "33\n", "00:01:42,079 --> 00:01:43,400\n", "Let's look at this one.\n", "\n", "34\n", "00:01:48,960 --> 00:01:51,280\n", "Yeah, they're unrated manually, everybody.\n", "\n", "35\n", "00:01:51,880 --> 00:01:54,120\n", "Oh, there are a couple of them, but low score.\n", "\n", "36\n", "00:01:55,439 --> 00:01:56,760\n", "These are all low score.\n", "\n", "37\n", "00:01:56,939 --> 00:01:59,099\n", "How does it know who to invite automatically?\n", "\n", "38\n", "00:01:59,480 --> 00:01:59,980\n", "Yeah, it's going to do it.\n", "\n", "39\n", "00:01:59,980 --> 00:01:59,980\n", "\n", "\n", "40\n", "00:01:59,980 --> 00:01:59,980\n", "\n", "\n", "41\n", "00:01:59,980 --> 00:01:59,980\n", "\n", "\n", "42\n", "00:02:00,959 --> 00:02:04,599\n", "So the score has to be 50 and above.\n", "\n", "43\n", "00:02:05,859 --> 00:02:08,020\n", "So this is 29, 36.\n", "\n", "44\n", "00:02:09,060 --> 00:02:11,259\n", "It should invite these two people\n", "\n", "45\n", "00:02:11,259 --> 00:02:14,319\n", "because Azure says Azure's score is high.\n", "\n", "46\n", "00:02:14,800 --> 00:02:16,439\n", "But non-none score is low.\n", "\n", "47\n", "00:02:17,980 --> 00:02:19,479\n", "Then it should send out an invitation.\n", "\n", "48\n", "00:02:20,500 --> 00:02:21,979\n", "Let's wait for...\n", "\n", "49\n", "00:02:24,980 --> 00:02:26,599\n", "So it should look at score.\n", "\n", "50\n", "00:02:27,000 --> 00:02:28,659\n", "Yeah, it did send somebody.\n", "\n", "51\n", "00:02:29,640 --> 00:02:30,979\n", "Yeah, here you go.\n", "\n", "52\n", "00:02:34,500 --> 00:02:36,879\n", "It sent to all these people.\n", "\n", "53\n", "00:02:38,760 --> 00:02:39,780\n", "And that's...\n", "\n", "54\n", "00:02:40,980 --> 00:02:43,139\n", "Is that people from our database\n", "\n", "55\n", "00:02:43,139 --> 00:02:44,199\n", "or it's the full database\n", "\n", "56\n", "00:02:44,199 --> 00:02:45,620\n", "or it's only people who applied?\n", "\n", "57\n", "00:02:46,500 --> 00:02:48,159\n", "These are the resume database.\n", "\n", "58\n", "00:02:48,300 --> 00:02:49,599\n", "Means these are the...\n", "\n", "59\n", "00:02:49,599 --> 00:02:52,419\n", "This company database, their own people.\n", "\n", "60\n", "00:02:52,560 --> 00:02:53,620\n", "They have a resume.\n", "\n", "61\n", "00:02:55,920 --> 00:02:57,680\n", "So they have a resume.\n", "\n", "62\n", "00:02:57,680 --> 00:03:00,840\n", "Now system find a match\n", "\n", "63\n", "00:03:00,840 --> 00:03:04,500\n", "and also it automatically send the invitation to apply.\n", "\n", "\n", "Transcript      : Here is the new feature. It's called Invite by AI Matching. What it does is when you click this, it says auto-invite job applicants. Are you sure you want to schedule in relation to all eligible applicants for this job? When it says schedule, it kicks out schedule here. Then it's looking into nano database as well as resume database. As I said, we have resume, 127,000 resumes. It's going to schedule it. Once it is processed, we're going to have... Let's see. Let's look at it here. Let's see. They've done a manual here. Yeah, you're going to get like this. Auto-invite job resume. So these are automated. All these people received, automated email. And they get an email to apply to the job. So it matched with the score and they get it. Let's refresh this. See if it's run anything. Yeah, it did not. Not yet. So because this is already run, it's going to run automatically. So let's do a matching here. So yeah, they invited everybody. But these are the lowest scores. Let's look at this one. Yeah, they're unrated manually, everybody. Oh, there are a couple of them, but low score. These are all low score. How does it know who to invite automatically? Yeah, it's going to do it. So the score has to be 50 and above. So this is 29, 36. It should invite these two people because <PERSON>zure says <PERSON>zure's score is high. But non-none score is low. Then it should send out an invitation. Let's wait for... So it should look at score. Yeah, it did send somebody. Yeah, here you go. It sent to all these people. And that's... Is that people from our database or it's the full database or it's only people who applied? These are the resume database. Means these are the... This company database, their own people. They have a resume. So they have a resume. Now system find a match and also it automatically send the invitation to apply.\n", "WordTimestamps  : [{'word': ' Here', 'start': 0.0, 'end': 0.32}, {'word': ' is', 'start': 0.32, 'end': 0.52}, {'word': ' the', 'start': 0.52, 'end': 0.64}, {'word': ' new', 'start': 0.64, 'end': 0.82}, {'word': ' feature.', 'start': 0.82, 'end': 1.2}, {'word': \" It's\", 'start': 1.32, 'end': 1.52}, {'word': ' called', 'start': 1.52, 'end': 1.7}, {'word': ' Invite', 'start': 1.7, 'end': 2.26}, {'word': ' by', 'start': 2.26, 'end': 2.4}, {'word': ' AI', 'start': 2.4, 'end': 2.7}, {'word': ' Matching.', 'start': 2.7, 'end': 3.42}, {'word': ' What', 'start': 4.14, 'end': 4.7}, {'word': ' it', 'start': 4.7, 'end': 4.92}, {'word': ' does', 'start': 4.92, 'end': 5.24}, {'word': ' is', 'start': 5.24, 'end': 5.62}, {'word': ' when', 'start': 5.62, 'end': 6.24}, {'word': ' you', 'start': 6.24, 'end': 6.36}, {'word': ' click', 'start': 6.36, 'end': 6.58}, {'word': ' this,', 'start': 6.58, 'end': 6.88}, {'word': ' it', 'start': 7.8199999999999985, 'end': 8.1}, {'word': ' says', 'start': 8.1, 'end': 8.36}, {'word': ' auto', 'start': 8.36, 'end': 9.16}, {'word': '-invite', 'start': 9.16, 'end': 9.54}, {'word': ' job', 'start': 9.54, 'end': 9.74}, {'word': ' applicants.', 'start': 9.74, 'end': 10.28}, {'word': ' Are', 'start': 10.54, 'end': 10.84}, {'word': ' you', 'start': 10.84, 'end': 10.98}, {'word': ' sure', 'start': 10.98, 'end': 11.2}, {'word': ' you', 'start': 11.2, 'end': 11.32}, {'word': ' want', 'start': 11.32, 'end': 11.48}, {'word': ' to', 'start': 11.48, 'end': 11.58}, {'word': ' schedule', 'start': 11.58, 'end': 11.92}, {'word': ' in', 'start': 11.92, 'end': 12.14}, {'word': ' relation', 'start': 12.14, 'end': 12.38}, {'word': ' to', 'start': 12.38, 'end': 12.64}, {'word': ' all', 'start': 12.64, 'end': 12.92}, {'word': ' eligible', 'start': 12.92, 'end': 13.4}, {'word': ' applicants', 'start': 13.4, 'end': 13.86}, {'word': ' for', 'start': 13.86, 'end': 14.16}, {'word': ' this', 'start': 14.16, 'end': 14.32}, {'word': ' job?', 'start': 14.32, 'end': 14.58}, {'word': ' When', 'start': 15.8, 'end': 16.36}, {'word': ' it', 'start': 16.36, 'end': 16.46}, {'word': ' says', 'start': 16.46, 'end': 16.66}, {'word': ' schedule,', 'start': 16.66, 'end': 17.2}, {'word': ' it', 'start': 17.48, 'end': 18.28}, {'word': ' kicks', 'start': 18.28, 'end': 18.54}, {'word': ' out', 'start': 18.54, 'end': 18.82}, {'word': ' schedule', 'start': 18.82, 'end': 19.18}, {'word': ' here.', 'start': 19.18, 'end': 19.7}, {'word': ' Then', 'start': 20.439999999999994, 'end': 20.999999999999996}, {'word': \" it's\", 'start': 20.999999999999996, 'end': 21.56}, {'word': ' looking', 'start': 21.56, 'end': 21.76}, {'word': ' into', 'start': 21.76, 'end': 22.1}, {'word': ' nano', 'start': 22.1, 'end': 22.34}, {'word': ' database', 'start': 22.34, 'end': 22.74}, {'word': ' as', 'start': 22.74, 'end': 23.36}, {'word': ' well', 'start': 23.36, 'end': 23.6}, {'word': ' as', 'start': 23.6, 'end': 24.1}, {'word': ' resume', 'start': 24.1, 'end': 25.14}, {'word': ' database.', 'start': 25.14, 'end': 25.42}, {'word': ' As', 'start': 26.02, 'end': 26.58}, {'word': ' I', 'start': 26.58, 'end': 26.64}, {'word': ' said,', 'start': 26.64, 'end': 26.92}, {'word': ' we', 'start': 26.92, 'end': 27.02}, {'word': ' have', 'start': 27.02, 'end': 27.2}, {'word': ' resume,', 'start': 27.2, 'end': 27.7}, {'word': ' 127', 'start': 27.8, 'end': 28.4}, {'word': ',000', 'start': 28.4, 'end': 29.1}, {'word': ' resumes.', 'start': 29.1, 'end': 29.36}, {'word': \" It's\", 'start': 30.060000000000002, 'end': 30.62}, {'word': ' going', 'start': 30.62, 'end': 30.7}, {'word': ' to', 'start': 30.7, 'end': 30.82}, {'word': ' schedule', 'start': 30.82, 'end': 31.22}, {'word': ' it.', 'start': 31.22, 'end': 31.58}, {'word': ' Once', 'start': 32.14, 'end': 32.7}, {'word': ' it', 'start': 32.7, 'end': 32.94}, {'word': ' is', 'start': 32.94, 'end': 33.12}, {'word': ' processed,', 'start': 33.12, 'end': 33.9}, {'word': \" we're\", 'start': 34.44, 'end': 35.5}, {'word': ' going', 'start': 35.5, 'end': 35.64}, {'word': ' to', 'start': 35.64, 'end': 35.74}, {'word': ' have...', 'start': 35.74, 'end': 36.7}, {'word': \" Let's\", 'start': 40.3, 'end': 40.86}, {'word': ' see.', 'start': 40.86, 'end': 41.1}, {'word': \" Let's\", 'start': 41.12, 'end': 41.36}, {'word': ' look', 'start': 41.36, 'end': 41.58}, {'word': ' at', 'start': 41.58, 'end': 41.76}, {'word': ' it', 'start': 41.76, 'end': 41.84}, {'word': ' here.', 'start': 41.84, 'end': 42.12}, {'word': \" Let's\", 'start': 43.620000000000005, 'end': 44.18}, {'word': ' see.', 'start': 44.18, 'end': 44.2}, {'word': \" They've\", 'start': 44.26, 'end': 44.48}, {'word': ' done', 'start': 44.48, 'end': 44.66}, {'word': ' a', 'start': 44.66, 'end': 44.8}, {'word': ' manual', 'start': 44.8, 'end': 45.18}, {'word': ' here.', 'start': 45.18, 'end': 45.42}, {'word': ' Yeah,', 'start': 49.260000000000005, 'end': 49.82}, {'word': \" you're\", 'start': 49.96, 'end': 50.36}, {'word': ' going', 'start': 50.36, 'end': 50.48}, {'word': ' to', 'start': 50.48, 'end': 50.58}, {'word': ' get', 'start': 50.58, 'end': 50.74}, {'word': ' like', 'start': 50.74, 'end': 50.96}, {'word': ' this.', 'start': 50.96, 'end': 51.28}, {'word': ' Auto', 'start': 51.84, 'end': 52.4}, {'word': '-invite', 'start': 52.4, 'end': 52.8}, {'word': ' job', 'start': 52.8, 'end': 53.0}, {'word': ' resume.', 'start': 53.0, 'end': 53.5}, {'word': ' So', 'start': 54.480000000000004, 'end': 55.04}, {'word': ' these', 'start': 55.04, 'end': 55.36}, {'word': ' are', 'start': 55.36, 'end': 55.54}, {'word': ' automated.', 'start': 55.54, 'end': 56.02}, {'word': ' All', 'start': 57.96000000000001, 'end': 58.52}, {'word': ' these', 'start': 58.52, 'end': 58.88}, {'word': ' people', 'start': 58.88, 'end': 59.3}, {'word': ' received,', 'start': 59.3, 'end': 59.74}, {'word': ' automated', 'start': 60.0, 'end': 60.3}, {'word': ' email.', 'start': 60.3, 'end': 60.72}, {'word': ' And', 'start': 62.94, 'end': 63.58}, {'word': ' they', 'start': 63.58, 'end': 63.88}, {'word': ' get', 'start': 63.88, 'end': 64.14}, {'word': ' an', 'start': 64.14, 'end': 64.26}, {'word': ' email', 'start': 64.26, 'end': 64.62}, {'word': ' to', 'start': 65.78, 'end': 66.42}, {'word': ' apply', 'start': 66.42, 'end': 66.7}, {'word': ' to', 'start': 66.7, 'end': 66.96}, {'word': ' the', 'start': 66.96, 'end': 67.1}, {'word': ' job.', 'start': 67.1, 'end': 67.42}, {'word': ' So', 'start': 68.82, 'end': 69.14}, {'word': ' it', 'start': 69.14, 'end': 69.32}, {'word': ' matched', 'start': 69.32, 'end': 69.9}, {'word': ' with', 'start': 69.9, 'end': 70.68}, {'word': ' the', 'start': 70.68, 'end': 70.74}, {'word': ' score', 'start': 70.74, 'end': 71.1}, {'word': ' and', 'start': 71.1, 'end': 71.82}, {'word': ' they', 'start': 71.82, 'end': 72.18}, {'word': ' get', 'start': 72.18, 'end': 72.42}, {'word': ' it.', 'start': 72.42, 'end': 72.6}, {'word': \" Let's\", 'start': 72.92, 'end': 73.56}, {'word': ' refresh', 'start': 73.56, 'end': 73.82}, {'word': ' this.', 'start': 73.82, 'end': 74.4}, {'word': ' See', 'start': 75.32, 'end': 75.64}, {'word': ' if', 'start': 75.64, 'end': 75.84}, {'word': \" it's\", 'start': 75.84, 'end': 76.12}, {'word': ' run', 'start': 76.12, 'end': 76.3}, {'word': ' anything.', 'start': 76.3, 'end': 76.82}, {'word': ' Yeah,', 'start': 80.14, 'end': 80.78}, {'word': ' it', 'start': 80.78, 'end': 80.9}, {'word': ' did', 'start': 80.9, 'end': 81.1}, {'word': ' not.', 'start': 81.1, 'end': 81.44}, {'word': ' Not', 'start': 81.56, 'end': 81.7}, {'word': ' yet.', 'start': 81.7, 'end': 81.78}, {'word': ' So', 'start': 85.9, 'end': 86.54}, {'word': ' because', 'start': 86.54, 'end': 86.94}, {'word': ' this', 'start': 86.94, 'end': 87.26}, {'word': ' is', 'start': 87.26, 'end': 87.4}, {'word': ' already', 'start': 87.4, 'end': 87.66}, {'word': ' run,', 'start': 87.66, 'end': 88.16}, {'word': \" it's\", 'start': 90.0, 'end': 90.0}, {'word': ' going', 'start': 90.0, 'end': 90.68}, {'word': ' to', 'start': 90.68, 'end': 90.68}, {'word': ' run', 'start': 90.68, 'end': 90.68}, {'word': ' automatically.', 'start': 90.68, 'end': 90.68}, {'word': ' So', 'start': 90.68, 'end': 90.68}, {'word': \" let's\", 'start': 90.68, 'end': 90.88}, {'word': ' do', 'start': 90.88, 'end': 91.0}, {'word': ' a', 'start': 91.0, 'end': 91.14}, {'word': ' matching', 'start': 91.14, 'end': 91.54}, {'word': ' here.', 'start': 91.54, 'end': 91.92}, {'word': ' So', 'start': 95.46000000000001, 'end': 95.98}, {'word': ' yeah,', 'start': 95.98, 'end': 96.5}, {'word': ' they', 'start': 96.54, 'end': 96.7}, {'word': ' invited', 'start': 96.7, 'end': 97.06}, {'word': ' everybody.', 'start': 97.06, 'end': 97.6}, {'word': ' But', 'start': 98.68, 'end': 99.2}, {'word': ' these', 'start': 99.2, 'end': 99.5}, {'word': ' are', 'start': 99.5, 'end': 99.68}, {'word': ' the', 'start': 99.68, 'end': 99.84}, {'word': ' lowest', 'start': 99.84, 'end': 100.12}, {'word': ' scores.', 'start': 100.12, 'end': 100.48}, {'word': \" Let's\", 'start': 102.08, 'end': 102.6}, {'word': ' look', 'start': 102.6, 'end': 102.74}, {'word': ' at', 'start': 102.74, 'end': 102.96}, {'word': ' this', 'start': 102.96, 'end': 103.2}, {'word': ' one.', 'start': 103.2, 'end': 103.4}, {'word': ' Yeah,', 'start': 108.96000000000001, 'end': 109.48}, {'word': \" they're\", 'start': 109.52, 'end': 109.9}, {'word': ' unrated', 'start': 109.9, 'end': 110.28}, {'word': ' manually,', 'start': 110.28, 'end': 110.7}, {'word': ' everybody.', 'start': 110.9, 'end': 111.28}, {'word': ' Oh,', 'start': 111.88000000000001, 'end': 112.4}, {'word': ' there', 'start': 112.5, 'end': 112.8}, {'word': ' are', 'start': 112.8, 'end': 113.04}, {'word': ' a', 'start': 113.04, 'end': 113.08}, {'word': ' couple', 'start': 113.08, 'end': 113.3}, {'word': ' of', 'start': 113.3, 'end': 113.4}, {'word': ' them,', 'start': 113.4, 'end': 113.56}, {'word': ' but', 'start': 113.58, 'end': 113.7}, {'word': ' low', 'start': 113.7, 'end': 113.88}, {'word': ' score.', 'start': 113.88, 'end': 114.12}, {'word': ' These', 'start': 115.44, 'end': 115.96}, {'word': ' are', 'start': 115.96, 'end': 116.1}, {'word': ' all', 'start': 116.1, 'end': 116.28}, {'word': ' low', 'start': 116.28, 'end': 116.52}, {'word': ' score.', 'start': 116.52, 'end': 116.76}, {'word': ' How', 'start': 116.94, 'end': 117.28}, {'word': ' does', 'start': 117.28, 'end': 117.44}, {'word': ' it', 'start': 117.44, 'end': 117.56}, {'word': ' know', 'start': 117.56, 'end': 117.72}, {'word': ' who', 'start': 117.72, 'end': 118.08}, {'word': ' to', 'start': 118.08, 'end': 118.22}, {'word': ' invite', 'start': 118.22, 'end': 118.54}, {'word': ' automatically?', 'start': 118.54, 'end': 119.1}, {'word': ' Yeah,', 'start': 119.48, 'end': 119.98}, {'word': \" it's\", 'start': 119.98, 'end': 119.98}, {'word': ' going', 'start': 119.98, 'end': 119.98}, {'word': ' to', 'start': 119.98, 'end': 119.98}, {'word': ' do', 'start': 119.98, 'end': 119.98}, {'word': ' it.', 'start': 119.98, 'end': 119.98}, {'word': ' So', 'start': 120.96, 'end': 121.58}, {'word': ' the', 'start': 121.58, 'end': 121.58}, {'word': ' score', 'start': 121.58, 'end': 122.48}, {'word': ' has', 'start': 122.48, 'end': 122.86}, {'word': ' to', 'start': 122.86, 'end': 123.32}, {'word': ' be', 'start': 123.32, 'end': 123.48}, {'word': ' 50', 'start': 123.48, 'end': 124.0}, {'word': ' and', 'start': 124.0, 'end': 124.34}, {'word': ' above.', 'start': 124.34, 'end': 124.6}, {'word': ' So', 'start': 125.86, 'end': 126.48}, {'word': ' this', 'start': 126.48, 'end': 126.72}, {'word': ' is', 'start': 126.72, 'end': 126.92}, {'word': ' 29,', 'start': 126.92, 'end': 127.32}, {'word': ' 36.', 'start': 127.58, 'end': 128.02}, {'word': ' It', 'start': 129.06, 'end': 129.68}, {'word': ' should', 'start': 129.68, 'end': 130.0}, {'word': ' invite', 'start': 130.0, 'end': 130.3}, {'word': ' these', 'start': 130.3, 'end': 130.68}, {'word': ' two', 'start': 130.68, 'end': 130.92}, {'word': ' people', 'start': 130.92, 'end': 131.26}, {'word': ' because', 'start': 131.26, 'end': 131.68}, {'word': ' Azure', 'start': 131.68, 'end': 132.54}, {'word': ' says', 'start': 132.54, 'end': 132.9}, {'word': \" Azure's\", 'start': 132.9, 'end': 133.64}, {'word': ' score', 'start': 133.64, 'end': 133.8}, {'word': ' is', 'start': 133.8, 'end': 134.04}, {'word': ' high.', 'start': 134.04, 'end': 134.32}, {'word': ' But', 'start': 134.8, 'end': 135.42}, {'word': ' non', 'start': 135.42, 'end': 135.64}, {'word': '-none', 'start': 135.64, 'end': 135.76}, {'word': ' score', 'start': 135.76, 'end': 135.88}, {'word': ' is', 'start': 135.88, 'end': 136.16}, {'word': ' low.', 'start': 136.16, 'end': 136.44}, {'word': ' Then', 'start': 137.73, 'end': 138.04}, {'word': ' it', 'start': 138.04, 'end': 138.26}, {'word': ' should', 'start': 138.26, 'end': 138.4}, {'word': ' send', 'start': 138.4, 'end': 138.78}, {'word': ' out', 'start': 138.78, 'end': 139.0}, {'word': ' an', 'start': 139.0, 'end': 139.12}, {'word': ' invitation.', 'start': 139.12, 'end': 139.48}, {'word': \" Let's\", 'start': 140.5, 'end': 141.12}, {'word': ' wait', 'start': 141.12, 'end': 141.36}, {'word': ' for...', 'start': 141.36, 'end': 141.98}, {'word': ' So', 'start': 144.71, 'end': 145.02}, {'word': ' it', 'start': 145.02, 'end': 145.18}, {'word': ' should', 'start': 145.18, 'end': 145.4}, {'word': ' look', 'start': 145.4, 'end': 146.08}, {'word': ' at', 'start': 146.08, 'end': 146.3}, {'word': ' score.', 'start': 146.3, 'end': 146.6}, {'word': ' Yeah,', 'start': 147.0, 'end': 147.28}, {'word': ' it', 'start': 147.4, 'end': 147.64}, {'word': ' did', 'start': 147.64, 'end': 147.84}, {'word': ' send', 'start': 147.84, 'end': 148.14}, {'word': ' somebody.', 'start': 148.14, 'end': 148.66}, {'word': ' Yeah,', 'start': 149.64000000000001, 'end': 150.24}, {'word': ' here', 'start': 150.4, 'end': 150.74}, {'word': ' you', 'start': 150.74, 'end': 150.82}, {'word': ' go.', 'start': 150.82, 'end': 150.98}, {'word': ' It', 'start': 154.5, 'end': 155.1}, {'word': ' sent', 'start': 155.1, 'end': 155.68}, {'word': ' to', 'start': 155.68, 'end': 155.98}, {'word': ' all', 'start': 155.98, 'end': 156.22}, {'word': ' these', 'start': 156.22, 'end': 156.5}, {'word': ' people.', 'start': 156.5, 'end': 156.88}, {'word': ' And', 'start': 158.76000000000002, 'end': 159.36}, {'word': \" that's...\", 'start': 159.36, 'end': 159.78}, {'word': ' Is', 'start': 160.98000000000002, 'end': 161.58}, {'word': ' that', 'start': 161.58, 'end': 161.72}, {'word': ' people', 'start': 161.72, 'end': 161.92}, {'word': ' from', 'start': 161.92, 'end': 162.3}, {'word': ' our', 'start': 162.3, 'end': 162.72}, {'word': ' database', 'start': 162.72, 'end': 163.14}, {'word': ' or', 'start': 163.14, 'end': 163.38}, {'word': \" it's\", 'start': 163.38, 'end': 163.6}, {'word': ' the', 'start': 163.6, 'end': 163.68}, {'word': ' full', 'start': 163.68, 'end': 163.88}, {'word': ' database', 'start': 163.88, 'end': 164.2}, {'word': ' or', 'start': 164.2, 'end': 164.48}, {'word': \" it's\", 'start': 164.48, 'end': 164.6}, {'word': ' only', 'start': 164.6, 'end': 164.78}, {'word': ' people', 'start': 164.78, 'end': 165.16}, {'word': ' who', 'start': 165.16, 'end': 165.34}, {'word': ' applied?', 'start': 165.34, 'end': 165.62}, {'word': ' These', 'start': 166.5, 'end': 167.1}, {'word': ' are', 'start': 167.1, 'end': 167.3}, {'word': ' the', 'start': 167.3, 'end': 167.46}, {'word': ' resume', 'start': 167.46, 'end': 167.8}, {'word': ' database.', 'start': 167.8, 'end': 168.16}, {'word': ' Means', 'start': 168.3, 'end': 168.6}, {'word': ' these', 'start': 168.6, 'end': 169.0}, {'word': ' are', 'start': 169.0, 'end': 169.28}, {'word': ' the...', 'start': 169.28, 'end': 169.6}, {'word': ' This', 'start': 169.6, 'end': 170.14}, {'word': ' company', 'start': 170.14, 'end': 170.58}, {'word': ' database,', 'start': 170.58, 'end': 170.96}, {'word': ' their', 'start': 171.4, 'end': 171.7}, {'word': ' own', 'start': 171.7, 'end': 172.0}, {'word': ' people.', 'start': 172.0, 'end': 172.42}, {'word': ' They', 'start': 172.56, 'end': 172.96}, {'word': ' have', 'start': 172.96, 'end': 173.2}, {'word': ' a', 'start': 173.2, 'end': 173.34}, {'word': ' resume.', 'start': 173.34, 'end': 173.62}, {'word': ' So', 'start': 175.92000000000002, 'end': 176.52}, {'word': ' they', 'start': 176.52, 'end': 176.92}, {'word': ' have', 'start': 176.92, 'end': 177.18}, {'word': ' a', 'start': 177.18, 'end': 177.3}, {'word': ' resume.', 'start': 177.3, 'end': 177.68}, {'word': ' Now', 'start': 177.68, 'end': 178.26}, {'word': ' system', 'start': 178.26, 'end': 179.74}, {'word': ' find', 'start': 179.74, 'end': 180.32}, {'word': ' a', 'start': 180.32, 'end': 180.54}, {'word': ' match', 'start': 180.54, 'end': 180.84}, {'word': ' and', 'start': 180.84, 'end': 181.24}, {'word': ' also', 'start': 181.24, 'end': 181.7}, {'word': ' it', 'start': 181.7, 'end': 181.94}, {'word': ' automatically', 'start': 181.94, 'end': 182.34}, {'word': ' send', 'start': 182.34, 'end': 183.14}, {'word': ' the', 'start': 183.14, 'end': 183.5}, {'word': ' invitation', 'start': 183.5, 'end': 183.8}, {'word': ' to', 'start': 183.8, 'end': 184.24}, {'word': ' apply.', 'start': 184.24, 'end': 184.5}]\n", "AudioTags       : [['Speech', 0.8018766641616821], ['Inside, small room', 0.29124903678894043], ['Sigh', 0.26131922006607056], ['Groan', 0.17233911156654358], ['Wail, moan', 0.08965220302343369]]\n", "Diarization     : [{'start': 0.03096875, 'end': 3.69284375, 'speaker': 'SPEAKER_01'}, {'start': 4.570343750000001, 'end': 14.94846875, 'speaker': 'SPEAKER_01'}, {'start': 16.29846875, 'end': 34.64159375, 'speaker': 'SPEAKER_01'}, {'start': 35.41784375, 'end': 37.64534375, 'speaker': 'SPEAKER_01'}, {'start': 40.68284375, 'end': 42.64034375, 'speaker': 'SPEAKER_01'}, {'start': 43.97346875, 'end': 45.86346875, 'speaker': 'SPEAKER_01'}, {'start': 47.095343750000005, 'end': 47.70284375, 'speaker': 'SPEAKER_01'}, {'start': 47.48346875, 'end': 47.534093750000004, 'speaker': 'SPEAKER_00'}, {'start': 49.79534375, 'end': 56.56221875000001, 'speaker': 'SPEAKER_01'}, {'start': 50.65596875, 'end': 51.16221875, 'speaker': 'SPEAKER_00'}, {'start': 58.45221875, 'end': 61.35471875, 'speaker': 'SPEAKER_01'}, {'start': 63.49784375, 'end': 65.42159375, 'speaker': 'SPEAKER_01'}, {'start': 66.41721875, 'end': 67.78409375000001, 'speaker': 'SPEAKER_01'}, {'start': 69.15096875, 'end': 77.11596875000001, 'speaker': 'SPEAKER_01'}, {'start': 78.76971875000001, 'end': 79.29284375, 'speaker': 'SPEAKER_01'}, {'start': 80.65971875000001, 'end': 82.26284375, 'speaker': 'SPEAKER_01'}, {'start': 86.53221875, 'end': 88.65846875000001, 'speaker': 'SPEAKER_01'}, {'start': 90.54846875000001, 'end': 93.72096875000001, 'speaker': 'SPEAKER_01'}, {'start': 96.42096875, 'end': 98.09159375, 'speaker': 'SPEAKER_01'}, {'start': 99.15471875, 'end': 100.94346875000001, 'speaker': 'SPEAKER_01'}, {'start': 102.47909375, 'end': 103.71096875, 'speaker': 'SPEAKER_01'}, {'start': 109.34721875000001, 'end': 114.57846875000001, 'speaker': 'SPEAKER_01'}, {'start': 115.87784375000001, 'end': 116.90721875000001, 'speaker': 'SPEAKER_01'}, {'start': 115.92846875000001, 'end': 119.55659375, 'speaker': 'SPEAKER_00'}, {'start': 120.97409375000001, 'end': 125.22659375, 'speaker': 'SPEAKER_01'}, {'start': 126.49221875, 'end': 128.97284375, 'speaker': 'SPEAKER_01'}, {'start': 129.64784375000002, 'end': 136.98846875, 'speaker': 'SPEAKER_01'}, {'start': 137.88284375, 'end': 140.31284375, 'speaker': 'SPEAKER_01'}, {'start': 140.92034375, 'end': 142.15221875, 'speaker': 'SPEAKER_01'}, {'start': 144.90284375000002, 'end': 149.23971875, 'speaker': 'SPEAKER_01'}, {'start': 149.91471875000002, 'end': 151.24784375000002, 'speaker': 'SPEAKER_01'}, {'start': 155.04471875000002, 'end': 157.25534375, 'speaker': 'SPEAKER_01'}, {'start': 159.36471875, 'end': 160.********, 'speaker': 'SPEAKER_00'}, {'start': 161.********, 'end': 166.**************, 'speaker': 'SPEAKER_00'}, {'start': 166.********, 'end': 173.**************, 'speaker': 'SPEAKER_01'}, {'start': 173.**************, 'end': 173.********, 'speaker': 'SPEAKER_00'}, {'start': 176.**************, 'end': 184.**************, 'speaker': 'SPEAKER_01'}]\n", "Relevance Score : 0.4578\n", "=================================\n", "\n", "========== Result 2 ==========\n", "VideoPath       : https://strorageaccount123.blob.core.windows.net/videosearch/b856699b-6f96-4136-b422-7adad02c93dc.mp4\n", "SrtContentObjects: 1\n", "00:00:00,000 --> 00:00:08,560\n", "The video starts with a woman standing at the front desk of a hotel, talking to someone. Then, a man walks into the frame and approaches her.\n", "\n", "2\n", "00:00:08,560 --> 00:00:11,920\n", "The scene features a woman in a black suit, white shirt, and purple scarf standing next to another person. The woman greets the other individual with \"Good morning\" and offers assistance by asking what they can do for them.\n", "\n", "3\n", "00:00:11,920 --> 00:00:16,320\n", "The man in the video is speaking to someone off-camera and appears to be giving a statement or making an announcement.\n", "\n", "4\n", "00:00:16,320 --> 00:00:20,200\n", "The video features a sequence of images where the same individual is shown in various poses, indicating movement or action. The setting appears to be indoors with wooden paneling on the wall and a partial view of what seems like an office environment.\n", "\n", "5\n", "00:00:20,200 --> 00:00:29,080\n", "The scene features a man and woman standing at a marble counter, engaged in conversation. The text overlay suggests that they are discussing how to communicate with guests more fluently and easily.\n", "\n", "6\n", "00:00:29,080 --> 00:00:35,920\n", "The scene features a man wearing a suit and tie who is engaged in a conversation. He speaks to the camera while making hand gestures, emphasizing his points with facial expressions. The background includes a plant and some artwork on the wall.\n", "\n", "7\n", "00:00:35,920 --> 00:00:40,400\n", "The video shows a man in business attire interacting with a receptionist at the front desk of a hotel. The woman hands him a document and he responds by thanking her.\n", "\n", "8\n", "00:00:40,400 --> 00:00:53,120\n", "The woman in the video is seen talking to a man and explaining something. She appears to be giving instructions or advice, as she is speaking directly to him and using specific language that suggests a professional context.\n", "\n", "9\n", "00:00:53,120 --> 00:00:56,520\n", "The scene shows a man in a suit speaking to the camera, and there are two captions that appear on the screen. The video is taken indoors with some plants visible in the background.\n", "\n", "10\n", "00:00:56,520 --> 00:00:59,800\n", "The man approaches the receptionist, who is holding a piece of paper. They engage in conversation and the receptionist appears to be reading from the paper while speaking with the man.\n", "\n", "11\n", "00:00:59,800 --> 00:01:02,920\n", "In this scene, a man in a suit is seen talking to someone off-camera. He appears to be engaged in a conversation and seems to be speaking slowly and carefully. The camera zooms in on his face as he continues to speak.\n", "\n", "12\n", "00:01:02,920 --> 00:01:05,239\n", "The scene features a woman in uniform who is smiling and talking to someone off-camera. She seems to be addressing the camera or another person, possibly providing instructions or information. The background appears to be an indoor setting with warm lighting, giving it a cozy ambiance.\n", "\n", "13\n", "00:01:05,239 --> 00:01:07,440\n", "The man in the suit engages with the camera, displaying various facial expressions and gestures. The text overlay at the top of the video reads 'Prepare yourself to interact correctly and politely with guests.'\n", "\n", "14\n", "00:01:07,440 --> 00:01:15,440\n", "The woman behind the reception desk hands a man an access code and instructions on how to use it.\n", "\n", "15\n", "00:01:15,440 --> 00:01:20,920\n", "The scene shows a woman in a black suit and tie speaking to the camera, with text on the screen providing information about hotel amenities.\n", "\n", "16\n", "00:01:20,920 --> 00:01:26,280\n", "The scene shows a man and woman standing at the front desk of a hotel. The man hands over a piece of paper to the receptionist, who then reads it aloud while smiling and responding with \"You're welcome.\"\n", "\n", "17\n", "00:01:26,280 --> 00:01:30,480\n", "The sequence of images shows a woman in uniform, possibly at an airport or train station, engaged in conversation with someone off-camera. The setting includes wooden paneling and there's visible text overlay on the video indicating that she is being asked about valet parking services.\n", "\n", "18\n", "00:01:30,480 --> 00:01:33,320\n", "In this scene, a man is shown in the video talking to the camera. He appears to be explaining something and there are no other objects or people visible in the frame besides him.\n", "\n", "19\n", "00:01:33,320 --> 00:01:41,320\n", "The scene depicts a man in business attire approaching the reception desk of what appears to be a hotel or corporate office. He is handed an ID card and asked for registration details on a form, which he fills out. The woman at the counter hands him another document, presumably his driver's license.\n", "\n", "20\n", "00:01:41,320 --> 00:01:43,280\n", "The scene is a series of still images from a video where a woman in business attire engages in a conversation with another person, who remains out of frame. The interaction appears to be professional and friendly, indicated by the smiling expressions on both parties' faces.\n", "\n", "21\n", "00:01:43,280 --> 00:01:45,400\n", "The man in the video is shown speaking, and he appears to be standing in an indoor setting with a blurred background. The focus of the scene remains on him throughout the series of images, which are static frames without any movement or change in scenery.\n", "\n", "22\n", "00:01:45,400 --> 00:01:46,800\n", "The video features a woman in business attire who appears to be engaged in conversation with someone off-camera. She is wearing a black blazer and a white shirt, complemented by a purple scarf tied around her neck. The background suggests an indoor setting with wooden paneling. There's no significant movement or action captured in the scene as it seems to focus on the interaction between the two individuals.\n", "\n", "23\n", "00:01:46,800 --> 00:01:51,080\n", "The scene shows a man in his mid-30s to 40s with dark hair, dressed in a formal black suit and tie. He is sitting at home on the couch and talking about his flight experience. The background includes some artwork or decorations hanging on the wall behind him.\n", "\n", "24\n", "00:01:51,080 --> 00:01:54,040\n", "The woman in the video is seen talking to someone and then looking at a man who she later talks to. She seems to be in an office setting, possibly with other people around her.\n", "\n", "25\n", "00:01:54,040 --> 00:01:59,440\n", "The man is speaking and the subtitles show his dialogue. The camera zooms in on him as he continues to speak, but there are no other significant actions or events that take place in this scene.\n", "\n", "26\n", "00:01:59,440 --> 00:02:02,200\n", "The scene shows a woman in a black suit and white blouse, with her hair tied back, standing in front of a wooden wall. She is speaking to someone off-camera while looking directly at the camera.\n", "\n", "27\n", "00:02:02,200 --> 00:02:04,480\n", "The man, dressed in a dark suit and tie, engages with the camera while speaking. The setting suggests an interview or a similar interactive session.\n", "\n", "28\n", "00:02:04,480 --> 00:02:06,640\n", "In this scene, a woman is shown speaking to the camera and handing over a room key.\n", "\n", "29\n", "00:02:06,640 --> 00:02:10,560\n", "The man hands over a key to the woman at the reception desk.\n", "\n", "30\n", "00:02:10,560 --> 00:02:20,440\n", "The scene shows a woman in business attire giving instructions to someone off-camera, providing directions on how to get to their room. She is speaking and gesturing with her hands while the person she is addressing stands nearby.\n", "\n", "31\n", "00:02:20,440 --> 00:02:36,360\n", "The scene shows a man and woman in business attire interacting at the hotel reception desk. The man hands over his suitcase to the woman, who takes it from him while they exchange pleasantries. They seem to be discussing something related to checking into the hotel.\n", "\n", "32\n", "00:02:36,360 --> 00:02:40,400\n", "The video begins with a logo on black background. The scene then transitions to the website address of \"linguatv\" in white font, which is shown multiple times throughout the video.\n", "\n", "Description     : The video begins with a receptionist at the front desk of a hotel, dressed in a black suit and white shirt. The scene transitions to an exterior shot of a building before shifting back to the reception area where a man in a dark suit approaches the counter. He engages in conversation with the receptionist, who responds with 'Good morning. Welcome to the Transnational Hotel.' A series of interactions follows as the man checks into the hotel, discussing various amenities and services. Text overlays provide instructions for the guest. Towards the end, he receives his room key and is informed about the bellboy's service. The final scenes show the guest leaving and thanking the staff, concluding the video.\n", "SrtContent      : 1\n", "00:00:08,640 --> 00:00:11,779\n", "Good morning, welcome to the Transnational Hotel. What can I do for you?\n", "\n", "2\n", "00:00:12,000 --> 00:00:15,519\n", "Good morning, my name is <PERSON>. I have a reservation for a single room for three nights.\n", "\n", "3\n", "00:00:15,699 --> 00:00:17,519\n", "Alright Mr. <PERSON>, let me pull up your reservation.\n", "\n", "4\n", "00:00:20,400 --> 00:00:28,760\n", "I can't seem to find a record of your booking. Did you book the room directly through us or do you use a hotel reservation service or a travel agent?\n", "\n", "5\n", "00:00:28,760 --> 00:00:35,659\n", "I booked it directly through you. I've already also paid a deposit on the first night. I have a reservation number if that helps.\n", "\n", "6\n", "00:00:35,899 --> 00:00:38,899\n", "Yeah sure, can I see that please? Thank you.\n", "\n", "7\n", "00:00:40,880 --> 00:00:44,939\n", "Oh I see. Maybe there was a glitch with the booking system.\n", "\n", "8\n", "00:00:45,579 --> 00:00:50,619\n", "Well, we don't have any more single rooms available with the exception of one adjoined room.\n", "\n", "9\n", "00:00:50,820 --> 00:00:54,799\n", "But you would then be right next door to a family with children, which might get noisy.\n", "\n", "10\n", "00:00:55,480 --> 00:00:58,359\n", "But that's not a problem. I can upgrade you to one of our business suites.\n", "\n", "11\n", "00:00:58,500 --> 00:00:58,740\n", "Okay.\n", "\n", "12\n", "00:00:58,759 --> 00:00:59,740\n", "They all come with jacuzzis.\n", "\n", "13\n", "00:00:59,920 --> 00:01:02,740\n", "Oh that sounds nice. But how much more is that going to cost?\n", "\n", "14\n", "00:01:02,939 --> 00:01:04,879\n", "That would of course be at no extra charge to you.\n", "\n", "15\n", "00:01:05,079 --> 00:01:06,200\n", "Oh, well thank you.\n", "\n", "16\n", "00:01:06,379 --> 00:01:06,859\n", "My pleasure.\n", "\n", "17\n", "00:01:07,640 --> 00:01:09,180\n", "What about the wireless internet?\n", "\n", "18\n", "00:01:09,640 --> 00:01:18,379\n", "Oh, it's really easy. This is your access code and instructions on how to use it. If you have any problems, feel free to call the front desk.\n", "\n", "19\n", "00:01:18,920 --> 00:01:23,620\n", "And this is a list of all the hotel amenities, like the gym and the indoor pool.\n", "\n", "20\n", "00:01:23,980 --> 00:01:25,040\n", "Oh, thank you very much.\n", "\n", "21\n", "00:01:25,280 --> 00:01:25,700\n", "You're welcome.\n", "\n", "22\n", "00:01:26,040 --> 00:01:28,739\n", "Has the valet already taken your car or will you be able to use it?\n", "\n", "23\n", "00:01:30,159 --> 00:01:32,599\n", "I don't have a car. I took a taxi direct from the airport.\n", "\n", "24\n", "00:01:32,859 --> 00:01:37,500\n", "Oh, alright. Could I have some form of ID please? And could you just fill out this registration form?\n", "\n", "25\n", "00:01:37,719 --> 00:01:40,840\n", "Sure. Here's my driver's license.\n", "\n", "26\n", "00:01:41,040 --> 00:01:42,780\n", "Thank you. Oh, you're from San Francisco.\n", "\n", "27\n", "00:01:43,060 --> 00:01:44,879\n", "Yes, I am. All the way from the west coast.\n", "\n", "28\n", "00:01:45,219 --> 00:01:46,239\n", "I hope you had a good trip.\n", "\n", "29\n", "00:01:46,820 --> 00:01:51,280\n", "Yes, I did. Thank you. The flight was long, but it was smooth and I slept almost the whole way.\n", "\n", "30\n", "00:01:51,400 --> 00:01:53,200\n", "Oh, and is this your first time in the Big Apple?\n", "\n", "31\n", "00:01:53,799 --> 00:01:58,659\n", "Yes, it is. I have a business conference to attend, but I'm looking forward to getting some sightseeing done.\n", "\n", "32\n", "00:01:58,719 --> 00:01:58,719\n", "\n", "\n", "33\n", "00:01:58,719 --> 00:01:58,719\n", "\n", "\n", "34\n", "00:01:58,719 --> 00:01:58,719\n", "\n", "\n", "35\n", "00:01:58,719 --> 00:01:58,719\n", "\n", "\n", "36\n", "00:01:58,719 --> 00:01:59,099\n", "Oh, I'm looking forward to it.\n", "\n", "37\n", "00:01:59,099 --> 00:02:02,359\n", "Well, I'd be more than happy to give you some sightseeing tips if you need any.\n", "\n", "38\n", "00:02:02,800 --> 00:02:03,400\n", "Thank you.\n", "\n", "39\n", "00:02:03,879 --> 00:02:09,960\n", "Alright, I've got you all checked into your room. This is your room key. You're in room 653.\n", "\n", "40\n", "00:02:10,500 --> 00:02:16,340\n", "Just take the elevator on the right up to the sixth floor. When you get off the elevator, turn right.\n", "\n", "41\n", "00:02:16,460 --> 00:02:22,840\n", "Your room is at the end of the corridor on the left-hand side. Just leave your suitcase here and the bellboy will bring it up.\n", "\n", "42\n", "00:02:23,379 --> 00:02:24,979\n", "Great. Well, thank you very much.\n", "\n", "43\n", "00:02:25,219 --> 00:02:28,319\n", "If you need anything, please feel free to dial the front desk. Enjoy your stay.\n", "\n", "44\n", "00:02:28,719 --> 00:02:28,800\n", "Thank you.\n", "\n", "45\n", "00:02:28,900 --> 00:02:29,419\n", "You're welcome.\n", "\n", "\n", "Transcript      : Good morning, welcome to the Transnational Hotel. What can I do for you? Good morning, my name is <PERSON>. I have a reservation for a single room for three nights. Alright Mr. <PERSON>, let me pull up your reservation. I can't seem to find a record of your booking. Did you book the room directly through us or do you use a hotel reservation service or a travel agent? I booked it directly through you. I've already also paid a deposit on the first night. I have a reservation number if that helps. Yeah sure, can I see that please? Thank you. Oh I see. Maybe there was a glitch with the booking system. Well, we don't have any more single rooms available with the exception of one adjoined room. But you would then be right next door to a family with children, which might get noisy. But that's not a problem. I can upgrade you to one of our business suites. Okay. They all come with jacuzzis. Oh that sounds nice. But how much more is that going to cost? That would of course be at no extra charge to you. Oh, well thank you. My pleasure. What about the wireless internet? Oh, it's really easy. This is your access code and instructions on how to use it. If you have any problems, feel free to call the front desk. And this is a list of all the hotel amenities, like the gym and the indoor pool. Oh, thank you very much. You're welcome. Has the valet already taken your car or will you be able to use it? I don't have a car. I took a taxi direct from the airport. Oh, alright. Could I have some form of ID please? And could you just fill out this registration form? Sure. Here's my driver's license. Thank you. Oh, you're from San Francisco. Yes, I am. All the way from the west coast. I hope you had a good trip. Yes, I did. Thank you. The flight was long, but it was smooth and I slept almost the whole way. Oh, and is this your first time in the Big Apple? Yes, it is. I have a business conference to attend, but I'm looking forward to getting some sightseeing done. Oh, I'm looking forward to it. Well, I'd be more than happy to give you some sightseeing tips if you need any. Thank you. Alright, I've got you all checked into your room. This is your room key. You're in room 653. Just take the elevator on the right up to the sixth floor. When you get off the elevator, turn right. Your room is at the end of the corridor on the left-hand side. Just leave your suitcase here and the bellboy will bring it up. Great. Well, thank you very much. If you need anything, please feel free to dial the front desk. Enjoy your stay. Thank you. You're welcome.\n", "WordTimestamps  : [{'word': ' Good', 'start': 8.640000000000002, 'end': 9.08}, {'word': ' morning,', 'start': 9.08, 'end': 9.38}, {'word': ' welcome', 'start': 9.44, 'end': 9.66}, {'word': ' to', 'start': 9.66, 'end': 9.88}, {'word': ' the', 'start': 9.88, 'end': 10.0}, {'word': ' Transnational', 'start': 10.0, 'end': 10.46}, {'word': ' Hotel.', 'start': 10.46, 'end': 10.78}, {'word': ' What', 'start': 11.02, 'end': 11.18}, {'word': ' can', 'start': 11.18, 'end': 11.3}, {'word': ' I', 'start': 11.3, 'end': 11.4}, {'word': ' do', 'start': 11.4, 'end': 11.52}, {'word': ' for', 'start': 11.52, 'end': 11.68}, {'word': ' you?', 'start': 11.68, 'end': 11.78}, {'word': ' Good', 'start': 12.0, 'end': 12.18}, {'word': ' morning,', 'start': 12.18, 'end': 12.4}, {'word': ' my', 'start': 12.44, 'end': 12.52}, {'word': ' name', 'start': 12.52, 'end': 12.64}, {'word': ' is', 'start': 12.64, 'end': 12.74}, {'word': ' Tom', 'start': 12.74, 'end': 13.0}, {'word': ' Sanders.', 'start': 13.0, 'end': 13.28}, {'word': ' I', 'start': 13.52, 'end': 13.62}, {'word': ' have', 'start': 13.62, 'end': 13.74}, {'word': ' a', 'start': 13.74, 'end': 13.84}, {'word': ' reservation', 'start': 13.84, 'end': 14.14}, {'word': ' for', 'start': 14.14, 'end': 14.48}, {'word': ' a', 'start': 14.48, 'end': 14.58}, {'word': ' single', 'start': 14.58, 'end': 14.8}, {'word': ' room', 'start': 14.8, 'end': 14.96}, {'word': ' for', 'start': 14.96, 'end': 15.1}, {'word': ' three', 'start': 15.1, 'end': 15.28}, {'word': ' nights.', 'start': 15.28, 'end': 15.52}, {'word': ' Alright', 'start': 15.7, 'end': 15.82}, {'word': ' Mr.', 'start': 15.82, 'end': 16.04}, {'word': ' Sanders,', 'start': 16.14, 'end': 16.44}, {'word': ' let', 'start': 16.6, 'end': 16.68}, {'word': ' me', 'start': 16.68, 'end': 16.8}, {'word': ' pull', 'start': 16.8, 'end': 16.96}, {'word': ' up', 'start': 16.96, 'end': 17.12}, {'word': ' your', 'start': 17.12, 'end': 17.24}, {'word': ' reservation.', 'start': 17.24, 'end': 17.52}, {'word': ' I', 'start': 20.400000000000006, 'end': 20.840000000000003}, {'word': \" can't\", 'start': 20.840000000000003, 'end': 21.28}, {'word': ' seem', 'start': 21.28, 'end': 21.52}, {'word': ' to', 'start': 21.52, 'end': 21.72}, {'word': ' find', 'start': 21.72, 'end': 22.06}, {'word': ' a', 'start': 22.06, 'end': 22.22}, {'word': ' record', 'start': 22.22, 'end': 22.62}, {'word': ' of', 'start': 22.62, 'end': 22.82}, {'word': ' your', 'start': 22.82, 'end': 22.96}, {'word': ' booking.', 'start': 22.96, 'end': 23.36}, {'word': ' Did', 'start': 23.7, 'end': 24.12}, {'word': ' you', 'start': 24.12, 'end': 24.34}, {'word': ' book', 'start': 24.34, 'end': 24.52}, {'word': ' the', 'start': 24.52, 'end': 24.68}, {'word': ' room', 'start': 24.68, 'end': 24.82}, {'word': ' directly', 'start': 24.82, 'end': 25.08}, {'word': ' through', 'start': 25.08, 'end': 25.4}, {'word': ' us', 'start': 25.4, 'end': 25.62}, {'word': ' or', 'start': 25.62, 'end': 25.86}, {'word': ' do', 'start': 25.86, 'end': 25.98}, {'word': ' you', 'start': 25.98, 'end': 26.1}, {'word': ' use', 'start': 26.1, 'end': 26.32}, {'word': ' a', 'start': 26.32, 'end': 26.42}, {'word': ' hotel', 'start': 26.42, 'end': 26.68}, {'word': ' reservation', 'start': 26.68, 'end': 27.06}, {'word': ' service', 'start': 27.06, 'end': 27.8}, {'word': ' or', 'start': 27.8, 'end': 28.08}, {'word': ' a', 'start': 28.08, 'end': 28.16}, {'word': ' travel', 'start': 28.16, 'end': 28.4}, {'word': ' agent?', 'start': 28.4, 'end': 28.76}, {'word': ' I', 'start': 28.76, 'end': 29.36}, {'word': ' booked', 'start': 29.36, 'end': 29.62}, {'word': ' it', 'start': 29.62, 'end': 29.76}, {'word': ' directly', 'start': 29.76, 'end': 29.96}, {'word': ' through', 'start': 29.96, 'end': 30.26}, {'word': ' you.', 'start': 30.26, 'end': 30.44}, {'word': \" I've\", 'start': 30.7, 'end': 31.1}, {'word': ' already', 'start': 31.1, 'end': 31.22}, {'word': ' also', 'start': 31.22, 'end': 31.48}, {'word': ' paid', 'start': 31.48, 'end': 31.78}, {'word': ' a', 'start': 31.78, 'end': 31.94}, {'word': ' deposit', 'start': 31.94, 'end': 32.32}, {'word': ' on', 'start': 32.32, 'end': 32.54}, {'word': ' the', 'start': 32.54, 'end': 32.6}, {'word': ' first', 'start': 32.6, 'end': 32.76}, {'word': ' night.', 'start': 32.76, 'end': 32.96}, {'word': ' I', 'start': 33.32, 'end': 33.72}, {'word': ' have', 'start': 33.72, 'end': 33.9}, {'word': ' a', 'start': 33.9, 'end': 34.04}, {'word': ' reservation', 'start': 34.04, 'end': 34.68}, {'word': ' number', 'start': 34.68, 'end': 35.14}, {'word': ' if', 'start': 35.14, 'end': 35.3}, {'word': ' that', 'start': 35.3, 'end': 35.4}, {'word': ' helps.', 'start': 35.4, 'end': 35.66}, {'word': ' Yeah', 'start': 35.9, 'end': 36.12}, {'word': ' sure,', 'start': 36.12, 'end': 36.34}, {'word': ' can', 'start': 36.6, 'end': 36.8}, {'word': ' I', 'start': 36.8, 'end': 36.92}, {'word': ' see', 'start': 36.92, 'end': 37.06}, {'word': ' that', 'start': 37.06, 'end': 37.16}, {'word': ' please?', 'start': 37.16, 'end': 37.34}, {'word': ' Thank', 'start': 38.26, 'end': 38.66}, {'word': ' you.', 'start': 38.66, 'end': 38.9}, {'word': ' Oh', 'start': 40.88, 'end': 41.28}, {'word': ' I', 'start': 41.28, 'end': 41.5}, {'word': ' see.', 'start': 41.5, 'end': 41.96}, {'word': ' Maybe', 'start': 43.06, 'end': 43.46}, {'word': ' there', 'start': 43.46, 'end': 43.66}, {'word': ' was', 'start': 43.66, 'end': 43.8}, {'word': ' a', 'start': 43.8, 'end': 43.9}, {'word': ' glitch', 'start': 43.9, 'end': 44.08}, {'word': ' with', 'start': 44.08, 'end': 44.24}, {'word': ' the', 'start': 44.24, 'end': 44.36}, {'word': ' booking', 'start': 44.36, 'end': 44.58}, {'word': ' system.', 'start': 44.58, 'end': 44.94}, {'word': ' Well,', 'start': 45.58, 'end': 45.98}, {'word': ' we', 'start': 46.2, 'end': 46.42}, {'word': \" don't\", 'start': 46.42, 'end': 46.66}, {'word': ' have', 'start': 46.66, 'end': 46.82}, {'word': ' any', 'start': 46.82, 'end': 47.02}, {'word': ' more', 'start': 47.02, 'end': 47.22}, {'word': ' single', 'start': 47.22, 'end': 47.56}, {'word': ' rooms', 'start': 47.56, 'end': 48.0}, {'word': ' available', 'start': 48.0, 'end': 48.46}, {'word': ' with', 'start': 48.46, 'end': 48.86}, {'word': ' the', 'start': 48.86, 'end': 49.0}, {'word': ' exception', 'start': 49.0, 'end': 49.3}, {'word': ' of', 'start': 49.3, 'end': 49.6}, {'word': ' one', 'start': 49.6, 'end': 49.82}, {'word': ' adjoined', 'start': 49.82, 'end': 50.36}, {'word': ' room.', 'start': 50.36, 'end': 50.62}, {'word': ' But', 'start': 50.82, 'end': 51.22}, {'word': ' you', 'start': 51.22, 'end': 51.32}, {'word': ' would', 'start': 51.32, 'end': 51.42}, {'word': ' then', 'start': 51.42, 'end': 51.56}, {'word': ' be', 'start': 51.56, 'end': 51.7}, {'word': ' right', 'start': 51.7, 'end': 51.86}, {'word': ' next', 'start': 51.86, 'end': 52.06}, {'word': ' door', 'start': 52.06, 'end': 52.26}, {'word': ' to', 'start': 52.26, 'end': 52.4}, {'word': ' a', 'start': 52.4, 'end': 52.5}, {'word': ' family', 'start': 52.5, 'end': 52.78}, {'word': ' with', 'start': 52.78, 'end': 52.96}, {'word': ' children,', 'start': 52.96, 'end': 53.28}, {'word': ' which', 'start': 53.46, 'end': 53.66}, {'word': ' might', 'start': 53.66, 'end': 54.08}, {'word': ' get', 'start': 54.08, 'end': 54.4}, {'word': ' noisy.', 'start': 54.4, 'end': 54.8}, {'word': ' But', 'start': 55.480000000000004, 'end': 55.88}, {'word': \" that's\", 'start': 55.88, 'end': 56.02}, {'word': ' not', 'start': 56.02, 'end': 56.12}, {'word': ' a', 'start': 56.12, 'end': 56.24}, {'word': ' problem.', 'start': 56.24, 'end': 56.48}, {'word': ' I', 'start': 56.58, 'end': 56.74}, {'word': ' can', 'start': 56.74, 'end': 56.88}, {'word': ' upgrade', 'start': 56.88, 'end': 57.12}, {'word': ' you', 'start': 57.12, 'end': 57.3}, {'word': ' to', 'start': 57.3, 'end': 57.38}, {'word': ' one', 'start': 57.38, 'end': 57.5}, {'word': ' of', 'start': 57.5, 'end': 57.58}, {'word': ' our', 'start': 57.58, 'end': 57.7}, {'word': ' business', 'start': 57.7, 'end': 57.96}, {'word': ' suites.', 'start': 57.96, 'end': 58.36}, {'word': ' Okay.', 'start': 58.5, 'end': 58.74}, {'word': ' They', 'start': 58.76, 'end': 58.86}, {'word': ' all', 'start': 58.86, 'end': 58.94}, {'word': ' come', 'start': 58.94, 'end': 59.08}, {'word': ' with', 'start': 59.08, 'end': 59.24}, {'word': ' jacuzzis.', 'start': 59.24, 'end': 59.74}, {'word': ' Oh', 'start': 59.92, 'end': 60.14}, {'word': ' that', 'start': 60.14, 'end': 60.28}, {'word': ' sounds', 'start': 60.28, 'end': 60.5}, {'word': ' nice.', 'start': 60.5, 'end': 60.78}, {'word': ' But', 'start': 61.16, 'end': 61.6}, {'word': ' how', 'start': 61.6, 'end': 61.8}, {'word': ' much', 'start': 61.8, 'end': 61.96}, {'word': ' more', 'start': 61.96, 'end': 62.1}, {'word': ' is', 'start': 62.1, 'end': 62.22}, {'word': ' that', 'start': 62.22, 'end': 62.26}, {'word': ' going', 'start': 62.26, 'end': 62.38}, {'word': ' to', 'start': 62.38, 'end': 62.48}, {'word': ' cost?', 'start': 62.48, 'end': 62.74}, {'word': ' That', 'start': 62.94, 'end': 63.1}, {'word': ' would', 'start': 63.1, 'end': 63.24}, {'word': ' of', 'start': 63.24, 'end': 63.32}, {'word': ' course', 'start': 63.32, 'end': 63.52}, {'word': ' be', 'start': 63.52, 'end': 63.66}, {'word': ' at', 'start': 63.66, 'end': 63.78}, {'word': ' no', 'start': 63.78, 'end': 63.94}, {'word': ' extra', 'start': 63.94, 'end': 64.24}, {'word': ' charge', 'start': 64.24, 'end': 64.54}, {'word': ' to', 'start': 64.54, 'end': 64.72}, {'word': ' you.', 'start': 64.72, 'end': 64.88}, {'word': ' Oh,', 'start': 65.08, 'end': 65.46}, {'word': ' well', 'start': 65.46, 'end': 65.66}, {'word': ' thank', 'start': 65.66, 'end': 66.02}, {'word': ' you.', 'start': 66.02, 'end': 66.2}, {'word': ' My', 'start': 66.38, 'end': 66.62}, {'word': ' pleasure.', 'start': 66.62, 'end': 66.86}, {'word': ' What', 'start': 67.64, 'end': 68.08}, {'word': ' about', 'start': 68.08, 'end': 68.34}, {'word': ' the', 'start': 68.34, 'end': 68.54}, {'word': ' wireless', 'start': 68.54, 'end': 68.84}, {'word': ' internet?', 'start': 68.84, 'end': 69.18}, {'word': ' Oh,', 'start': 69.64, 'end': 70.08}, {'word': \" it's\", 'start': 70.1, 'end': 70.28}, {'word': ' really', 'start': 70.28, 'end': 70.44}, {'word': ' easy.', 'start': 70.44, 'end': 70.76}, {'word': ' This', 'start': 71.48, 'end': 71.92}, {'word': ' is', 'start': 71.92, 'end': 72.14}, {'word': ' your', 'start': 72.14, 'end': 72.42}, {'word': ' access', 'start': 72.42, 'end': 72.92}, {'word': ' code', 'start': 72.92, 'end': 73.32}, {'word': ' and', 'start': 73.32, 'end': 73.68}, {'word': ' instructions', 'start': 73.68, 'end': 74.06}, {'word': ' on', 'start': 74.06, 'end': 74.5}, {'word': ' how', 'start': 74.5, 'end': 74.78}, {'word': ' to', 'start': 74.78, 'end': 74.94}, {'word': ' use', 'start': 74.94, 'end': 75.2}, {'word': ' it.', 'start': 75.2, 'end': 75.42}, {'word': ' If', 'start': 75.54, 'end': 75.74}, {'word': ' you', 'start': 75.74, 'end': 75.82}, {'word': ' have', 'start': 75.82, 'end': 75.92}, {'word': ' any', 'start': 75.92, 'end': 76.22}, {'word': ' problems,', 'start': 76.22, 'end': 76.72}, {'word': ' feel', 'start': 76.98, 'end': 77.16}, {'word': ' free', 'start': 77.16, 'end': 77.4}, {'word': ' to', 'start': 77.4, 'end': 77.54}, {'word': ' call', 'start': 77.54, 'end': 77.7}, {'word': ' the', 'start': 77.7, 'end': 77.84}, {'word': ' front', 'start': 77.84, 'end': 78.04}, {'word': ' desk.', 'start': 78.04, 'end': 78.38}, {'word': ' And', 'start': 78.92, 'end': 79.36}, {'word': ' this', 'start': 79.36, 'end': 79.8}, {'word': ' is', 'start': 79.8, 'end': 80.18}, {'word': ' a', 'start': 80.18, 'end': 80.5}, {'word': ' list', 'start': 80.5, 'end': 80.72}, {'word': ' of', 'start': 80.72, 'end': 80.84}, {'word': ' all', 'start': 80.84, 'end': 80.96}, {'word': ' the', 'start': 80.96, 'end': 81.1}, {'word': ' hotel', 'start': 81.1, 'end': 81.3}, {'word': ' amenities,', 'start': 81.3, 'end': 81.82}, {'word': ' like', 'start': 82.08, 'end': 82.52}, {'word': ' the', 'start': 82.52, 'end': 82.7}, {'word': ' gym', 'start': 82.7, 'end': 82.88}, {'word': ' and', 'start': 82.88, 'end': 83.02}, {'word': ' the', 'start': 83.02, 'end': 83.14}, {'word': ' indoor', 'start': 83.14, 'end': 83.32}, {'word': ' pool.', 'start': 83.32, 'end': 83.62}, {'word': ' Oh,', 'start': 83.98, 'end': 84.2}, {'word': ' thank', 'start': 84.24, 'end': 84.5}, {'word': ' you', 'start': 84.5, 'end': 84.68}, {'word': ' very', 'start': 84.68, 'end': 84.8}, {'word': ' much.', 'start': 84.8, 'end': 85.04}, {'word': \" You're\", 'start': 85.28, 'end': 85.48}, {'word': ' welcome.', 'start': 85.48, 'end': 85.7}, {'word': ' Has', 'start': 86.04, 'end': 86.48}, {'word': ' the', 'start': 86.48, 'end': 86.72}, {'word': ' valet', 'start': 86.72, 'end': 87.02}, {'word': ' already', 'start': 87.02, 'end': 87.22}, {'word': ' taken', 'start': 87.22, 'end': 87.6}, {'word': ' your', 'start': 87.6, 'end': 87.84}, {'word': ' car', 'start': 87.84, 'end': 88.14}, {'word': ' or', 'start': 88.14, 'end': 88.5}, {'word': ' will', 'start': 88.5, 'end': 88.62}, {'word': ' you', 'start': 88.62, 'end': 88.74}, {'word': ' be', 'start': 88.74, 'end': 88.74}, {'word': ' able', 'start': 88.74, 'end': 88.74}, {'word': ' to', 'start': 88.74, 'end': 88.74}, {'word': ' use', 'start': 88.74, 'end': 88.74}, {'word': ' it?', 'start': 88.74, 'end': 88.74}, {'word': ' I', 'start': 90.16, 'end': 90.52}, {'word': \" don't\", 'start': 90.52, 'end': 90.88}, {'word': ' have', 'start': 90.88, 'end': 91.0}, {'word': ' a', 'start': 91.0, 'end': 91.16}, {'word': ' car.', 'start': 91.16, 'end': 91.34}, {'word': ' I', 'start': 91.38, 'end': 91.46}, {'word': ' took', 'start': 91.46, 'end': 91.58}, {'word': ' a', 'start': 91.58, 'end': 91.68}, {'word': ' taxi', 'start': 91.68, 'end': 91.9}, {'word': ' direct', 'start': 91.9, 'end': 92.12}, {'word': ' from', 'start': 92.12, 'end': 92.26}, {'word': ' the', 'start': 92.26, 'end': 92.36}, {'word': ' airport.', 'start': 92.36, 'end': 92.6}, {'word': ' Oh,', 'start': 92.86, 'end': 92.96}, {'word': ' alright.', 'start': 93.02, 'end': 93.12}, {'word': ' Could', 'start': 93.42, 'end': 93.78}, {'word': ' I', 'start': 93.78, 'end': 93.88}, {'word': ' have', 'start': 93.88, 'end': 94.04}, {'word': ' some', 'start': 94.04, 'end': 94.24}, {'word': ' form', 'start': 94.24, 'end': 94.44}, {'word': ' of', 'start': 94.44, 'end': 94.62}, {'word': ' ID', 'start': 94.62, 'end': 94.8}, {'word': ' please?', 'start': 94.8, 'end': 95.16}, {'word': ' And', 'start': 95.3, 'end': 95.52}, {'word': ' could', 'start': 95.52, 'end': 95.68}, {'word': ' you', 'start': 95.68, 'end': 95.86}, {'word': ' just', 'start': 95.86, 'end': 96.12}, {'word': ' fill', 'start': 96.12, 'end': 96.34}, {'word': ' out', 'start': 96.34, 'end': 96.46}, {'word': ' this', 'start': 96.46, 'end': 96.66}, {'word': ' registration', 'start': 96.66, 'end': 97.1}, {'word': ' form?', 'start': 97.1, 'end': 97.5}, {'word': ' Sure.', 'start': 97.72, 'end': 98.04}, {'word': \" Here's\", 'start': 99.78, 'end': 100.14}, {'word': ' my', 'start': 100.14, 'end': 100.28}, {'word': \" driver's\", 'start': 100.28, 'end': 100.66}, {'word': ' license.', 'start': 100.66, 'end': 100.84}, {'word': ' Thank', 'start': 101.04, 'end': 101.18}, {'word': ' you.', 'start': 101.18, 'end': 101.36}, {'word': ' Oh,', 'start': 101.62, 'end': 101.98}, {'word': \" you're\", 'start': 102.0, 'end': 102.1}, {'word': ' from', 'start': 102.1, 'end': 102.22}, {'word': ' San', 'start': 102.22, 'end': 102.36}, {'word': ' Francisco.', 'start': 102.36, 'end': 102.78}, {'word': ' Yes,', 'start': 103.06, 'end': 103.38}, {'word': ' I', 'start': 103.38, 'end': 103.52}, {'word': ' am.', 'start': 103.52, 'end': 103.7}, {'word': ' All', 'start': 103.74, 'end': 103.86}, {'word': ' the', 'start': 103.86, 'end': 103.96}, {'word': ' way', 'start': 103.96, 'end': 104.04}, {'word': ' from', 'start': 104.04, 'end': 104.22}, {'word': ' the', 'start': 104.22, 'end': 104.4}, {'word': ' west', 'start': 104.4, 'end': 104.58}, {'word': ' coast.', 'start': 104.58, 'end': 104.88}, {'word': ' I', 'start': 105.22, 'end': 105.38}, {'word': ' hope', 'start': 105.38, 'end': 105.56}, {'word': ' you', 'start': 105.56, 'end': 105.64}, {'word': ' had', 'start': 105.64, 'end': 105.78}, {'word': ' a', 'start': 105.78, 'end': 105.86}, {'word': ' good', 'start': 105.86, 'end': 105.98}, {'word': ' trip.', 'start': 105.98, 'end': 106.24}, {'word': ' Yes,', 'start': 106.82000000000001, 'end': 107.18}, {'word': ' I', 'start': 107.18, 'end': 107.34}, {'word': ' did.', 'start': 107.34, 'end': 107.56}, {'word': ' Thank', 'start': 107.64, 'end': 107.84}, {'word': ' you.', 'start': 107.84, 'end': 108.0}, {'word': ' The', 'start': 108.08, 'end': 108.18}, {'word': ' flight', 'start': 108.18, 'end': 108.54}, {'word': ' was', 'start': 108.54, 'end': 108.72}, {'word': ' long,', 'start': 108.72, 'end': 109.16}, {'word': ' but', 'start': 109.18, 'end': 109.46}, {'word': ' it', 'start': 109.46, 'end': 109.58}, {'word': ' was', 'start': 109.58, 'end': 109.62}, {'word': ' smooth', 'start': 109.62, 'end': 109.98}, {'word': ' and', 'start': 109.98, 'end': 110.18}, {'word': ' I', 'start': 110.18, 'end': 110.26}, {'word': ' slept', 'start': 110.26, 'end': 110.48}, {'word': ' almost', 'start': 110.48, 'end': 110.74}, {'word': ' the', 'start': 110.74, 'end': 110.92}, {'word': ' whole', 'start': 110.92, 'end': 111.08}, {'word': ' way.', 'start': 111.08, 'end': 111.28}, {'word': ' Oh,', 'start': 111.4, 'end': 111.68}, {'word': ' and', 'start': 111.68, 'end': 111.76}, {'word': ' is', 'start': 111.76, 'end': 111.88}, {'word': ' this', 'start': 111.88, 'end': 112.0}, {'word': ' your', 'start': 112.0, 'end': 112.12}, {'word': ' first', 'start': 112.12, 'end': 112.32}, {'word': ' time', 'start': 112.32, 'end': 112.54}, {'word': ' in', 'start': 112.54, 'end': 112.68}, {'word': ' the', 'start': 112.68, 'end': 112.74}, {'word': ' Big', 'start': 112.74, 'end': 112.9}, {'word': ' Apple?', 'start': 112.9, 'end': 113.2}, {'word': ' Yes,', 'start': 113.8, 'end': 114.16}, {'word': ' it', 'start': 114.2, 'end': 114.38}, {'word': ' is.', 'start': 114.38, 'end': 114.56}, {'word': ' I', 'start': 114.86, 'end': 115.22}, {'word': ' have', 'start': 115.22, 'end': 115.36}, {'word': ' a', 'start': 115.36, 'end': 115.46}, {'word': ' business', 'start': 115.46, 'end': 115.68}, {'word': ' conference', 'start': 115.68, 'end': 116.0}, {'word': ' to', 'start': 116.0, 'end': 116.28}, {'word': ' attend,', 'start': 116.28, 'end': 116.5}, {'word': ' but', 'start': 116.76, 'end': 117.16}, {'word': \" I'm\", 'start': 117.16, 'end': 117.34}, {'word': ' looking', 'start': 117.34, 'end': 117.42}, {'word': ' forward', 'start': 117.42, 'end': 117.64}, {'word': ' to', 'start': 117.64, 'end': 117.74}, {'word': ' getting', 'start': 117.74, 'end': 117.84}, {'word': ' some', 'start': 117.84, 'end': 118.06}, {'word': ' sightseeing', 'start': 118.06, 'end': 118.48}, {'word': ' done.', 'start': 118.48, 'end': 118.66}, {'word': ' Oh,', 'start': 118.72, 'end': 119.1}, {'word': \" I'm\", 'start': 119.1, 'end': 119.1}, {'word': ' looking', 'start': 119.1, 'end': 119.1}, {'word': ' forward', 'start': 119.1, 'end': 119.1}, {'word': ' to', 'start': 119.1, 'end': 119.1}, {'word': ' it.', 'start': 119.1, 'end': 119.1}, {'word': ' Well,', 'start': 119.1, 'end': 119.34}, {'word': \" I'd\", 'start': 119.52, 'end': 119.7}, {'word': ' be', 'start': 119.7, 'end': 119.84}, {'word': ' more', 'start': 119.84, 'end': 120.08}, {'word': ' than', 'start': 120.08, 'end': 120.22}, {'word': ' happy', 'start': 120.22, 'end': 120.48}, {'word': ' to', 'start': 120.48, 'end': 120.64}, {'word': ' give', 'start': 120.64, 'end': 120.78}, {'word': ' you', 'start': 120.78, 'end': 120.9}, {'word': ' some', 'start': 120.9, 'end': 121.04}, {'word': ' sightseeing', 'start': 121.04, 'end': 121.48}, {'word': ' tips', 'start': 121.48, 'end': 121.7}, {'word': ' if', 'start': 121.7, 'end': 121.92}, {'word': ' you', 'start': 121.92, 'end': 121.98}, {'word': ' need', 'start': 121.98, 'end': 122.14}, {'word': ' any.', 'start': 122.14, 'end': 122.36}, {'word': ' Thank', 'start': 122.80000000000001, 'end': 123.2}, {'word': ' you.', 'start': 123.2, 'end': 123.4}, {'word': ' Alright,', 'start': 123.88, 'end': 124.28}, {'word': \" I've\", 'start': 124.44, 'end': 124.6}, {'word': ' got', 'start': 124.6, 'end': 124.74}, {'word': ' you', 'start': 124.74, 'end': 124.84}, {'word': ' all', 'start': 124.84, 'end': 125.02}, {'word': ' checked', 'start': 125.02, 'end': 125.24}, {'word': ' into', 'start': 125.24, 'end': 125.44}, {'word': ' your', 'start': 125.44, 'end': 125.64}, {'word': ' room.', 'start': 125.64, 'end': 125.94}, {'word': ' This', 'start': 126.3, 'end': 126.7}, {'word': ' is', 'start': 126.7, 'end': 126.92}, {'word': ' your', 'start': 126.92, 'end': 127.2}, {'word': ' room', 'start': 127.2, 'end': 127.44}, {'word': ' key.', 'start': 127.44, 'end': 127.66}, {'word': \" You're\", 'start': 127.82, 'end': 128.12}, {'word': ' in', 'start': 128.12, 'end': 128.28}, {'word': ' room', 'start': 128.28, 'end': 128.56}, {'word': ' 653.', 'start': 128.56, 'end': 129.96}, {'word': ' Just', 'start': 130.5, 'end': 130.9}, {'word': ' take', 'start': 130.9, 'end': 131.08}, {'word': ' the', 'start': 131.08, 'end': 131.24}, {'word': ' elevator', 'start': 131.24, 'end': 131.68}, {'word': ' on', 'start': 131.68, 'end': 132.06}, {'word': ' the', 'start': 132.06, 'end': 132.28}, {'word': ' right', 'start': 132.28, 'end': 132.56}, {'word': ' up', 'start': 132.56, 'end': 132.9}, {'word': ' to', 'start': 132.9, 'end': 133.04}, {'word': ' the', 'start': 133.04, 'end': 133.16}, {'word': ' sixth', 'start': 133.16, 'end': 133.46}, {'word': ' floor.', 'start': 133.46, 'end': 133.82}, {'word': ' When', 'start': 134.32, 'end': 134.72}, {'word': ' you', 'start': 134.72, 'end': 134.9}, {'word': ' get', 'start': 134.9, 'end': 135.02}, {'word': ' off', 'start': 135.02, 'end': 135.16}, {'word': ' the', 'start': 135.16, 'end': 135.28}, {'word': ' elevator,', 'start': 135.28, 'end': 135.6}, {'word': ' turn', 'start': 135.7, 'end': 135.92}, {'word': ' right.', 'start': 135.92, 'end': 136.34}, {'word': ' Your', 'start': 136.46, 'end': 136.74}, {'word': ' room', 'start': 136.74, 'end': 137.22}, {'word': ' is', 'start': 137.22, 'end': 137.54}, {'word': ' at', 'start': 137.54, 'end': 137.74}, {'word': ' the', 'start': 137.74, 'end': 138.06}, {'word': ' end', 'start': 138.06, 'end': 138.36}, {'word': ' of', 'start': 138.36, 'end': 138.46}, {'word': ' the', 'start': 138.46, 'end': 138.54}, {'word': ' corridor', 'start': 138.54, 'end': 138.8}, {'word': ' on', 'start': 138.8, 'end': 139.06}, {'word': ' the', 'start': 139.06, 'end': 139.18}, {'word': ' left', 'start': 139.18, 'end': 139.52}, {'word': '-hand', 'start': 139.52, 'end': 139.72}, {'word': ' side.', 'start': 139.72, 'end': 140.12}, {'word': ' Just', 'start': 140.42, 'end': 140.68}, {'word': ' leave', 'start': 140.68, 'end': 140.84}, {'word': ' your', 'start': 140.84, 'end': 140.98}, {'word': ' suitcase', 'start': 140.98, 'end': 141.26}, {'word': ' here', 'start': 141.26, 'end': 141.7}, {'word': ' and', 'start': 141.7, 'end': 141.92}, {'word': ' the', 'start': 141.92, 'end': 142.02}, {'word': ' bellboy', 'start': 142.02, 'end': 142.28}, {'word': ' will', 'start': 142.28, 'end': 142.4}, {'word': ' bring', 'start': 142.4, 'end': 142.58}, {'word': ' it', 'start': 142.58, 'end': 142.7}, {'word': ' up.', 'start': 142.7, 'end': 142.84}, {'word': ' Great.', 'start': 143.38, 'end': 143.78}, {'word': ' Well,', 'start': 143.92, 'end': 144.16}, {'word': ' thank', 'start': 144.2, 'end': 144.42}, {'word': ' you', 'start': 144.42, 'end': 144.58}, {'word': ' very', 'start': 144.58, 'end': 144.72}, {'word': ' much.', 'start': 144.72, 'end': 144.98}, {'word': ' If', 'start': 145.22, 'end': 145.4}, {'word': ' you', 'start': 145.4, 'end': 145.52}, {'word': ' need', 'start': 145.52, 'end': 145.7}, {'word': ' anything,', 'start': 145.7, 'end': 145.94}, {'word': ' please', 'start': 146.1, 'end': 146.28}, {'word': ' feel', 'start': 146.28, 'end': 146.44}, {'word': ' free', 'start': 146.44, 'end': 146.62}, {'word': ' to', 'start': 146.62, 'end': 146.78}, {'word': ' dial', 'start': 146.78, 'end': 146.94}, {'word': ' the', 'start': 146.94, 'end': 147.08}, {'word': ' front', 'start': 147.08, 'end': 147.24}, {'word': ' desk.', 'start': 147.24, 'end': 147.56}, {'word': ' Enjoy', 'start': 147.72, 'end': 148.04}, {'word': ' your', 'start': 148.04, 'end': 148.16}, {'word': ' stay.', 'start': 148.16, 'end': 148.32}, {'word': ' Thank', 'start': 148.72, 'end': 148.8}, {'word': ' you.', 'start': 148.8, 'end': 148.8}, {'word': \" You're\", 'start': 148.9, 'end': 149.2}, {'word': ' welcome.', 'start': 149.2, 'end': 149.42}]\n", "AudioTags       : [['Speech', 0.8283295631408691], ['Music', 0.7810096740722656], ['Ding-dong', 0.07357385754585266], ['Skateboard', 0.047408051788806915], ['Chink, clink', 0.03378196433186531]]\n", "Diarization     : [{'start': 9.025343750000001, 'end': 12.04596875, 'speaker': 'SPEAKER_01'}, {'start': 12.09659375, 'end': 15.67409375, 'speaker': 'SPEAKER_00'}, {'start': 15.640343750000003, 'end': 18.08721875, 'speaker': 'SPEAKER_01'}, {'start': 20.73659375, 'end': 29.190968750000003, 'speaker': 'SPEAKER_01'}, {'start': 29.29221875, 'end': 35.94096875, 'speaker': 'SPEAKER_00'}, {'start': 35.99159375, 'end': 37.72971875, 'speaker': 'SPEAKER_01'}, {'start': 38.57346875, 'end': 39.33284375, 'speaker': 'SPEAKER_01'}, {'start': 41.23971875, 'end': 60.02159375, 'speaker': 'SPEAKER_01'}, {'start': 60.08909375, 'end': 62.95784375, 'speaker': 'SPEAKER_00'}, {'start': 62.95784375, 'end': 65.11784375, 'speaker': 'SPEAKER_01'}, {'start': 65.28659375000001, 'end': 66.46784375, 'speaker': 'SPEAKER_00'}, {'start': 66.46784375, 'end': 67.26096875, 'speaker': 'SPEAKER_01'}, {'start': 67.91909375, 'end': 69.64034375, 'speaker': 'SPEAKER_00'}, {'start': 69.99471875, 'end': 84.00096875, 'speaker': 'SPEAKER_01'}, {'start': 84.10221875, 'end': 85.36784375, 'speaker': 'SPEAKER_00'}, {'start': 85.36784375, 'end': 90.37971875000001, 'speaker': 'SPEAKER_01'}, {'start': 90.41346875, 'end': 92.94471875, 'speaker': 'SPEAKER_00'}, {'start': 92.87721875000001, 'end': 98.00721875, 'speaker': 'SPEAKER_01'}, {'start': 98.00721875, 'end': 101.55096875000001, 'speaker': 'SPEAKER_00'}, {'start': 101.55096875000001, 'end': 103.20471875000001, 'speaker': 'SPEAKER_01'}, {'start': 103.18784375000001, 'end': 105.26346875, 'speaker': 'SPEAKER_00'}, {'start': 105.33096875000001, 'end': 106.57971875000001, 'speaker': 'SPEAKER_01'}, {'start': 106.98471875000001, 'end': 111.50721875, 'speaker': 'SPEAKER_00'}, {'start': 111.55784375, 'end': 113.51534375000001, 'speaker': 'SPEAKER_01'}, {'start': 113.97096875000001, 'end': 119.18534375, 'speaker': 'SPEAKER_00'}, {'start': 119.42159375000001, 'end': 122.62784375000001, 'speaker': 'SPEAKER_01'}, {'start': 123.18471875, 'end': 123.**************, 'speaker': 'SPEAKER_00'}, {'start': 124.**************, 'end': 143.********, 'speaker': 'SPEAKER_01'}, {'start': 143.**************, 'end': 145.**************, 'speaker': 'SPEAKER_00'}, {'start': 145.**************, 'end': 149.********, 'speaker': 'SPEAKER_01'}, {'start': 148.********, 'end': 149.********, 'speaker': 'SPEAKER_00'}]\n", "Relevance Score : 0.2048\n", "=================================\n", "\n", "========== Result 3 ==========\n", "VideoPath       : https://strorageaccount123.blob.core.windows.net/videosearch/eb0cc947-d5ba-498d-959a-a97733186ae3.mp4\n", "SrtContentObjects: 1\n", "00:00:00,000 --> 00:00:22,420\n", "In this scene, two men are seated at a wooden table. One man is speaking and gesturing with his hands while the other listens attentively. The speaker seems to be explaining something important or giving instructions.\n", "\n", "2\n", "00:00:22,420 --> 00:00:25,740\n", "The video begins with a white screen that transitions to display the Unnani logo. The logo is presented in blue and white, featuring stylized human figures holding hands above the word \"unnanu.\"\n", "\n", "3\n", "00:00:25,740 --> 00:10:40,920\n", "The two men are seated at a table, engaged in conversation. They use hand gestures to emphasize their points and smile while talking.\n", "\n", "4\n", "00:10:40,920 --> 00:12:07,220\n", "In the video, a man is seen sitting at a table in front of an Apple laptop. He appears to be working on something and occasionally talks into the camera while gesturing with his hands.\n", "\n", "5\n", "00:12:07,220 --> 00:16:51,060\n", "The man in the black shirt begins speaking, and then the other man puts his hands on the table. The second man also starts to speak as he listens attentively to the first man.\n", "\n", "Description     : The video depicts a meeting between two men seated at a wooden table in an office setting. One man is wearing glasses and a black polo shirt, while the other has gray hair and also wears a black polo shirt. A smartphone rests on the table between them. The background features blue walls with white trim and a large whiteboard mounted on one of the walls. At various points, the men engage in conversation using hand gestures to emphasize their points. They occasionally look directly at each other or towards the camera. Additionally, there are instances where they use laptops placed on the table.\n", "SrtContent      : 1\n", "00:00:00,000 --> 00:00:02,620\n", "If you're ignoring AI, you might be put out of work.\n", "\n", "2\n", "00:00:02,859 --> 00:00:07,620\n", "I don't care what your history, I don't care what your grades, your maybe scholar, PhD.\n", "\n", "3\n", "00:00:08,880 --> 00:00:11,699\n", "Are you solving a real business problem?\n", "\n", "4\n", "00:00:12,279 --> 00:00:16,420\n", "Every time I had a job within three weeks and I barely made an outbound call\n", "\n", "5\n", "00:00:16,420 --> 00:00:19,039\n", "and people said, well, how, how do you get that?\n", "\n", "6\n", "00:00:19,100 --> 00:00:19,980\n", "And it was a better job.\n", "\n", "7\n", "00:00:20,079 --> 00:00:21,260\n", "They go, you get laid off up.\n", "\n", "8\n", "00:00:21,379 --> 00:00:22,160\n", "How does that happen?\n", "\n", "9\n", "00:00:23,400 --> 00:00:25,239\n", "Okay, welcome back to the Ignorant Podcast.\n", "\n", "10\n", "00:00:25,679 --> 00:00:27,339\n", "We're here with <PERSON><PERSON> and <PERSON>.\n", "\n", "11\n", "00:00:27,760 --> 00:00:28,699\n", "They use AI.\n", "\n", "12\n", "00:00:28,699 --> 00:00:33,140\n", "So basically the idea was if you're ignoring AI, you might be put out of work.\n", "\n", "13\n", "00:00:33,259 --> 00:00:36,799\n", "But if you're embracing it and using it and realizing how to morph where your skill set is,\n", "\n", "14\n", "00:00:37,539 --> 00:00:38,560\n", "there'll be room for you.\n", "\n", "15\n", "00:00:38,560 --> 00:00:38,799\n", "Compliments.\n", "\n", "16\n", "00:00:39,000 --> 00:00:39,399\n", "Yeah.\n", "\n", "17\n", "00:00:39,960 --> 00:00:41,780\n", "It's a tool you got to get in.\n", "\n", "18\n", "00:00:41,960 --> 00:00:44,159\n", "I think people has misunderstood AI.\n", "\n", "19\n", "00:00:45,500 --> 00:00:53,240\n", "The education part of this, how you are adaptable, how you are open to new technologies.\n", "\n", "20\n", "00:00:53,439 --> 00:00:58,179\n", "I think a lot of people are very hesitant because part of this,\n", "\n", "21\n", "00:00:58,179 --> 00:00:59,039\n", "you know, AI is what are you here?\n", "\n", "22\n", "00:01:00,140 --> 00:01:03,119\n", "Somebody's writing, don't know what exactly I can do.\n", "\n", "23\n", "00:01:03,979 --> 00:01:10,640\n", "And also we have a lot of distraction with all these models and there's a dark fight out there.\n", "\n", "24\n", "00:01:10,879 --> 00:01:12,519\n", "So, so in technology.\n", "\n", "25\n", "00:01:12,840 --> 00:01:16,299\n", "So what is the other question you have?\n", "\n", "26\n", "00:01:16,519 --> 00:01:17,640\n", "I have a question for you <PERSON><PERSON>.\n", "\n", "27\n", "00:01:17,760 --> 00:01:23,319\n", "It's like with that, because, you know, AI is changing the job market and the job, like, you know, scope.\n", "\n", "28\n", "00:01:23,480 --> 00:01:27,840\n", "Where do you kind of see, like if people are still getting laid off, obviously that's still very scary.\n", "\n", "29\n", "00:01:27,859 --> 00:01:28,140\n", "I suppose.\n", "\n", "30\n", "00:01:28,140 --> 00:01:30,659\n", "So Bumble, which is an Austin company, that was like a 30%.\n", "\n", "31\n", "00:01:31,200 --> 00:01:37,180\n", "So if, you know, we're moving away from certain jobs, what jobs do you kind of see opening or what do you kind of see in the job market?\n", "\n", "32\n", "00:01:37,900 --> 00:01:39,819\n", "You have been involved in our meetings.\n", "\n", "33\n", "00:01:39,859 --> 00:01:45,780\n", "We welcome interns to come in to our companies and see the difference, what we see.\n", "\n", "34\n", "00:01:47,620 --> 00:01:50,159\n", "The, I don't care your resume anymore.\n", "\n", "35\n", "00:01:50,960 --> 00:01:55,340\n", "I mean, I saw an article this morning, how to write a resume here is a tool.\n", "\n", "36\n", "00:01:55,560 --> 00:01:56,739\n", "Stop doing that.\n", "\n", "37\n", "00:01:58,480 --> 00:01:59,019\n", "So I'm going to ask you a question.\n", "\n", "38\n", "00:01:59,019 --> 00:02:03,180\n", "So tell the world, tell me, go back to the cover letter, right?\n", "\n", "39\n", "00:02:03,260 --> 00:02:04,640\n", "Give me your cover letter.\n", "\n", "40\n", "00:02:04,680 --> 00:02:09,340\n", "In one paragraph, there is a project you have done can help me.\n", "\n", "41\n", "00:02:09,460 --> 00:02:09,759\n", "Right.\n", "\n", "42\n", "00:02:10,280 --> 00:02:13,319\n", "And I can, next day you have a job.\n", "\n", "43\n", "00:02:13,939 --> 00:02:15,699\n", "I don't care about industry.\n", "\n", "44\n", "00:02:15,900 --> 00:02:17,259\n", "I don't care about your grades.\n", "\n", "45\n", "00:02:17,460 --> 00:02:18,819\n", "You're maybe a scholar, PhD.\n", "\n", "46\n", "00:02:20,039 --> 00:02:22,919\n", "Are you solving a real business problem?\n", "\n", "47\n", "00:02:23,500 --> 00:02:27,840\n", "Are you helping my company and companies are trying to.\n", "\n", "48\n", "00:02:27,840 --> 00:02:28,400\n", "I hire you.\n", "\n", "49\n", "00:02:29,219 --> 00:02:30,659\n", "Why do we need to hire you?\n", "\n", "50\n", "00:02:30,699 --> 00:02:31,560\n", "You need to justify.\n", "\n", "51\n", "00:02:32,380 --> 00:02:33,979\n", "What do you bring it to the table?\n", "\n", "52\n", "00:02:35,100 --> 00:02:38,060\n", "Are you open to learning and creating solutions?\n", "\n", "53\n", "00:02:38,560 --> 00:02:40,080\n", "I think we are already creative all now.\n", "\n", "54\n", "00:02:40,319 --> 00:02:40,939\n", "Oh, for sure.\n", "\n", "55\n", "00:02:41,039 --> 00:02:41,319\n", "That clear.\n", "\n", "56\n", "00:02:41,620 --> 00:02:42,039\n", "For sure.\n", "\n", "57\n", "00:02:42,419 --> 00:02:47,319\n", "It's a paper trail work days gone that can replace the AI.\n", "\n", "58\n", "00:02:48,240 --> 00:02:56,419\n", "Where our, we see is, yeah, he's a, I know it's hard to say, but everybody has talent.\n", "\n", "59\n", "00:02:57,220 --> 00:02:58,300\n", "But I don't think so.\n", "\n", "60\n", "00:02:58,319 --> 00:02:59,039\n", "They use it.\n", "\n", "61\n", "00:02:59,039 --> 00:03:04,219\n", "Well, they get distracted with, oh, I need to get framed resume.\n", "\n", "62\n", "00:03:04,439 --> 00:03:06,620\n", "I need to have keywords in a resume.\n", "\n", "63\n", "00:03:06,819 --> 00:03:07,020\n", "Right.\n", "\n", "64\n", "00:03:07,159 --> 00:03:12,460\n", "We are in that space where like, okay, give me a one project you delivered out there.\n", "\n", "65\n", "00:03:12,500 --> 00:03:19,039\n", "You put it out there and where I can look at it and see if it's giving any value to us.\n", "\n", "66\n", "00:03:19,099 --> 00:03:20,319\n", "Oh, I didn't think about it.\n", "\n", "67\n", "00:03:20,379 --> 00:03:21,740\n", "How many times we talk in a meeting?\n", "\n", "68\n", "00:03:21,860 --> 00:03:23,259\n", "Oh, I didn't think about this.\n", "\n", "69\n", "00:03:23,660 --> 00:03:26,060\n", "That's what I expect in new generation is they have.\n", "\n", "70\n", "00:03:26,879 --> 00:03:27,680\n", "Amazing talent.\n", "\n", "71\n", "00:03:27,879 --> 00:03:30,879\n", "I think they have their last focus.\n", "\n", "72\n", "00:03:31,300 --> 00:03:36,659\n", "Well, and the other thing is, is that with the job market, the way it is now, your reputation,\n", "\n", "73\n", "00:03:36,860 --> 00:03:40,960\n", "your brand and your network as an individual are going to be more important than ever.\n", "\n", "74\n", "00:03:41,039 --> 00:03:46,280\n", "And part of that is how do you tell a particular employer what the projects you've worked on and done it.\n", "\n", "75\n", "00:03:46,340 --> 00:03:50,819\n", "But also as the employer, if I call you and say, Hey, you need to meet <PERSON>.\n", "\n", "76\n", "00:03:51,479 --> 00:03:53,139\n", "He's perfect for what you're working on.\n", "\n", "77\n", "00:03:53,180 --> 00:03:55,819\n", "You're more apt to talk to him and possibly hiring.\n", "\n", "78\n", "00:03:56,419 --> 00:04:07,159\n", "And yet the younger generation is more apt not to go to networking events, not to join organizations, not to really go out even socialize with people in their industry.\n", "\n", "79\n", "00:04:07,219 --> 00:04:09,080\n", "They don't go to national conferences in their industry.\n", "\n", "80\n", "00:04:09,400 --> 00:04:13,419\n", "And then they are like, oh, well, everybody gets their job through their network, but I don't have a network.\n", "\n", "81\n", "00:04:13,599 --> 00:04:15,340\n", "And it's like, well, you have to create that yourself.\n", "\n", "82\n", "00:04:15,520 --> 00:04:18,420\n", "And I call it human interaction, <PERSON><PERSON><PERSON>.\n", "\n", "83\n", "00:04:18,699 --> 00:04:25,860\n", "And I talk about the fact that <PERSON><PERSON><PERSON><PERSON> is more important in the world than any time before, because anybody can generate a resume.\n", "\n", "84\n", "00:04:26,420 --> 00:04:26,519\n", "Yeah.\n", "\n", "85\n", "00:04:26,660 --> 00:04:28,379\n", "You know, anyone can generate a resume.\n", "\n", "86\n", "00:04:28,439 --> 00:04:33,939\n", "Now, if you can't write a resume using an A.I. tool in five minutes, then, you know, shame on you.\n", "\n", "87\n", "00:04:34,639 --> 00:04:37,480\n", "But how do you get that resume in front of a hiring manager?\n", "\n", "88\n", "00:04:37,620 --> 00:04:40,339\n", "How do you get that resume, you know, in front of people?\n", "\n", "89\n", "00:04:40,379 --> 00:04:45,860\n", "And how do you know that when a job is open, someone's like, oh, my God, I just heard that <PERSON> got laid off.\n", "\n", "90\n", "00:04:46,259 --> 00:04:46,980\n", "She's fantastic.\n", "\n", "91\n", "00:04:47,319 --> 00:04:48,160\n", "Let's hire her.\n", "\n", "92\n", "00:04:48,319 --> 00:04:48,660\n", "Yeah.\n", "\n", "93\n", "00:04:48,720 --> 00:04:53,459\n", "Earlier in my career, I got laid off three times because of companies, not anything I did.\n", "\n", "94\n", "00:04:53,959 --> 00:04:55,980\n", "Companies that either entirely went out of business.\n", "\n", "95\n", "00:04:56,019 --> 00:04:56,319\n", "Yeah.\n", "\n", "96\n", "00:04:56,420 --> 00:04:57,060\n", "Or left Austin.\n", "\n", "97\n", "00:04:57,199 --> 00:04:57,300\n", "Yeah.\n", "\n", "98\n", "00:04:57,420 --> 00:05:03,579\n", "And every time I had a job within three weeks and I barely made an outbound call and people said, well, how?\n", "\n", "99\n", "00:05:03,759 --> 00:05:04,560\n", "How do you get that?\n", "\n", "100\n", "00:05:04,600 --> 00:05:05,540\n", "And it was a better job.\n", "\n", "101\n", "00:05:05,579 --> 00:05:06,740\n", "They go, you get laid off up.\n", "\n", "102\n", "00:05:06,879 --> 00:05:07,660\n", "How does that happen?\n", "\n", "103\n", "00:05:08,040 --> 00:05:17,480\n", "And it was because I bought into this idea and I learned it from a gentleman named <PERSON>, who wrote a famous book in the 90s called How to Swim with the Sharks Without Getting Eaten Alive.\n", "\n", "104\n", "00:05:17,519 --> 00:05:17,920\n", "Yeah.\n", "\n", "105\n", "00:05:18,079 --> 00:05:19,579\n", "And I met <PERSON>.\n", "\n", "106\n", "00:05:19,720 --> 00:05:24,759\n", "He actually is the person who got me inspired to become a professional speaker, something that I do as well.\n", "\n", "107\n", "00:05:25,120 --> 00:05:26,399\n", "And I've met him.\n", "\n", "108\n", "00:05:26,420 --> 00:05:27,279\n", "I've known <PERSON> for a long time.\n", "\n", "109\n", "00:05:27,360 --> 00:05:30,899\n", "But I read his books and I believed him.\n", "\n", "110\n", "00:05:31,040 --> 00:05:37,500\n", "And he basically said that the friendships you build and the relationships you build and the reputation you build are going to be your secret weapon.\n", "\n", "111\n", "00:05:37,819 --> 00:05:40,579\n", "And it doesn't happen in the first five or 10 years.\n", "\n", "112\n", "00:05:40,680 --> 00:05:40,879\n", "No.\n", "\n", "113\n", "00:05:41,180 --> 00:05:47,860\n", "If you fast forward from when I was in my 20s and 30s when I learned that to now, people always ask me, how did you get a job at ATC?\n", "\n", "114\n", "00:05:47,939 --> 00:05:50,079\n", "I'm like, well, they were looking for somebody to reinvent it.\n", "\n", "115\n", "00:05:50,160 --> 00:05:50,399\n", "Yeah.\n", "\n", "116\n", "00:05:50,459 --> 00:05:51,959\n", "And half the board knew who I was.\n", "\n", "117\n", "00:05:52,040 --> 00:05:52,379\n", "Yeah.\n", "\n", "118\n", "00:05:52,459 --> 00:05:53,660\n", "And they asked me.\n", "\n", "119\n", "00:05:53,759 --> 00:05:54,060\n", "Yeah.\n", "\n", "120\n", "00:05:54,660 --> 00:05:55,060\n", "Exactly.\n", "\n", "121\n", "00:05:55,180 --> 00:05:56,180\n", "What you just laid.\n", "\n", "122\n", "00:05:56,420 --> 00:05:59,079\n", "We are doing a fit gap.\n", "\n", "123\n", "00:06:00,040 --> 00:06:01,360\n", "If a role demands.\n", "\n", "124\n", "00:06:03,000 --> 00:06:08,379\n", "You get shocked how many people apply not knowing what job, what company that.\n", "\n", "125\n", "00:06:09,780 --> 00:06:12,660\n", "Are you applying to a job in a mass?\n", "\n", "126\n", "00:06:13,200 --> 00:06:13,980\n", "Like not.\n", "\n", "127\n", "00:06:14,240 --> 00:06:16,600\n", "I understand the desperation to need a job.\n", "\n", "128\n", "00:06:17,480 --> 00:06:18,879\n", "But spend time.\n", "\n", "129\n", "00:06:19,139 --> 00:06:20,439\n", "What this company does.\n", "\n", "130\n", "00:06:20,699 --> 00:06:21,300\n", "Go to their website.\n", "\n", "131\n", "00:06:21,500 --> 00:06:24,420\n", "I mean, in five minutes, you can get the gist of what a company does.\n", "\n", "132\n", "00:06:24,620 --> 00:06:24,819\n", "Yeah.\n", "\n", "133\n", "00:06:24,819 --> 00:06:26,160\n", "You got to know the company.\n", "\n", "134\n", "00:06:26,160 --> 00:06:28,459\n", "You got to know what you are pitching to the company.\n", "\n", "135\n", "00:06:28,540 --> 00:06:28,899\n", "Yeah.\n", "\n", "136\n", "00:06:28,899 --> 00:06:33,000\n", "What exactly you fit in, why you are the best guy.\n", "\n", "137\n", "00:06:33,180 --> 00:06:34,600\n", "You need to articulate that really.\n", "\n", "138\n", "00:06:34,839 --> 00:06:35,620\n", "I don't care.\n", "\n", "139\n", "00:06:36,079 --> 00:06:38,060\n", "Oh, nobody reads all the pages.\n", "\n", "140\n", "00:06:38,199 --> 00:06:39,779\n", "We get some resumes, 20 pages.\n", "\n", "141\n", "00:06:40,860 --> 00:06:42,339\n", "I don't blame them.\n", "\n", "142\n", "00:06:42,360 --> 00:06:44,519\n", "They're very proud of what they've done.\n", "\n", "143\n", "00:06:44,740 --> 00:06:46,019\n", "We are all proud of.\n", "\n", "144\n", "00:06:46,040 --> 00:06:46,819\n", "We're not going to read it.\n", "\n", "145\n", "00:06:46,920 --> 00:06:47,220\n", "Yeah.\n", "\n", "146\n", "00:06:47,259 --> 00:06:49,319\n", "Nobody's going to read your 20 pages.\n", "\n", "147\n", "00:06:49,699 --> 00:06:55,240\n", "What we need is that cover letter tells me what you can bring it to our company.\n", "\n", "148\n", "00:06:55,240 --> 00:06:56,360\n", "Our region.\n", "\n", "149\n", "00:06:56,500 --> 00:06:58,819\n", "You show me what we are doing.\n", "\n", "150\n", "00:06:59,339 --> 00:07:00,500\n", "You can do different.\n", "\n", "151\n", "00:07:00,800 --> 00:07:03,120\n", "And I flip that around to the companies too.\n", "\n", "152\n", "00:07:03,279 --> 00:07:07,300\n", "You know, it used to be companies wanted to be really active in the community.\n", "\n", "153\n", "00:07:07,439 --> 00:07:09,560\n", "They wanted to support the Austin Technology Council.\n", "\n", "154\n", "00:07:09,680 --> 00:07:12,000\n", "They wanted to support the Chamber of Commerce or whatever group.\n", "\n", "155\n", "00:07:12,139 --> 00:07:16,160\n", "And they wanted their logos to be out there that they were part of the community because\n", "\n", "156\n", "00:07:16,160 --> 00:07:20,759\n", "they knew that their employees appreciated seeing the support of the community.\n", "\n", "157\n", "00:07:20,860 --> 00:07:25,160\n", "But they also knew that when they were looking for employees, if I've never heard of the company,\n", "\n", "158\n", "00:07:25,220 --> 00:07:25,220\n", "\n", "\n", "159\n", "00:07:25,680 --> 00:07:28,519\n", "I could totally miss the opportunity and not even apply.\n", "\n", "160\n", "00:07:29,060 --> 00:07:33,860\n", "But if the company builds a reputation and the leadership of the company and you do a great job of this,\n", "\n", "161\n", "00:07:33,959 --> 00:07:36,519\n", "you know, you show up at different events around town.\n", "\n", "162\n", "00:07:36,720 --> 00:07:39,240\n", "I would love to see because that's where I pick on some people.\n", "\n", "163\n", "00:07:39,439 --> 00:07:39,800\n", "Well, right.\n", "\n", "164\n", "00:07:40,100 --> 00:07:41,420\n", "But that's how you find people.\n", "\n", "165\n", "00:07:41,540 --> 00:07:45,839\n", "But also, then if someone comes to me and says, hey, have you heard of this company?\n", "\n", "166\n", "00:07:45,939 --> 00:07:46,980\n", "I'm like, oh, that was great.\n", "\n", "167\n", "00:07:47,120 --> 00:07:47,600\n", "You've got to go.\n", "\n", "168\n", "00:07:48,020 --> 00:07:53,180\n", "If you stay behind the wall and just build your product and somebody comes to me and says, oh, they're looking for somebody.\n", "\n", "169\n", "00:07:53,259 --> 00:07:54,360\n", "And I go, I don't know who they are.\n", "\n", "170\n", "00:07:54,360 --> 00:07:57,720\n", "A great person may not be as inspired to apply.\n", "\n", "171\n", "00:07:58,079 --> 00:08:02,959\n", "And so I think that the people have to build a reputation in their community and in their industry.\n", "\n", "172\n", "00:08:03,120 --> 00:08:08,199\n", "And I think companies have to go back to the days of building a reputation in their community.\n", "\n", "173\n", "00:08:08,220 --> 00:08:08,819\n", "They got to be there.\n", "\n", "174\n", "00:08:08,879 --> 00:08:09,740\n", "It's a both ways.\n", "\n", "175\n", "00:08:09,819 --> 00:08:11,519\n", "Yeah, it's a both ways.\n", "\n", "176\n", "00:08:12,339 --> 00:08:17,560\n", "Another one thing we have done in Onanu is we welcome anybody who wants an internship.\n", "\n", "177\n", "00:08:18,459 --> 00:08:20,500\n", "I know we are a bootstrapping it.\n", "\n", "178\n", "00:08:20,560 --> 00:08:21,079\n", "We don't pay.\n", "\n", "179\n", "00:08:21,259 --> 00:08:21,980\n", "So what do we give?\n", "\n", "180\n", "00:08:22,060 --> 00:08:23,899\n", "Three months time to prove them.\n", "\n", "181\n", "00:08:23,899 --> 00:08:24,139\n", "Why?\n", "\n", "182\n", "00:08:24,360 --> 00:08:24,959\n", "Because we know that you need to hire them.\n", "\n", "183\n", "00:08:25,500 --> 00:08:26,800\n", "Some kind of a job.\n", "\n", "184\n", "00:08:27,220 --> 00:08:29,199\n", "That's what we have done with a lot of people.\n", "\n", "185\n", "00:08:29,639 --> 00:08:32,379\n", "Some people came and they said, OK, they didn't.\n", "\n", "186\n", "00:08:32,440 --> 00:08:34,200\n", "We are not looking for paper files.\n", "\n", "187\n", "00:08:34,360 --> 00:08:34,659\n", "Right.\n", "\n", "188\n", "00:08:34,759 --> 00:08:35,899\n", "We don't guide you.\n", "\n", "189\n", "00:08:35,919 --> 00:08:36,879\n", "We'll give you a project.\n", "\n", "190\n", "00:08:37,019 --> 00:08:38,259\n", "You come and beat us down.\n", "\n", "191\n", "00:08:38,340 --> 00:08:39,220\n", "This is what you should do.\n", "\n", "192\n", "00:08:40,179 --> 00:08:41,059\n", "That's what we have.\n", "\n", "193\n", "00:08:41,120 --> 00:08:47,159\n", "I think that is where the market is going in the jobs now is every company with the AI.\n", "\n", "194\n", "00:08:48,199 --> 00:08:54,279\n", "Because you have now you have we crossed information is now we are in technology age.\n", "\n", "195\n", "00:08:54,700 --> 00:08:58,299\n", "We have the AI tools for everything you can do.\n", "\n", "196\n", "00:08:59,400 --> 00:09:05,100\n", "Now, what you can do with the AI tools, the information you already have, just a click\n", "\n", "197\n", "00:09:05,100 --> 00:09:05,659\n", "of a button.\n", "\n", "198\n", "00:09:05,980 --> 00:09:06,000\n", "Right.\n", "\n", "199\n", "00:09:06,139 --> 00:09:07,759\n", "And people are scared of the AI tools.\n", "\n", "200\n", "00:09:07,960 --> 00:09:10,299\n", "But, you know, we're still in the infancy.\n", "\n", "201\n", "00:09:10,440 --> 00:09:10,799\n", "Yeah.\n", "\n", "202\n", "00:09:10,799 --> 00:09:12,039\n", "So we're not even started.\n", "\n", "203\n", "00:09:12,279 --> 00:09:13,240\n", "We're not even started.\n", "\n", "204\n", "00:09:13,379 --> 00:09:13,539\n", "Right.\n", "\n", "205\n", "00:09:13,580 --> 00:09:15,980\n", "I mean, ChatGPT was two and a half years ago.\n", "\n", "206\n", "00:09:16,039 --> 00:09:16,320\n", "Yeah.\n", "\n", "207\n", "00:09:16,379 --> 00:09:20,659\n", "And, you know, I credit a friend of mine who ChatGPT had released like three days before.\n", "\n", "208\n", "00:09:20,720 --> 00:09:21,840\n", "And he said, you got to look at this.\n", "\n", "209\n", "00:09:21,840 --> 00:09:25,940\n", "Not just because of your role with ATC, but your role as a professional speaker.\n", "\n", "210\n", "00:09:26,139 --> 00:09:28,379\n", "You've got to learn what's going on because this is going to affect things.\n", "\n", "211\n", "00:09:28,519 --> 00:09:31,820\n", "So I was accidentally early exposed to it.\n", "\n", "212\n", "00:09:32,179 --> 00:09:34,960\n", "And, you know, it's come a long way in two and a half years.\n", "\n", "213\n", "00:09:35,139 --> 00:09:36,519\n", "It doesn't hallucinate as much.\n", "\n", "214\n", "00:09:36,539 --> 00:09:38,879\n", "You know, it doesn't use as many goofy phrases.\n", "\n", "215\n", "00:09:40,319 --> 00:09:41,940\n", "However, it's still in its infancy.\n", "\n", "216\n", "00:09:42,019 --> 00:09:44,779\n", "And so people who are like, oh, AI can do the writing.\n", "\n", "217\n", "00:09:44,799 --> 00:09:46,460\n", "No, you can't trust it.\n", "\n", "218\n", "00:09:46,460 --> 00:09:51,820\n", "If you're looking for AI to do coding or writing, you can't trust it to give you the final product.\n", "\n", "219\n", "00:09:51,840 --> 00:09:53,919\n", "You still need humans who are going to tweak it.\n", "\n", "220\n", "00:09:53,960 --> 00:09:55,600\n", "I know somebody who's a coder.\n", "\n", "221\n", "00:09:55,679 --> 00:09:56,940\n", "And they use AI.\n", "\n", "222\n", "00:09:57,179 --> 00:09:59,559\n", "But he said, look, it's not writing the best code.\n", "\n", "223\n", "00:09:59,740 --> 00:10:01,320\n", "So he treats it in one of two ways.\n", "\n", "224\n", "00:10:01,679 --> 00:10:06,120\n", "He either treats it as a junior coder who he has to review everything they do.\n", "\n", "225\n", "00:10:06,139 --> 00:10:06,419\n", "Yeah.\n", "\n", "226\n", "00:10:06,500 --> 00:10:09,639\n", "Or as the most senior creative coder he's ever seen.\n", "\n", "227\n", "00:10:09,799 --> 00:10:09,960\n", "Yeah.\n", "\n", "228\n", "00:10:10,039 --> 00:10:11,139\n", "Who's on LSD.\n", "\n", "229\n", "00:10:11,360 --> 00:10:11,779\n", "Yeah.\n", "\n", "230\n", "00:10:11,860 --> 00:10:14,840\n", "Which means he still has to go back and review everything that it does.\n", "\n", "231\n", "00:10:15,039 --> 00:10:16,860\n", "And then it's a great productivity tool.\n", "\n", "232\n", "00:10:17,039 --> 00:10:20,700\n", "But people need to, for the time being, see it as just that.\n", "\n", "233\n", "00:10:20,740 --> 00:10:21,159\n", "Yeah.\n", "\n", "234\n", "00:10:21,840 --> 00:10:23,179\n", "And then, oh, it's going to take away my job.\n", "\n", "235\n", "00:10:23,259 --> 00:10:23,379\n", "No.\n", "\n", "236\n", "00:10:23,460 --> 00:10:29,559\n", "If it makes you more productive, you're going to keep your job longer and you're going to get more jobs if you know how to use these tools.\n", "\n", "237\n", "00:10:29,659 --> 00:10:31,840\n", "And it's not just, you know, LLMs.\n", "\n", "238\n", "00:10:32,440 --> 00:10:36,480\n", "There's so much more coming out and becoming more ubiquitous in the world of AI.\n", "\n", "239\n", "00:10:37,019 --> 00:10:42,179\n", "And then when we get to quantum computing, if they can figure that out, God save us all, the changes are going to go even faster.\n", "\n", "240\n", "00:10:42,360 --> 00:10:42,440\n", "Yeah.\n", "\n", "241\n", "00:10:42,539 --> 00:10:48,559\n", "And I think, like, something really that you say, like, the younger generation, like, it's kind of scary to go out and network, you know?\n", "\n", "242\n", "00:10:48,600 --> 00:10:49,620\n", "Like, we're not used to it.\n", "\n", "243\n", "00:10:49,759 --> 00:10:50,799\n", "So I'm going to ask you.\n", "\n", "244\n", "00:10:50,799 --> 00:10:52,200\n", "I'm going to throw that back at you, <PERSON>.\n", "\n", "245\n", "00:10:52,379 --> 00:10:54,340\n", "Why is it scary to go network?\n", "\n", "246\n", "00:10:54,480 --> 00:10:57,000\n", "Are people like <PERSON> and <PERSON>, are we mean?\n", "\n", "247\n", "00:10:57,759 --> 00:11:03,299\n", "Well, because, like, this is the most, like, crazy part about all of this to me is that, like, we went to school.\n", "\n", "248\n", "00:11:03,320 --> 00:11:05,039\n", "We're learning this stuff, like accounting.\n", "\n", "249\n", "00:11:05,179 --> 00:11:05,980\n", "We're learning other stuff.\n", "\n", "250\n", "00:11:06,220 --> 00:11:10,759\n", "And then we're out now and it's four years after, you know, like two years after AI comes out.\n", "\n", "251\n", "00:11:11,019 --> 00:11:14,639\n", "There's no one, there's no educational, you know, like, who's the best at doing AI?\n", "\n", "252\n", "00:11:14,679 --> 00:11:16,539\n", "They started learning three, four years ago.\n", "\n", "253\n", "00:11:16,740 --> 00:11:20,039\n", "You know, so that's why it's scary because you're going out and you're not classically trained.\n", "\n", "254\n", "00:11:20,039 --> 00:11:22,399\n", "I mean, the more you go out, the more people you meet, the more network you have.\n", "\n", "255\n", "00:11:22,700 --> 00:11:25,220\n", "And it's also why you can see, like, the rise of, like, YouTube.\n", "\n", "256\n", "00:11:25,399 --> 00:11:26,840\n", "Like, I just looked it up.\n", "\n", "257\n", "00:11:26,860 --> 00:11:32,919\n", "It's three, almost 400,000 people make a living off of platforms like YouTube, which is just a large network that's deployed digitally.\n", "\n", "258\n", "00:11:33,279 --> 00:11:39,000\n", "And they, you know, so because it's just so interesting, like, there's no education.\n", "\n", "259\n", "00:11:39,240 --> 00:11:40,159\n", "Who's going to be the best?\n", "\n", "260\n", "00:11:40,320 --> 00:11:42,159\n", "Like, can you apply to a job at a company?\n", "\n", "261\n", "00:11:42,240 --> 00:11:44,159\n", "You could have almost no experience, no education.\n", "\n", "262\n", "00:11:44,379 --> 00:11:49,559\n", "But if you have three or four projects that you're successful with, then you're going to solve a problem at that company.\n", "\n", "263\n", "00:11:49,700 --> 00:11:50,019\n", "Right.\n", "\n", "264\n", "00:11:50,039 --> 00:11:54,059\n", "You're going to get the job, you know, because it is just so new and there's no education.\n", "\n", "265\n", "00:11:54,360 --> 00:11:58,899\n", "But why the scary to go network to find these connections and open those doors?\n", "\n", "266\n", "00:11:59,300 --> 00:12:03,100\n", "Well, like, you expect one thing and then you get another, you know.\n", "\n", "267\n", "00:12:03,120 --> 00:12:06,379\n", "So it's like everybody is living in a world where it's like we don't really know how to use AI.\n", "\n", "268\n", "00:12:06,580 --> 00:12:06,860\n", "Right.\n", "\n", "269\n", "00:12:07,039 --> 00:12:12,659\n", "See, the personality, I came from a, you know, very shy, you know, I opened up.\n", "\n", "270\n", "00:12:12,679 --> 00:12:16,200\n", "I learned in the U.S. being staying this long in Austin.\n", "\n", "271\n", "00:12:16,399 --> 00:12:17,759\n", "I never used to talk up.\n", "\n", "272\n", "00:12:18,560 --> 00:12:20,019\n", "You have to get up.\n", "\n", "273\n", "00:12:20,019 --> 00:12:21,220\n", "You have to get out of your comfort zone.\n", "\n", "274\n", "00:12:21,500 --> 00:12:21,639\n", "Right.\n", "\n", "275\n", "00:12:22,159 --> 00:12:25,259\n", "The only way to get off the continent, two things you have to do.\n", "\n", "276\n", "00:12:25,379 --> 00:12:34,139\n", "Either you go all in or you have one glass of beer and then get a little bit out of shyness and then go and talk.\n", "\n", "277\n", "00:12:34,320 --> 00:12:35,759\n", "You have to start with strangers.\n", "\n", "278\n", "00:12:35,940 --> 00:12:36,940\n", "And it's a learned skill.\n", "\n", "279\n", "00:12:37,039 --> 00:12:45,899\n", "If you say, I'm going to go to two networking events a week for the next six months, at the end of six months, you're going to be like, I can talk to anybody.\n", "\n", "280\n", "00:12:46,100 --> 00:12:46,720\n", "I can go do this.\n", "\n", "281\n", "00:12:46,759 --> 00:12:49,679\n", "I just go and break into some group and stand there.\n", "\n", "282\n", "00:12:49,679 --> 00:12:49,940\n", "Yeah.\n", "\n", "283\n", "00:12:50,039 --> 00:12:52,139\n", "Well, and don't you don't need to do anything.\n", "\n", "284\n", "00:12:52,399 --> 00:12:56,919\n", "What I say is like back to your generation, a lot of people grew up with everything was digital.\n", "\n", "285\n", "00:12:56,980 --> 00:13:02,659\n", "You talk to your friends via text, you know, you hang out and, you know, and chat via the screen.\n", "\n", "286\n", "00:13:03,039 --> 00:13:04,080\n", "And here's what I tell them.\n", "\n", "287\n", "00:13:04,139 --> 00:13:09,639\n", "We have to remember, though, is that all opportunities in life and this will not change no matter what the technology does.\n", "\n", "288\n", "00:13:09,860 --> 00:13:11,860\n", "All opportunities in life come from people.\n", "\n", "289\n", "00:13:11,960 --> 00:13:12,360\n", "Yeah.\n", "\n", "290\n", "00:13:12,440 --> 00:13:19,580\n", "And so earlier on, if you can collect people in a good way, if you can collect relationships, not like <PERSON> and <PERSON>,\n", "\n", "291\n", "00:13:19,679 --> 00:13:21,460\n", "not like, ha ha ha, what can they do for me?\n", "\n", "292\n", "00:13:21,620 --> 00:13:29,059\n", "But if you can get to know people and build relationships with people and show them through actions over five years, 10 years, etc.,\n", "\n", "293\n", "00:13:29,059 --> 00:13:30,559\n", "then you're a doer.\n", "\n", "294\n", "00:13:30,559 --> 00:13:31,580\n", "You care about the community.\n", "\n", "295\n", "00:13:32,179 --> 00:13:33,539\n", "You're someone who helps other people.\n", "\n", "296\n", "00:13:33,620 --> 00:13:34,600\n", "You're not just out for yourself.\n", "\n", "297\n", "00:13:34,899 --> 00:13:37,580\n", "What's going to happen is, is opportunities are going to come up.\n", "\n", "298\n", "00:13:37,620 --> 00:13:38,879\n", "People are going to pull you in.\n", "\n", "299\n", "00:13:39,220 --> 00:13:41,659\n", "But if you don't do those things, they don't know who you are.\n", "\n", "300\n", "00:13:42,259 --> 00:13:44,480\n", "Even if they would want to pull you in, they don't know you.\n", "\n", "301\n", "00:13:44,539 --> 00:13:45,220\n", "They can't do anything.\n", "\n", "302\n", "00:13:45,320 --> 00:13:49,659\n", "So I try to teach younger people, you know, look at everything good that's happened in your life.\n", "\n", "303\n", "00:13:49,659 --> 00:13:51,419\n", "It's happened because of a person.\n", "\n", "304\n", "00:13:51,840 --> 00:13:54,259\n", "You know, somehow you can trace it back to people.\n", "\n", "305\n", "00:13:54,419 --> 00:14:03,980\n", "And the more you can connect in a community like Austin, and that's why we have to work hard as a community to keep that vibe of connectivity, of community.\n", "\n", "306\n", "00:14:04,580 --> 00:14:10,039\n", "Because if you can still reach people and share with people, you know, not everybody's going to like you and that's fine.\n", "\n", "307\n", "00:14:10,179 --> 00:14:16,299\n", "But if you just keep trying, and when people say, oh, I don't like to go to networking events because I go once and nothing happens.\n", "\n", "308\n", "00:14:16,360 --> 00:14:19,379\n", "Well, no, you've got to go to that same organization like for a year.\n", "\n", "309\n", "00:14:19,820 --> 00:14:23,120\n", "Not before you're going to get benefit, but before people even notice that you're there.\n", "\n", "310\n", "00:14:23,320 --> 00:14:24,340\n", "You've got to keep showing up.\n", "\n", "311\n", "00:14:24,340 --> 00:14:25,120\n", "You've got to keep showing up.\n", "\n", "312\n", "00:14:25,179 --> 00:14:26,720\n", "And after a while, they're like, I see you around.\n", "\n", "313\n", "00:14:26,879 --> 00:14:27,299\n", "What do you do?\n", "\n", "314\n", "00:14:27,379 --> 00:14:28,559\n", "And then you start to learn.\n", "\n", "315\n", "00:14:28,740 --> 00:14:28,899\n", "Yeah.\n", "\n", "316\n", "00:14:29,139 --> 00:14:32,320\n", "The more persistent you are in showing up is going to value.\n", "\n", "317\n", "00:14:32,659 --> 00:14:35,059\n", "Another one is we know the stats.\n", "\n", "318\n", "00:14:35,240 --> 00:14:38,000\n", "80% of the jobs in America are filled by repros.\n", "\n", "319\n", "00:14:39,720 --> 00:14:41,840\n", "So you have left with 20%.\n", "\n", "320\n", "00:14:41,840 --> 00:14:42,279\n", "You are.\n", "\n", "321\n", "00:14:42,840 --> 00:14:49,639\n", "And most of the, unfortunately, these federal laws, compliance, HR laws, and state laws create a lot of problems.\n", "\n", "322\n", "00:14:49,639 --> 00:14:49,639\n", "\n", "\n", "323\n", "00:14:49,659 --> 00:14:50,960\n", "And they create a big mess.\n", "\n", "324\n", "00:14:51,440 --> 00:14:57,220\n", "Oh, you've got to post a job two weeks before, you know, it's all this.\n", "\n", "325\n", "00:14:57,299 --> 00:14:58,980\n", "I don't know if it's really helping anybody.\n", "\n", "326\n", "00:14:59,340 --> 00:15:00,919\n", "Well, it depends.\n", "\n", "327\n", "00:15:01,179 --> 00:15:01,440\n", "Right.\n", "\n", "328\n", "00:15:01,539 --> 00:15:06,799\n", "But in a lot of cases, what happens is if they're taking somebody internally or they're taking someone through.\n", "\n", "329\n", "00:15:06,940 --> 00:15:08,019\n", "I already know who they are.\n", "\n", "330\n", "00:15:08,039 --> 00:15:08,980\n", "They post the job.\n", "\n", "331\n", "00:15:09,019 --> 00:15:14,460\n", "I can't tell you how many people tell me they apply to 100 jobs and only hear back from two, three companies.\n", "\n", "332\n", "00:15:14,559 --> 00:15:16,320\n", "It's like you're applying to a job.\n", "\n", "333\n", "00:15:16,360 --> 00:15:18,700\n", "You're not even getting a thing back that says, thank you for applying.\n", "\n", "334\n", "00:15:18,700 --> 00:15:19,899\n", "We've gone a different direction.\n", "\n", "335\n", "00:15:20,179 --> 00:15:21,980\n", "You're just never, it's an abyss.\n", "\n", "336\n", "00:15:22,059 --> 00:15:23,820\n", "I'll tell you why people don't respond.\n", "\n", "337\n", "00:15:24,379 --> 00:15:25,460\n", "There is a compliance.\n", "\n", "338\n", "00:15:25,720 --> 00:15:27,840\n", "They could be you are pulled into a lawsuit.\n", "\n", "339\n", "00:15:28,080 --> 00:15:28,360\n", "Right.\n", "\n", "340\n", "00:15:29,359 --> 00:15:35,980\n", "I think it's a part of, because the communication sometimes can turn on your, against you.\n", "\n", "341\n", "00:15:36,059 --> 00:15:36,539\n", "That's true.\n", "\n", "342\n", "00:15:36,899 --> 00:15:39,700\n", "So companies are scared to do anything.\n", "\n", "343\n", "00:15:40,139 --> 00:15:40,580\n", "Right.\n", "\n", "344\n", "00:15:40,639 --> 00:15:42,080\n", "It's sensitive to somebody.\n", "\n", "345\n", "00:15:42,539 --> 00:15:43,299\n", "He's desperate.\n", "\n", "346\n", "00:15:43,480 --> 00:15:44,360\n", "He needs a job.\n", "\n", "347\n", "00:15:44,379 --> 00:15:45,100\n", "And he puts it.\n", "\n", "348\n", "00:15:45,120 --> 00:15:47,500\n", "Then you want to help him to send a message.\n", "\n", "349\n", "00:15:47,500 --> 00:15:49,399\n", "That message can turn into your side.\n", "\n", "350\n", "00:15:49,820 --> 00:15:54,159\n", "So there is always how you see the world, how you can say.\n", "\n", "351\n", "00:15:54,960 --> 00:16:01,120\n", "For me, like, as you said, know people, talk to people, connect with people.\n", "\n", "352\n", "00:16:02,460 --> 00:16:05,159\n", "You know, your local community is a big thing.\n", "\n", "353\n", "00:16:05,259 --> 00:16:05,519\n", "Yep.\n", "\n", "354\n", "00:16:05,559 --> 00:16:11,740\n", "You, if you, you, if you are a person they like you, you'll get a job.\n", "\n", "355\n", "00:16:12,000 --> 00:16:13,559\n", "Well, I think it's your local community.\n", "\n", "356\n", "00:16:13,679 --> 00:16:15,440\n", "So it's things like technology council.\n", "\n", "357\n", "00:16:15,539 --> 00:16:17,440\n", "It's things like chamber, a whole bunch of other organizations.\n", "\n", "358\n", "00:16:18,179 --> 00:16:20,919\n", "You know, you can't belong to everything and you can't go to everything.\n", "\n", "359\n", "00:16:21,039 --> 00:16:22,279\n", "So pick two or three groups.\n", "\n", "360\n", "00:16:22,379 --> 00:16:22,539\n", "Yeah.\n", "\n", "361\n", "00:16:22,559 --> 00:16:23,480\n", "And go to those.\n", "\n", "362\n", "00:16:23,480 --> 00:16:23,740\n", "Regular.\n", "\n", "363\n", "00:16:23,919 --> 00:16:24,240\n", "Regular.\n", "\n", "364\n", "00:16:24,320 --> 00:16:26,799\n", "Because most of us only have one event a month.\n", "\n", "365\n", "00:16:26,940 --> 00:16:27,100\n", "Yeah.\n", "\n", "366\n", "00:16:27,139 --> 00:16:27,659\n", "So pick three.\n", "\n", "367\n", "00:16:27,679 --> 00:16:29,440\n", "You've got three things to go to in a month.\n", "\n", "368\n", "00:16:29,460 --> 00:16:29,580\n", "Yeah.\n", "\n", "369\n", "00:16:29,580 --> 00:16:32,360\n", "One that does a breakfast, one that does a lunch, one that does a happy hour.\n", "\n", "370\n", "00:16:33,659 --> 00:16:34,899\n", "And always go to those.\n", "\n", "371\n", "00:16:34,960 --> 00:16:37,120\n", "And then you can drop in on other ones if there's a good topic.\n", "\n", "372\n", "00:16:37,200 --> 00:16:39,600\n", "But the other thing is, is what is your industry?\n", "\n", "373\n", "00:16:39,940 --> 00:16:40,259\n", "Yeah.\n", "\n", "374\n", "00:16:40,320 --> 00:16:43,519\n", "And what type of industry groups, both locally and nationally exist.\n", "\n", "375\n", "00:16:43,700 --> 00:16:43,860\n", "Yeah.\n", "\n", "376\n", "00:16:43,860 --> 00:16:47,419\n", "I mean, the, the people who get really involved with, you know, the, the, the, the, the, the,\n", "\n", "377\n", "00:16:47,419 --> 00:16:47,580\n", "the, the, the, the, the.\n", "\n", "378\n", "00:16:47,580 --> 00:16:50,740\n", "You know if there are a high, if there are hiring like CIO and the a property with real\n", "\n", "379\n", "00:16:50,740 --> 00:16:50,759\n", "estate marketing.\n", "\n", "380\n", "00:16:50,960 --> 00:16:51,059\n", "Yeah.\n", "\n", "381\n", "00:16:51,100 --> 00:16:52,379\n", "Does a beardamine comenzar beach and O supposed to be like a beach commercial to\n", "\n", "382\n", "00:16:52,379 --> 00:16:54,279\n", "describe not in저i a rock star and make a house sign Post because o supposed to be like\n", "\n", "383\n", "00:16:54,279 --> 00:16:54,279\n", "\n", "\n", "384\n", "00:16:54,279 --> 00:16:54,279\n", "\n", "\n", "385\n", "00:16:54,299 --> 00:16:54,500\n", "I only think about<PERSON> altogether and not others.\n", "\n", "386\n", "00:16:54,779 --> 00:16:54,779\n", "\n", "\n", "387\n", "00:16:54,779 --> 00:16:55,100\n", "Because I think you've got to keep that shit to yourself.\n", "\n", "388\n", "00:16:55,100 --> 00:16:55,299\n", "That's pretty high.\n", "\n", "389\n", "00:16:55,299 --> 00:16:55,299\n", "\n", "\n", "390\n", "00:16:55,299 --> 00:16:55,360\n", "Like let's look at...\n", "\n", "391\n", "00:16:55,360 --> 00:16:55,440\n", "Okay.\n", "\n", "392\n", "00:16:55,440 --> 00:16:55,440\n", "\n", "\n", "393\n", "00:16:55,440 --> 00:16:55,440\n", "\n", "\n", "394\n", "00:16:55,440 --> 00:16:55,440\n", "\n", "\n", "395\n", "00:16:55,440 --> 00:16:55,440\n", "\n", "\n", "396\n", "00:16:55,419 --> 00:16:55,419\n", "\n", "\n", "\n", "Transcript      : If you're ignoring AI, you might be put out of work. I don't care what your history, I don't care what your grades, your maybe scholar, PhD. Are you solving a real business problem? Every time I had a job within three weeks and I barely made an outbound call and people said, well, how, how do you get that? And it was a better job. They go, you get laid off up. How does that happen? Okay, welcome back to the Ignorant Podcast. We're here with <PERSON><PERSON> and <PERSON>. They use AI. So basically the idea was if you're ignoring AI, you might be put out of work. But if you're embracing it and using it and realizing how to morph where your skill set is, there'll be room for you. Compliments. Yeah. It's a tool you got to get in. I think people has misunderstood AI. The education part of this, how you are adaptable, how you are open to new technologies. I think a lot of people are very hesitant because part of this, you know, AI is what are you here? Somebody's writing, don't know what exactly I can do. And also we have a lot of distraction with all these models and there's a dark fight out there. So, so in technology. So what is the other question you have? I have a question for you <PERSON><PERSON>. It's like with that, because, you know, AI is changing the job market and the job, like, you know, scope. Where do you kind of see, like if people are still getting laid off, obviously that's still very scary. I suppose. So Bumble, which is an Austin company, that was like a 30%. So if, you know, we're moving away from certain jobs, what jobs do you kind of see opening or what do you kind of see in the job market? You have been involved in our meetings. We welcome interns to come in to our companies and see the difference, what we see. The, I don't care your resume anymore. I mean, I saw an article this morning, how to write a resume here is a tool. Stop doing that. So I'm going to ask you a question. So tell the world, tell me, go back to the cover letter, right? Give me your cover letter. In one paragraph, there is a project you have done can help me. Right. And I can, next day you have a job. I don't care about industry. I don't care about your grades. You're maybe a scholar, PhD. Are you solving a real business problem? Are you helping my company and companies are trying to. I hire you. Why do we need to hire you? You need to justify. What do you bring it to the table? Are you open to learning and creating solutions? I think we are already creative all now. Oh, for sure. That clear. For sure. It's a paper trail work days gone that can replace the AI. Where our, we see is, yeah, he's a, I know it's hard to say, but everybody has talent. But I don't think so. They use it. Well, they get distracted with, oh, I need to get framed resume. I need to have keywords in a resume. Right. We are in that space where like, okay, give me a one project you delivered out there. You put it out there and where I can look at it and see if it's giving any value to us. Oh, I didn't think about it. How many times we talk in a meeting? Oh, I didn't think about this. That's what I expect in new generation is they have. Amazing talent. I think they have their last focus. Well, and the other thing is, is that with the job market, the way it is now, your reputation, your brand and your network as an individual are going to be more important than ever. And part of that is how do you tell a particular employer what the projects you've worked on and done it. But also as the employer, if I call you and say, Hey, you need to meet Matt. He's perfect for what you're working on. You're more apt to talk to him and possibly hiring. And yet the younger generation is more apt not to go to networking events, not to join organizations, not to really go out even socialize with people in their industry. They don't go to national conferences in their industry. And then they are like, oh, well, everybody gets their job through their network, but I don't have a network. And it's like, well, you have to create that yourself. And I call it human interaction, H.I. And I talk about the fact that H.I. is more important in the world than any time before, because anybody can generate a resume. Yeah. You know, anyone can generate a resume. Now, if you can't write a resume using an A.I. tool in five minutes, then, you know, shame on you. But how do you get that resume in front of a hiring manager? How do you get that resume, you know, in front of people? And how do you know that when a job is open, someone's like, oh, my God, I just heard that Becky got laid off. She's fantastic. Let's hire her. Yeah. Earlier in my career, I got laid off three times because of companies, not anything I did. Companies that either entirely went out of business. Yeah. Or left Austin. Yeah. And every time I had a job within three weeks and I barely made an outbound call and people said, well, how? How do you get that? And it was a better job. They go, you get laid off up. How does that happen? And it was because I bought into this idea and I learned it from a gentleman named Harvey McKay, who wrote a famous book in the 90s called How to Swim with the Sharks Without Getting Eaten Alive. Yeah. And I met Harvey. He actually is the person who got me inspired to become a professional speaker, something that I do as well. And I've met him. I've known Harvey for a long time. But I read his books and I believed him. And he basically said that the friendships you build and the relationships you build and the reputation you build are going to be your secret weapon. And it doesn't happen in the first five or 10 years. No. If you fast forward from when I was in my 20s and 30s when I learned that to now, people always ask me, how did you get a job at ATC? I'm like, well, they were looking for somebody to reinvent it. Yeah. And half the board knew who I was. Yeah. And they asked me. Yeah. Exactly. What you just laid. We are doing a fit gap. If a role demands. You get shocked how many people apply not knowing what job, what company that. Are you applying to a job in a mass? Like not. I understand the desperation to need a job. But spend time. What this company does. Go to their website. I mean, in five minutes, you can get the gist of what a company does. Yeah. You got to know the company. You got to know what you are pitching to the company. Yeah. What exactly you fit in, why you are the best guy. You need to articulate that really. I don't care. Oh, nobody reads all the pages. We get some resumes, 20 pages. I don't blame them. They're very proud of what they've done. We are all proud of. We're not going to read it. Yeah. Nobody's going to read your 20 pages. What we need is that cover letter tells me what you can bring it to our company. Our region. You show me what we are doing. You can do different. And I flip that around to the companies too. You know, it used to be companies wanted to be really active in the community. They wanted to support the Austin Technology Council. They wanted to support the Chamber of Commerce or whatever group. And they wanted their logos to be out there that they were part of the community because they knew that their employees appreciated seeing the support of the community. But they also knew that when they were looking for employees, if I've never heard of the company, I could totally miss the opportunity and not even apply. But if the company builds a reputation and the leadership of the company and you do a great job of this, you know, you show up at different events around town. I would love to see because that's where I pick on some people. Well, right. But that's how you find people. But also, then if someone comes to me and says, hey, have you heard of this company? I'm like, oh, that was great. You've got to go. If you stay behind the wall and just build your product and somebody comes to me and says, oh, they're looking for somebody. And I go, I don't know who they are. A great person may not be as inspired to apply. And so I think that the people have to build a reputation in their community and in their industry. And I think companies have to go back to the days of building a reputation in their community. They got to be there. It's a both ways. Yeah, it's a both ways. Another one thing we have done in Onanu is we welcome anybody who wants an internship. I know we are a bootstrapping it. We don't pay. So what do we give? Three months time to prove them. Why? Because we know that you need to hire them. Some kind of a job. That's what we have done with a lot of people. Some people came and they said, OK, they didn't. We are not looking for paper files. Right. We don't guide you. We'll give you a project. You come and beat us down. This is what you should do. That's what we have. I think that is where the market is going in the jobs now is every company with the AI. Because you have now you have we crossed information is now we are in technology age. We have the AI tools for everything you can do. Now, what you can do with the AI tools, the information you already have, just a click of a button. Right. And people are scared of the AI tools. But, you know, we're still in the infancy. Yeah. So we're not even started. We're not even started. Right. I mean, ChatGPT was two and a half years ago. Yeah. And, you know, I credit a friend of mine who ChatGPT had released like three days before. And he said, you got to look at this. Not just because of your role with ATC, but your role as a professional speaker. You've got to learn what's going on because this is going to affect things. So I was accidentally early exposed to it. And, you know, it's come a long way in two and a half years. It doesn't hallucinate as much. You know, it doesn't use as many goofy phrases. However, it's still in its infancy. And so people who are like, oh, AI can do the writing. No, you can't trust it. If you're looking for AI to do coding or writing, you can't trust it to give you the final product. You still need humans who are going to tweak it. I know somebody who's a coder. And they use AI. But he said, look, it's not writing the best code. So he treats it in one of two ways. He either treats it as a junior coder who he has to review everything they do. Yeah. Or as the most senior creative coder he's ever seen. Yeah. Who's on LSD. Yeah. Which means he still has to go back and review everything that it does. And then it's a great productivity tool. But people need to, for the time being, see it as just that. Yeah. And then, oh, it's going to take away my job. No. If it makes you more productive, you're going to keep your job longer and you're going to get more jobs if you know how to use these tools. And it's not just, you know, LLMs. There's so much more coming out and becoming more ubiquitous in the world of AI. And then when we get to quantum computing, if they can figure that out, God save us all, the changes are going to go even faster. Yeah. And I think, like, something really that you say, like, the younger generation, like, it's kind of scary to go out and network, you know? Like, we're not used to it. So I'm going to ask you. I'm going to throw that back at you, Max. Why is it scary to go network? Are people like Babu and I, are we mean? Well, because, like, this is the most, like, crazy part about all of this to me is that, like, we went to school. We're learning this stuff, like accounting. We're learning other stuff. And then we're out now and it's four years after, you know, like two years after AI comes out. There's no one, there's no educational, you know, like, who's the best at doing AI? They started learning three, four years ago. You know, so that's why it's scary because you're going out and you're not classically trained. I mean, the more you go out, the more people you meet, the more network you have. And it's also why you can see, like, the rise of, like, YouTube. Like, I just looked it up. It's three, almost 400,000 people make a living off of platforms like YouTube, which is just a large network that's deployed digitally. And they, you know, so because it's just so interesting, like, there's no education. Who's going to be the best? Like, can you apply to a job at a company? You could have almost no experience, no education. But if you have three or four projects that you're successful with, then you're going to solve a problem at that company. Right. You're going to get the job, you know, because it is just so new and there's no education. But why the scary to go network to find these connections and open those doors? Well, like, you expect one thing and then you get another, you know. So it's like everybody is living in a world where it's like we don't really know how to use AI. Right. See, the personality, I came from a, you know, very shy, you know, I opened up. I learned in the U.S. being staying this long in Austin. I never used to talk up. You have to get up. You have to get out of your comfort zone. Right. The only way to get off the continent, two things you have to do. Either you go all in or you have one glass of beer and then get a little bit out of shyness and then go and talk. You have to start with strangers. And it's a learned skill. If you say, I'm going to go to two networking events a week for the next six months, at the end of six months, you're going to be like, I can talk to anybody. I can go do this. I just go and break into some group and stand there. Yeah. Well, and don't you don't need to do anything. What I say is like back to your generation, a lot of people grew up with everything was digital. You talk to your friends via text, you know, you hang out and, you know, and chat via the screen. And here's what I tell them. We have to remember, though, is that all opportunities in life and this will not change no matter what the technology does. All opportunities in life come from people. Yeah. And so earlier on, if you can collect people in a good way, if you can collect relationships, not like Courtney and I, not like, ha ha ha, what can they do for me? But if you can get to know people and build relationships with people and show them through actions over five years, 10 years, etc., then you're a doer. You care about the community. You're someone who helps other people. You're not just out for yourself. What's going to happen is, is opportunities are going to come up. People are going to pull you in. But if you don't do those things, they don't know who you are. Even if they would want to pull you in, they don't know you. They can't do anything. So I try to teach younger people, you know, look at everything good that's happened in your life. It's happened because of a person. You know, somehow you can trace it back to people. And the more you can connect in a community like Austin, and that's why we have to work hard as a community to keep that vibe of connectivity, of community. Because if you can still reach people and share with people, you know, not everybody's going to like you and that's fine. But if you just keep trying, and when people say, oh, I don't like to go to networking events because I go once and nothing happens. Well, no, you've got to go to that same organization like for a year. Not before you're going to get benefit, but before people even notice that you're there. You've got to keep showing up. You've got to keep showing up. And after a while, they're like, I see you around. What do you do? And then you start to learn. Yeah. The more persistent you are in showing up is going to value. Another one is we know the stats. 80% of the jobs in America are filled by repros. So you have left with 20%. You are. And most of the, unfortunately, these federal laws, compliance, HR laws, and state laws create a lot of problems. And they create a big mess. Oh, you've got to post a job two weeks before, you know, it's all this. I don't know if it's really helping anybody. Well, it depends. Right. But in a lot of cases, what happens is if they're taking somebody internally or they're taking someone through. I already know who they are. They post the job. I can't tell you how many people tell me they apply to 100 jobs and only hear back from two, three companies. It's like you're applying to a job. You're not even getting a thing back that says, thank you for applying. We've gone a different direction. You're just never, it's an abyss. I'll tell you why people don't respond. There is a compliance. They could be you are pulled into a lawsuit. Right. I think it's a part of, because the communication sometimes can turn on your, against you. That's true. So companies are scared to do anything. Right. It's sensitive to somebody. He's desperate. He needs a job. And he puts it. Then you want to help him to send a message. That message can turn into your side. So there is always how you see the world, how you can say. For me, like, as you said, know people, talk to people, connect with people. You know, your local community is a big thing. Yep. You, if you, you, if you are a person they like you, you'll get a job. Well, I think it's your local community. So it's things like technology council. It's things like chamber, a whole bunch of other organizations. You know, you can't belong to everything and you can't go to everything. So pick two or three groups. Yeah. And go to those. Regular. Regular. Because most of us only have one event a month. Yeah. So pick three. You've got three things to go to in a month. Yeah. One that does a breakfast, one that does a lunch, one that does a happy hour. And always go to those. And then you can drop in on other ones if there's a good topic. But the other thing is, is what is your industry? Yeah. And what type of industry groups, both locally and nationally exist. Yeah. I mean, the, the people who get really involved with, you know, the, the, the, the, the, the, the, the, the, the, the. You know if there are a high, if there are hiring like CIO and the a property with real estate marketing. Yeah. Does a beardamine comenzar beach and O supposed to be like a beach commercial to describe not in저i a rock star and make a house sign Post because o supposed to be like I only think aboutna James altogether and not others. Because I think you've got to keep that shit to yourself. That's pretty high. Like let's look at... Okay.\n", "WordTimestamps  : [{'word': ' If', 'start': 0.0, 'end': 0.14}, {'word': \" you're\", 'start': 0.14, 'end': 0.28}, {'word': ' ignoring', 'start': 0.28, 'end': 0.6}, {'word': ' AI,', 'start': 0.6, 'end': 0.9}, {'word': ' you', 'start': 1.2, 'end': 1.64}, {'word': ' might', 'start': 1.64, 'end': 1.88}, {'word': ' be', 'start': 1.88, 'end': 2.04}, {'word': ' put', 'start': 2.04, 'end': 2.18}, {'word': ' out', 'start': 2.18, 'end': 2.34}, {'word': ' of', 'start': 2.34, 'end': 2.42}, {'word': ' work.', 'start': 2.42, 'end': 2.62}, {'word': ' I', 'start': 2.86, 'end': 3.2}, {'word': \" don't\", 'start': 3.2, 'end': 3.52}, {'word': ' care', 'start': 3.52, 'end': 3.86}, {'word': ' what', 'start': 3.86, 'end': 4.16}, {'word': ' your', 'start': 4.16, 'end': 4.3}, {'word': ' history,', 'start': 4.3, 'end': 4.58}, {'word': ' I', 'start': 4.66, 'end': 4.76}, {'word': \" don't\", 'start': 4.76, 'end': 4.92}, {'word': ' care', 'start': 4.92, 'end': 5.18}, {'word': ' what', 'start': 5.18, 'end': 5.42}, {'word': ' your', 'start': 5.42, 'end': 5.6}, {'word': ' grades,', 'start': 5.6, 'end': 5.96}, {'word': ' your', 'start': 6.22, 'end': 6.38}, {'word': ' maybe', 'start': 6.38, 'end': 6.68}, {'word': ' scholar,', 'start': 6.68, 'end': 7.0}, {'word': ' PhD.', 'start': 7.26, 'end': 7.62}, {'word': ' Are', 'start': 8.880000000000003, 'end': 9.36}, {'word': ' you', 'start': 9.36, 'end': 9.58}, {'word': ' solving', 'start': 9.58, 'end': 10.06}, {'word': ' a', 'start': 10.06, 'end': 10.3}, {'word': ' real', 'start': 10.3, 'end': 10.62}, {'word': ' business', 'start': 10.62, 'end': 11.06}, {'word': ' problem?', 'start': 11.06, 'end': 11.7}, {'word': ' Every', 'start': 12.28, 'end': 12.72}, {'word': ' time', 'start': 12.72, 'end': 12.98}, {'word': ' I', 'start': 12.98, 'end': 13.22}, {'word': ' had', 'start': 13.22, 'end': 13.36}, {'word': ' a', 'start': 13.36, 'end': 13.46}, {'word': ' job', 'start': 13.46, 'end': 13.7}, {'word': ' within', 'start': 13.7, 'end': 13.9}, {'word': ' three', 'start': 13.9, 'end': 14.16}, {'word': ' weeks', 'start': 14.16, 'end': 14.52}, {'word': ' and', 'start': 14.52, 'end': 14.8}, {'word': ' I', 'start': 14.8, 'end': 14.94}, {'word': ' barely', 'start': 14.94, 'end': 15.34}, {'word': ' made', 'start': 15.34, 'end': 15.6}, {'word': ' an', 'start': 15.6, 'end': 15.76}, {'word': ' outbound', 'start': 15.76, 'end': 16.06}, {'word': ' call', 'start': 16.06, 'end': 16.42}, {'word': ' and', 'start': 16.42, 'end': 16.96}, {'word': ' people', 'start': 16.96, 'end': 17.34}, {'word': ' said,', 'start': 17.34, 'end': 17.58}, {'word': ' well,', 'start': 17.66, 'end': 17.72}, {'word': ' how,', 'start': 17.78, 'end': 18.06}, {'word': ' how', 'start': 18.28, 'end': 18.56}, {'word': ' do', 'start': 18.56, 'end': 18.66}, {'word': ' you', 'start': 18.66, 'end': 18.74}, {'word': ' get', 'start': 18.74, 'end': 18.92}, {'word': ' that?', 'start': 18.92, 'end': 19.04}, {'word': ' And', 'start': 19.1, 'end': 19.24}, {'word': ' it', 'start': 19.24, 'end': 19.34}, {'word': ' was', 'start': 19.34, 'end': 19.44}, {'word': ' a', 'start': 19.44, 'end': 19.52}, {'word': ' better', 'start': 19.52, 'end': 19.72}, {'word': ' job.', 'start': 19.72, 'end': 19.98}, {'word': ' They', 'start': 20.08, 'end': 20.18}, {'word': ' go,', 'start': 20.18, 'end': 20.32}, {'word': ' you', 'start': 20.34, 'end': 20.44}, {'word': ' get', 'start': 20.44, 'end': 20.6}, {'word': ' laid', 'start': 20.6, 'end': 20.78}, {'word': ' off', 'start': 20.78, 'end': 20.98}, {'word': ' up.', 'start': 20.98, 'end': 21.26}, {'word': ' How', 'start': 21.38, 'end': 21.52}, {'word': ' does', 'start': 21.52, 'end': 21.68}, {'word': ' that', 'start': 21.68, 'end': 21.82}, {'word': ' happen?', 'start': 21.82, 'end': 22.16}, {'word': ' Okay,', 'start': 23.400000000000002, 'end': 23.88}, {'word': ' welcome', 'start': 23.92, 'end': 24.04}, {'word': ' back', 'start': 24.04, 'end': 24.38}, {'word': ' to', 'start': 24.38, 'end': 24.56}, {'word': ' the', 'start': 24.56, 'end': 24.68}, {'word': ' Ignorant', 'start': 24.68, 'end': 25.0}, {'word': ' Podcast.', 'start': 25.0, 'end': 25.24}, {'word': \" We're\", 'start': 25.68, 'end': 25.96}, {'word': ' here', 'start': 25.96, 'end': 26.1}, {'word': ' with', 'start': 26.1, 'end': 26.24}, {'word': ' Madhu', 'start': 26.24, 'end': 26.54}, {'word': ' and', 'start': 26.54, 'end': 26.92}, {'word': ' Tom', 'start': 26.92, 'end': 27.12}, {'word': ' Singer.', 'start': 27.12, 'end': 27.34}, {'word': ' They', 'start': 27.76, 'end': 28.24}, {'word': ' use', 'start': 28.24, 'end': 28.42}, {'word': ' AI.', 'start': 28.42, 'end': 28.7}, {'word': ' So', 'start': 28.7, 'end': 29.02}, {'word': ' basically', 'start': 29.02, 'end': 29.4}, {'word': ' the', 'start': 29.4, 'end': 29.64}, {'word': ' idea', 'start': 29.64, 'end': 29.86}, {'word': ' was', 'start': 29.86, 'end': 30.2}, {'word': ' if', 'start': 30.2, 'end': 30.64}, {'word': \" you're\", 'start': 30.64, 'end': 30.82}, {'word': ' ignoring', 'start': 30.82, 'end': 31.1}, {'word': ' AI,', 'start': 31.1, 'end': 31.42}, {'word': ' you', 'start': 31.72, 'end': 32.18}, {'word': ' might', 'start': 32.18, 'end': 32.42}, {'word': ' be', 'start': 32.42, 'end': 32.56}, {'word': ' put', 'start': 32.56, 'end': 32.72}, {'word': ' out', 'start': 32.72, 'end': 32.86}, {'word': ' of', 'start': 32.86, 'end': 32.96}, {'word': ' work.', 'start': 32.96, 'end': 33.14}, {'word': ' But', 'start': 33.26, 'end': 33.62}, {'word': ' if', 'start': 33.62, 'end': 33.74}, {'word': \" you're\", 'start': 33.74, 'end': 33.86}, {'word': ' embracing', 'start': 33.86, 'end': 34.26}, {'word': ' it', 'start': 34.26, 'end': 34.42}, {'word': ' and', 'start': 34.42, 'end': 34.5}, {'word': ' using', 'start': 34.5, 'end': 34.74}, {'word': ' it', 'start': 34.74, 'end': 34.94}, {'word': ' and', 'start': 34.94, 'end': 35.0}, {'word': ' realizing', 'start': 35.0, 'end': 35.28}, {'word': ' how', 'start': 35.28, 'end': 35.48}, {'word': ' to', 'start': 35.48, 'end': 35.58}, {'word': ' morph', 'start': 35.58, 'end': 35.8}, {'word': ' where', 'start': 35.8, 'end': 35.96}, {'word': ' your', 'start': 35.96, 'end': 36.1}, {'word': ' skill', 'start': 36.1, 'end': 36.3}, {'word': ' set', 'start': 36.3, 'end': 36.48}, {'word': ' is,', 'start': 36.48, 'end': 36.8}, {'word': \" there'll\", 'start': 37.38, 'end': 37.6}, {'word': ' be', 'start': 37.6, 'end': 37.72}, {'word': ' room', 'start': 37.72, 'end': 38.18}, {'word': ' for', 'start': 38.18, 'end': 38.46}, {'word': ' you.', 'start': 38.46, 'end': 38.56}, {'word': ' Compliments.', 'start': 38.56, 'end': 38.8}, {'word': ' Yeah.', 'start': 39.0, 'end': 39.4}, {'word': \" It's\", 'start': 39.96, 'end': 40.4}, {'word': ' a', 'start': 40.4, 'end': 40.42}, {'word': ' tool', 'start': 40.42, 'end': 40.74}, {'word': ' you', 'start': 40.74, 'end': 41.22}, {'word': ' got', 'start': 41.22, 'end': 41.34}, {'word': ' to', 'start': 41.34, 'end': 41.46}, {'word': ' get', 'start': 41.46, 'end': 41.62}, {'word': ' in.', 'start': 41.62, 'end': 41.78}, {'word': ' I', 'start': 41.96, 'end': 42.06}, {'word': ' think', 'start': 42.06, 'end': 42.26}, {'word': ' people', 'start': 42.26, 'end': 42.84}, {'word': ' has', 'start': 42.84, 'end': 43.1}, {'word': ' misunderstood', 'start': 43.1, 'end': 43.52}, {'word': ' AI.', 'start': 43.52, 'end': 44.16}, {'word': ' The', 'start': 45.5, 'end': 45.94}, {'word': ' education', 'start': 45.94, 'end': 46.32}, {'word': ' part', 'start': 46.32, 'end': 46.7}, {'word': ' of', 'start': 46.7, 'end': 46.88}, {'word': ' this,', 'start': 46.88, 'end': 47.6}, {'word': ' how', 'start': 47.82, 'end': 48.06}, {'word': ' you', 'start': 48.06, 'end': 48.3}, {'word': ' are', 'start': 48.3, 'end': 48.56}, {'word': ' adaptable,', 'start': 48.56, 'end': 49.74}, {'word': ' how', 'start': 49.78, 'end': 50.02}, {'word': ' you', 'start': 50.02, 'end': 50.18}, {'word': ' are', 'start': 50.18, 'end': 50.26}, {'word': ' open', 'start': 50.26, 'end': 50.5}, {'word': ' to', 'start': 50.5, 'end': 50.84}, {'word': ' new', 'start': 50.84, 'end': 52.68}, {'word': ' technologies.', 'start': 52.68, 'end': 53.24}, {'word': ' I', 'start': 53.44, 'end': 53.56}, {'word': ' think', 'start': 53.56, 'end': 53.72}, {'word': ' a', 'start': 53.72, 'end': 53.82}, {'word': ' lot', 'start': 53.82, 'end': 53.9}, {'word': ' of', 'start': 53.9, 'end': 54.0}, {'word': ' people', 'start': 54.0, 'end': 54.2}, {'word': ' are', 'start': 54.2, 'end': 54.36}, {'word': ' very', 'start': 54.36, 'end': 54.52}, {'word': ' hesitant', 'start': 54.52, 'end': 54.96}, {'word': ' because', 'start': 54.96, 'end': 55.58}, {'word': ' part', 'start': 55.58, 'end': 57.8}, {'word': ' of', 'start': 57.8, 'end': 57.96}, {'word': ' this,', 'start': 57.96, 'end': 58.18}, {'word': ' you', 'start': 58.18, 'end': 58.58}, {'word': ' know,', 'start': 58.58, 'end': 58.58}, {'word': ' AI', 'start': 58.58, 'end': 58.58}, {'word': ' is', 'start': 58.58, 'end': 58.58}, {'word': ' what', 'start': 58.58, 'end': 58.58}, {'word': ' are', 'start': 58.58, 'end': 58.76}, {'word': ' you', 'start': 58.76, 'end': 58.84}, {'word': ' here?', 'start': 58.84, 'end': 59.04}, {'word': \" Somebody's\", 'start': 60.14, 'end': 60.54}, {'word': ' writing,', 'start': 60.54, 'end': 60.88}, {'word': \" don't\", 'start': 60.98, 'end': 61.24}, {'word': ' know', 'start': 61.24, 'end': 61.38}, {'word': ' what', 'start': 61.38, 'end': 61.64}, {'word': ' exactly', 'start': 61.64, 'end': 62.14}, {'word': ' I', 'start': 62.14, 'end': 62.52}, {'word': ' can', 'start': 62.52, 'end': 62.8}, {'word': ' do.', 'start': 62.8, 'end': 63.12}, {'word': ' And', 'start': 63.98, 'end': 64.38}, {'word': ' also', 'start': 64.38, 'end': 64.7}, {'word': ' we', 'start': 64.7, 'end': 64.86}, {'word': ' have', 'start': 64.86, 'end': 64.98}, {'word': ' a', 'start': 64.98, 'end': 65.14}, {'word': ' lot', 'start': 65.14, 'end': 65.32}, {'word': ' of', 'start': 65.32, 'end': 65.46}, {'word': ' distraction', 'start': 65.46, 'end': 65.76}, {'word': ' with', 'start': 65.76, 'end': 66.42}, {'word': ' all', 'start': 66.42, 'end': 66.64}, {'word': ' these', 'start': 66.64, 'end': 66.86}, {'word': ' models', 'start': 66.86, 'end': 67.36}, {'word': ' and', 'start': 67.36, 'end': 67.88}, {'word': \" there's\", 'start': 67.88, 'end': 69.26}, {'word': ' a', 'start': 69.26, 'end': 69.38}, {'word': ' dark', 'start': 69.38, 'end': 69.94}, {'word': ' fight', 'start': 69.94, 'end': 70.2}, {'word': ' out', 'start': 70.2, 'end': 70.4}, {'word': ' there.', 'start': 70.4, 'end': 70.64}, {'word': ' So,', 'start': 70.88, 'end': 71.28}, {'word': ' so', 'start': 71.34, 'end': 72.02}, {'word': ' in', 'start': 72.02, 'end': 72.06}, {'word': ' technology.', 'start': 72.06, 'end': 72.52}, {'word': ' So', 'start': 72.84, 'end': 73.18}, {'word': ' what', 'start': 73.18, 'end': 75.1}, {'word': ' is', 'start': 75.1, 'end': 75.32}, {'word': ' the', 'start': 75.32, 'end': 75.42}, {'word': ' other', 'start': 75.42, 'end': 75.58}, {'word': ' question', 'start': 75.58, 'end': 75.88}, {'word': ' you', 'start': 75.88, 'end': 76.12}, {'word': ' have?', 'start': 76.12, 'end': 76.3}, {'word': ' I', 'start': 76.52, 'end': 76.66}, {'word': ' have', 'start': 76.66, 'end': 76.76}, {'word': ' a', 'start': 76.76, 'end': 76.88}, {'word': ' question', 'start': 76.88, 'end': 77.2}, {'word': ' for', 'start': 77.2, 'end': 77.36}, {'word': ' you', 'start': 77.36, 'end': 77.46}, {'word': ' Madhu.', 'start': 77.46, 'end': 77.64}, {'word': \" It's\", 'start': 77.76, 'end': 77.88}, {'word': ' like', 'start': 77.88, 'end': 78.0}, {'word': ' with', 'start': 78.0, 'end': 78.4}, {'word': ' that,', 'start': 78.4, 'end': 78.66}, {'word': ' because,', 'start': 78.78, 'end': 79.12}, {'word': ' you', 'start': 79.26, 'end': 79.46}, {'word': ' know,', 'start': 79.46, 'end': 79.6}, {'word': ' AI', 'start': 79.62, 'end': 79.84}, {'word': ' is', 'start': 79.84, 'end': 80.0}, {'word': ' changing', 'start': 80.0, 'end': 80.38}, {'word': ' the', 'start': 80.38, 'end': 80.56}, {'word': ' job', 'start': 80.56, 'end': 80.88}, {'word': ' market', 'start': 80.88, 'end': 81.22}, {'word': ' and', 'start': 81.22, 'end': 81.38}, {'word': ' the', 'start': 81.38, 'end': 81.52}, {'word': ' job,', 'start': 81.52, 'end': 81.84}, {'word': ' like,', 'start': 81.9, 'end': 82.2}, {'word': ' you', 'start': 82.22, 'end': 82.82}, {'word': ' know,', 'start': 82.82, 'end': 83.02}, {'word': ' scope.', 'start': 83.02, 'end': 83.32}, {'word': ' Where', 'start': 83.48, 'end': 83.82}, {'word': ' do', 'start': 83.82, 'end': 83.96}, {'word': ' you', 'start': 83.96, 'end': 84.04}, {'word': ' kind', 'start': 84.04, 'end': 84.18}, {'word': ' of', 'start': 84.18, 'end': 84.28}, {'word': ' see,', 'start': 84.28, 'end': 84.58}, {'word': ' like', 'start': 84.68, 'end': 85.04}, {'word': ' if', 'start': 85.04, 'end': 85.2}, {'word': ' people', 'start': 85.2, 'end': 85.38}, {'word': ' are', 'start': 85.38, 'end': 85.54}, {'word': ' still', 'start': 85.54, 'end': 85.66}, {'word': ' getting', 'start': 85.66, 'end': 85.82}, {'word': ' laid', 'start': 85.82, 'end': 86.06}, {'word': ' off,', 'start': 86.06, 'end': 86.22}, {'word': ' obviously', 'start': 86.34, 'end': 86.8}, {'word': \" that's\", 'start': 86.8, 'end': 87.1}, {'word': ' still', 'start': 87.1, 'end': 87.3}, {'word': ' very', 'start': 87.3, 'end': 87.52}, {'word': ' scary.', 'start': 87.52, 'end': 87.84}, {'word': ' I', 'start': 87.86, 'end': 88.06}, {'word': ' suppose.', 'start': 88.06, 'end': 88.14}, {'word': ' So', 'start': 88.14, 'end': 88.22}, {'word': ' Bumble,', 'start': 88.22, 'end': 88.56}, {'word': ' which', 'start': 88.64, 'end': 88.8}, {'word': ' is', 'start': 88.8, 'end': 88.9}, {'word': ' an', 'start': 88.9, 'end': 88.98}, {'word': ' Austin', 'start': 88.98, 'end': 89.16}, {'word': ' company,', 'start': 89.16, 'end': 89.5}, {'word': ' that', 'start': 89.66, 'end': 89.74}, {'word': ' was', 'start': 89.74, 'end': 89.9}, {'word': ' like', 'start': 89.9, 'end': 90.04}, {'word': ' a', 'start': 90.04, 'end': 90.1}, {'word': ' 30%.', 'start': 90.1, 'end': 90.66}, {'word': ' So', 'start': 91.14, 'end': 91.36}, {'word': ' if,', 'start': 91.36, 'end': 91.98}, {'word': ' you', 'start': 92.12, 'end': 92.26}, {'word': ' know,', 'start': 92.26, 'end': 92.42}, {'word': \" we're\", 'start': 92.44, 'end': 92.62}, {'word': ' moving', 'start': 92.62, 'end': 92.8}, {'word': ' away', 'start': 92.8, 'end': 93.12}, {'word': ' from', 'start': 93.12, 'end': 93.42}, {'word': ' certain', 'start': 93.42, 'end': 93.72}, {'word': ' jobs,', 'start': 93.72, 'end': 93.96}, {'word': ' what', 'start': 94.06, 'end': 94.18}, {'word': ' jobs', 'start': 94.18, 'end': 94.46}, {'word': ' do', 'start': 94.46, 'end': 94.6}, {'word': ' you', 'start': 94.6, 'end': 94.64}, {'word': ' kind', 'start': 94.64, 'end': 94.78}, {'word': ' of', 'start': 94.78, 'end': 94.88}, {'word': ' see', 'start': 94.88, 'end': 95.04}, {'word': ' opening', 'start': 95.04, 'end': 95.34}, {'word': ' or', 'start': 95.34, 'end': 95.78}, {'word': ' what', 'start': 95.78, 'end': 96.08}, {'word': ' do', 'start': 96.08, 'end': 96.16}, {'word': ' you', 'start': 96.16, 'end': 96.22}, {'word': ' kind', 'start': 96.22, 'end': 96.34}, {'word': ' of', 'start': 96.34, 'end': 96.42}, {'word': ' see', 'start': 96.42, 'end': 96.56}, {'word': ' in', 'start': 96.56, 'end': 96.68}, {'word': ' the', 'start': 96.68, 'end': 96.74}, {'word': ' job', 'start': 96.74, 'end': 96.92}, {'word': ' market?', 'start': 96.92, 'end': 97.18}, {'word': ' You', 'start': 97.9, 'end': 98.34}, {'word': ' have', 'start': 98.34, 'end': 98.5}, {'word': ' been', 'start': 98.5, 'end': 98.64}, {'word': ' involved', 'start': 98.64, 'end': 99.02}, {'word': ' in', 'start': 99.02, 'end': 99.28}, {'word': ' our', 'start': 99.28, 'end': 99.4}, {'word': ' meetings.', 'start': 99.4, 'end': 99.82}, {'word': ' We', 'start': 99.86, 'end': 100.2}, {'word': ' welcome', 'start': 100.2, 'end': 100.82}, {'word': ' interns', 'start': 100.82, 'end': 101.42}, {'word': ' to', 'start': 101.42, 'end': 101.94}, {'word': ' come', 'start': 101.94, 'end': 102.2}, {'word': ' in', 'start': 102.2, 'end': 102.46}, {'word': ' to', 'start': 102.46, 'end': 102.88}, {'word': ' our', 'start': 102.88, 'end': 103.18}, {'word': ' companies', 'start': 103.18, 'end': 103.66}, {'word': ' and', 'start': 103.66, 'end': 104.12}, {'word': ' see', 'start': 104.12, 'end': 104.5}, {'word': ' the', 'start': 104.5, 'end': 104.74}, {'word': ' difference,', 'start': 104.74, 'end': 105.04}, {'word': ' what', 'start': 105.12, 'end': 105.32}, {'word': ' we', 'start': 105.32, 'end': 105.5}, {'word': ' see.', 'start': 105.5, 'end': 105.78}, {'word': ' The,', 'start': 107.62, 'end': 108.06}, {'word': ' I', 'start': 108.22, 'end': 108.54}, {'word': \" don't\", 'start': 108.54, 'end': 108.76}, {'word': ' care', 'start': 108.76, 'end': 108.94}, {'word': ' your', 'start': 108.94, 'end': 109.18}, {'word': ' resume', 'start': 109.18, 'end': 109.48}, {'word': ' anymore.', 'start': 109.48, 'end': 110.16}, {'word': ' I', 'start': 110.96000000000001, 'end': 111.4}, {'word': ' mean,', 'start': 111.4, 'end': 111.6}, {'word': ' I', 'start': 111.64, 'end': 111.82}, {'word': ' saw', 'start': 111.82, 'end': 112.1}, {'word': ' an', 'start': 112.1, 'end': 112.24}, {'word': ' article', 'start': 112.24, 'end': 112.46}, {'word': ' this', 'start': 112.46, 'end': 112.76}, {'word': ' morning,', 'start': 112.76, 'end': 113.2}, {'word': ' how', 'start': 113.5, 'end': 113.88}, {'word': ' to', 'start': 113.88, 'end': 114.02}, {'word': ' write', 'start': 114.02, 'end': 114.3}, {'word': ' a', 'start': 114.3, 'end': 114.4}, {'word': ' resume', 'start': 114.4, 'end': 114.62}, {'word': ' here', 'start': 114.62, 'end': 114.86}, {'word': ' is', 'start': 114.86, 'end': 115.06}, {'word': ' a', 'start': 115.06, 'end': 115.08}, {'word': ' tool.', 'start': 115.08, 'end': 115.34}, {'word': ' Stop', 'start': 115.56, 'end': 115.92}, {'word': ' doing', 'start': 115.92, 'end': 116.4}, {'word': ' that.', 'start': 116.4, 'end': 116.74}, {'word': ' So', 'start': 118.48, 'end': 119.0}, {'word': \" I'm\", 'start': 119.0, 'end': 119.02}, {'word': ' going', 'start': 119.02, 'end': 119.02}, {'word': ' to', 'start': 119.02, 'end': 119.02}, {'word': ' ask', 'start': 119.02, 'end': 119.02}, {'word': ' you', 'start': 119.02, 'end': 119.02}, {'word': ' a', 'start': 119.02, 'end': 119.02}, {'word': ' question.', 'start': 119.02, 'end': 119.02}, {'word': ' So', 'start': 119.02, 'end': 119.02}, {'word': ' tell', 'start': 119.02, 'end': 119.02}, {'word': ' the', 'start': 119.02, 'end': 119.36}, {'word': ' world,', 'start': 119.36, 'end': 119.84}, {'word': ' tell', 'start': 120.02, 'end': 121.26}, {'word': ' me,', 'start': 121.26, 'end': 121.38}, {'word': ' go', 'start': 121.44, 'end': 121.52}, {'word': ' back', 'start': 121.52, 'end': 121.72}, {'word': ' to', 'start': 121.72, 'end': 121.94}, {'word': ' the', 'start': 121.94, 'end': 122.08}, {'word': ' cover', 'start': 122.08, 'end': 122.32}, {'word': ' letter,', 'start': 122.32, 'end': 122.58}, {'word': ' right?', 'start': 122.74, 'end': 123.18}, {'word': ' Give', 'start': 123.26, 'end': 123.72}, {'word': ' me', 'start': 123.72, 'end': 123.86}, {'word': ' your', 'start': 123.86, 'end': 123.96}, {'word': ' cover', 'start': 123.96, 'end': 124.22}, {'word': ' letter.', 'start': 124.22, 'end': 124.64}, {'word': ' In', 'start': 124.68, 'end': 125.18}, {'word': ' one', 'start': 125.18, 'end': 125.46}, {'word': ' paragraph,', 'start': 125.46, 'end': 125.88}, {'word': ' there', 'start': 126.26, 'end': 127.04}, {'word': ' is', 'start': 127.04, 'end': 127.18}, {'word': ' a', 'start': 127.18, 'end': 127.24}, {'word': ' project', 'start': 127.24, 'end': 127.62}, {'word': ' you', 'start': 127.62, 'end': 127.84}, {'word': ' have', 'start': 127.84, 'end': 127.96}, {'word': ' done', 'start': 127.96, 'end': 128.24}, {'word': ' can', 'start': 128.24, 'end': 128.76}, {'word': ' help', 'start': 128.76, 'end': 129.08}, {'word': ' me.', 'start': 129.08, 'end': 129.34}, {'word': ' Right.', 'start': 129.46, 'end': 129.76}, {'word': ' And', 'start': 130.28, 'end': 130.8}, {'word': ' I', 'start': 130.8, 'end': 130.94}, {'word': ' can,', 'start': 130.94, 'end': 131.14}, {'word': ' next', 'start': 131.24, 'end': 132.34}, {'word': ' day', 'start': 132.34, 'end': 132.64}, {'word': ' you', 'start': 132.64, 'end': 132.76}, {'word': ' have', 'start': 132.76, 'end': 132.92}, {'word': ' a', 'start': 132.92, 'end': 133.06}, {'word': ' job.', 'start': 133.06, 'end': 133.32}, {'word': ' I', 'start': 133.94, 'end': 134.46}, {'word': \" don't\", 'start': 134.46, 'end': 134.76}, {'word': ' care', 'start': 134.76, 'end': 135.12}, {'word': ' about', 'start': 135.12, 'end': 135.42}, {'word': ' industry.', 'start': 135.42, 'end': 135.7}, {'word': ' I', 'start': 135.9, 'end': 135.98}, {'word': \" don't\", 'start': 135.98, 'end': 136.2}, {'word': ' care', 'start': 136.2, 'end': 136.44}, {'word': ' about', 'start': 136.44, 'end': 136.68}, {'word': ' your', 'start': 136.68, 'end': 136.86}, {'word': ' grades.', 'start': 136.86, 'end': 137.26}, {'word': \" You're\", 'start': 137.46, 'end': 137.7}, {'word': ' maybe', 'start': 137.7, 'end': 137.94}, {'word': ' a', 'start': 137.94, 'end': 138.08}, {'word': ' scholar,', 'start': 138.08, 'end': 138.4}, {'word': ' PhD.', 'start': 138.54, 'end': 138.82}, {'word': ' Are', 'start': 140.04, 'end': 140.56}, {'word': ' you', 'start': 140.56, 'end': 140.82}, {'word': ' solving', 'start': 140.82, 'end': 141.3}, {'word': ' a', 'start': 141.3, 'end': 141.58}, {'word': ' real', 'start': 141.58, 'end': 141.9}, {'word': ' business', 'start': 141.9, 'end': 142.36}, {'word': ' problem?', 'start': 142.36, 'end': 142.92}, {'word': ' Are', 'start': 143.5, 'end': 144.02}, {'word': ' you', 'start': 144.02, 'end': 144.28}, {'word': ' helping', 'start': 144.28, 'end': 144.72}, {'word': ' my', 'start': 144.72, 'end': 145.76}, {'word': ' company', 'start': 145.76, 'end': 146.32}, {'word': ' and', 'start': 146.32, 'end': 146.62}, {'word': ' companies', 'start': 146.62, 'end': 147.14}, {'word': ' are', 'start': 147.14, 'end': 147.4}, {'word': ' trying', 'start': 147.4, 'end': 147.68}, {'word': ' to.', 'start': 147.68, 'end': 147.84}, {'word': ' I', 'start': 147.84, 'end': 147.98}, {'word': ' hire', 'start': 147.98, 'end': 148.08}, {'word': ' you.', 'start': 148.08, 'end': 148.4}, {'word': ' Why', 'start': 149.22, 'end': 149.7}, {'word': ' do', 'start': 149.7, 'end': 149.88}, {'word': ' we', 'start': 149.88, 'end': 150.02}, {'word': ' need', 'start': 150.02, 'end': 150.14}, {'word': ' to', 'start': 150.14, 'end': 150.24}, {'word': ' hire', 'start': 150.24, 'end': 150.46}, {'word': ' you?', 'start': 150.46, 'end': 150.66}, {'word': ' You', 'start': 150.7, 'end': 150.86}, {'word': ' need', 'start': 150.86, 'end': 151.02}, {'word': ' to', 'start': 151.02, 'end': 151.16}, {'word': ' justify.', 'start': 151.16, 'end': 151.56}, {'word': ' What', 'start': 152.38000000000002, 'end': 152.86}, {'word': ' do', 'start': 152.86, 'end': 152.98}, {'word': ' you', 'start': 152.98, 'end': 153.04}, {'word': ' bring', 'start': 153.04, 'end': 153.28}, {'word': ' it', 'start': 153.28, 'end': 153.42}, {'word': ' to', 'start': 153.42, 'end': 153.52}, {'word': ' the', 'start': 153.52, 'end': 153.62}, {'word': ' table?', 'start': 153.62, 'end': 153.98}, {'word': ' Are', 'start': 155.10000000000002, 'end': 155.58}, {'word': ' you', 'start': 155.58, 'end': 155.82}, {'word': ' open', 'start': 155.82, 'end': 156.14}, {'word': ' to', 'start': 156.14, 'end': 156.4}, {'word': ' learning', 'start': 156.4, 'end': 156.74}, {'word': ' and', 'start': 156.74, 'end': 157.12}, {'word': ' creating', 'start': 157.12, 'end': 157.56}, {'word': ' solutions?', 'start': 157.56, 'end': 158.06}, {'word': ' I', 'start': 158.56, 'end': 158.66}, {'word': ' think', 'start': 158.66, 'end': 158.94}, {'word': ' we', 'start': 158.94, 'end': 159.08}, {'word': ' are', 'start': 159.08, 'end': 159.18}, {'word': ' already', 'start': 159.18, 'end': 159.3}, {'word': ' creative', 'start': 159.3, 'end': 159.7}, {'word': ' all', 'start': 159.7, 'end': 159.84}, {'word': ' now.', 'start': 159.84, 'end': 160.08}, {'word': ' Oh,', 'start': 160.32, 'end': 160.8}, {'word': ' for', 'start': 160.8, 'end': 160.86}, {'word': ' sure.', 'start': 160.86, 'end': 160.94}, {'word': ' That', 'start': 161.04, 'end': 161.04}, {'word': ' clear.', 'start': 161.04, 'end': 161.32}, {'word': ' For', 'start': 161.62, 'end': 161.86}, {'word': ' sure.', 'start': 161.86, 'end': 162.04}, {'word': \" It's\", 'start': 162.42, 'end': 162.9}, {'word': ' a', 'start': 162.9, 'end': 163.12}, {'word': ' paper', 'start': 163.12, 'end': 163.66}, {'word': ' trail', 'start': 163.66, 'end': 163.94}, {'word': ' work', 'start': 163.94, 'end': 164.54}, {'word': ' days', 'start': 164.54, 'end': 165.12}, {'word': ' gone', 'start': 165.12, 'end': 165.44}, {'word': ' that', 'start': 165.44, 'end': 166.12}, {'word': ' can', 'start': 166.12, 'end': 166.4}, {'word': ' replace', 'start': 166.4, 'end': 166.84}, {'word': ' the', 'start': 166.84, 'end': 167.08}, {'word': ' AI.', 'start': 167.08, 'end': 167.32}, {'word': ' Where', 'start': 168.24, 'end': 168.72}, {'word': ' our,', 'start': 168.72, 'end': 169.04}, {'word': ' we', 'start': 169.14, 'end': 169.38}, {'word': ' see', 'start': 169.38, 'end': 169.76}, {'word': ' is,', 'start': 169.76, 'end': 170.7}, {'word': ' yeah,', 'start': 170.96, 'end': 171.54}, {'word': \" he's\", 'start': 171.68, 'end': 172.24}, {'word': ' a,', 'start': 172.24, 'end': 172.26}, {'word': ' I', 'start': 172.38, 'end': 173.04}, {'word': ' know', 'start': 173.04, 'end': 173.26}, {'word': \" it's\", 'start': 173.26, 'end': 173.46}, {'word': ' hard', 'start': 173.46, 'end': 173.62}, {'word': ' to', 'start': 173.62, 'end': 173.86}, {'word': ' say,', 'start': 173.86, 'end': 174.08}, {'word': ' but', 'start': 174.18, 'end': 174.34}, {'word': ' everybody', 'start': 174.34, 'end': 175.42}, {'word': ' has', 'start': 175.42, 'end': 175.94}, {'word': ' talent.', 'start': 175.94, 'end': 176.42}, {'word': ' But', 'start': 177.22000000000003, 'end': 177.58}, {'word': ' I', 'start': 177.58, 'end': 177.78}, {'word': \" don't\", 'start': 177.78, 'end': 178.0}, {'word': ' think', 'start': 178.0, 'end': 178.18}, {'word': ' so.', 'start': 178.18, 'end': 178.3}, {'word': ' They', 'start': 178.32, 'end': 178.42}, {'word': ' use', 'start': 178.42, 'end': 178.78}, {'word': ' it.', 'start': 178.78, 'end': 179.04}, {'word': ' Well,', 'start': 179.04, 'end': 179.22}, {'word': ' they', 'start': 179.38, 'end': 179.8}, {'word': ' get', 'start': 179.8, 'end': 180.02}, {'word': ' distracted', 'start': 180.02, 'end': 180.62}, {'word': ' with,', 'start': 180.62, 'end': 181.1}, {'word': ' oh,', 'start': 181.32, 'end': 181.86}, {'word': ' I', 'start': 181.94, 'end': 182.06}, {'word': ' need', 'start': 182.06, 'end': 182.24}, {'word': ' to', 'start': 182.24, 'end': 182.4}, {'word': ' get', 'start': 182.4, 'end': 182.54}, {'word': ' framed', 'start': 182.54, 'end': 183.78}, {'word': ' resume.', 'start': 183.78, 'end': 184.22}, {'word': ' I', 'start': 184.44, 'end': 184.8}, {'word': ' need', 'start': 184.8, 'end': 184.96}, {'word': ' to', 'start': 184.96, 'end': 185.1}, {'word': ' have', 'start': 185.1, 'end': 185.2}, {'word': ' keywords', 'start': 185.2, 'end': 185.66}, {'word': ' in', 'start': 185.66, 'end': 186.24}, {'word': ' a', 'start': 186.24, 'end': 186.3}, {'word': ' resume.', 'start': 186.3, 'end': 186.62}, {'word': ' Right.', 'start': 186.82, 'end': 187.02}, {'word': ' We', 'start': 187.16, 'end': 187.52}, {'word': ' are', 'start': 187.52, 'end': 187.66}, {'word': ' in', 'start': 187.66, 'end': 187.8}, {'word': ' that', 'start': 187.8, 'end': 187.96}, {'word': ' space', 'start': 187.96, 'end': 188.82}, {'word': ' where', 'start': 188.82, 'end': 189.04}, {'word': ' like,', 'start': 189.04, 'end': 189.2}, {'word': ' okay,', 'start': 189.24, 'end': 189.62}, {'word': ' give', 'start': 189.74, 'end': 190.28}, {'word': ' me', 'start': 190.28, 'end': 190.48}, {'word': ' a', 'start': 190.48, 'end': 190.52}, {'word': ' one', 'start': 190.52, 'end': 190.76}, {'word': ' project', 'start': 190.76, 'end': 191.24}, {'word': ' you', 'start': 191.24, 'end': 191.66}, {'word': ' delivered', 'start': 191.66, 'end': 192.06}, {'word': ' out', 'start': 192.06, 'end': 192.32}, {'word': ' there.', 'start': 192.32, 'end': 192.46}, {'word': ' You', 'start': 192.5, 'end': 192.56}, {'word': ' put', 'start': 192.56, 'end': 192.74}, {'word': ' it', 'start': 192.74, 'end': 192.92}, {'word': ' out', 'start': 192.92, 'end': 193.04}, {'word': ' there', 'start': 193.04, 'end': 193.28}, {'word': ' and', 'start': 193.28, 'end': 194.74}, {'word': ' where', 'start': 194.74, 'end': 195.36}, {'word': ' I', 'start': 195.36, 'end': 195.54}, {'word': ' can', 'start': 195.54, 'end': 195.72}, {'word': ' look', 'start': 195.72, 'end': 195.9}, {'word': ' at', 'start': 195.9, 'end': 196.08}, {'word': ' it', 'start': 196.08, 'end': 196.3}, {'word': ' and', 'start': 196.3, 'end': 197.1}, {'word': ' see', 'start': 197.1, 'end': 197.34}, {'word': ' if', 'start': 197.34, 'end': 197.5}, {'word': \" it's\", 'start': 197.5, 'end': 197.64}, {'word': ' giving', 'start': 197.64, 'end': 197.82}, {'word': ' any', 'start': 197.82, 'end': 198.32}, {'word': ' value', 'start': 198.32, 'end': 198.66}, {'word': ' to', 'start': 198.66, 'end': 198.9}, {'word': ' us.', 'start': 198.9, 'end': 199.04}, {'word': ' Oh,', 'start': 199.1, 'end': 199.24}, {'word': ' I', 'start': 199.34, 'end': 199.46}, {'word': \" didn't\", 'start': 199.46, 'end': 199.7}, {'word': ' think', 'start': 199.7, 'end': 199.86}, {'word': ' about', 'start': 199.86, 'end': 200.16}, {'word': ' it.', 'start': 200.16, 'end': 200.32}, {'word': ' How', 'start': 200.38, 'end': 200.48}, {'word': ' many', 'start': 200.48, 'end': 200.64}, {'word': ' times', 'start': 200.64, 'end': 200.86}, {'word': ' we', 'start': 200.86, 'end': 201.0}, {'word': ' talk', 'start': 201.0, 'end': 201.18}, {'word': ' in', 'start': 201.18, 'end': 201.36}, {'word': ' a', 'start': 201.36, 'end': 201.44}, {'word': ' meeting?', 'start': 201.44, 'end': 201.74}, {'word': ' Oh,', 'start': 201.86, 'end': 202.22}, {'word': ' I', 'start': 202.26, 'end': 202.36}, {'word': \" didn't\", 'start': 202.36, 'end': 202.5}, {'word': ' think', 'start': 202.5, 'end': 202.7}, {'word': ' about', 'start': 202.7, 'end': 202.98}, {'word': ' this.', 'start': 202.98, 'end': 203.26}, {'word': \" That's\", 'start': 203.66000000000003, 'end': 204.02}, {'word': ' what', 'start': 204.02, 'end': 204.12}, {'word': ' I', 'start': 204.12, 'end': 204.26}, {'word': ' expect', 'start': 204.26, 'end': 204.6}, {'word': ' in', 'start': 204.6, 'end': 204.82}, {'word': ' new', 'start': 204.82, 'end': 204.94}, {'word': ' generation', 'start': 204.94, 'end': 205.16}, {'word': ' is', 'start': 205.16, 'end': 205.72}, {'word': ' they', 'start': 205.72, 'end': 205.88}, {'word': ' have.', 'start': 205.88, 'end': 206.06}, {'word': ' Amazing', 'start': 206.88, 'end': 207.28}, {'word': ' talent.', 'start': 207.28, 'end': 207.68}, {'word': ' I', 'start': 207.88, 'end': 208.28}, {'word': ' think', 'start': 208.28, 'end': 208.82}, {'word': ' they', 'start': 208.82, 'end': 208.96}, {'word': ' have', 'start': 208.96, 'end': 209.1}, {'word': ' their', 'start': 209.1, 'end': 209.76}, {'word': ' last', 'start': 209.76, 'end': 210.06}, {'word': ' focus.', 'start': 210.06, 'end': 210.88}, {'word': ' Well,', 'start': 211.3, 'end': 211.7}, {'word': ' and', 'start': 211.72, 'end': 211.86}, {'word': ' the', 'start': 211.86, 'end': 212.18}, {'word': ' other', 'start': 212.18, 'end': 212.38}, {'word': ' thing', 'start': 212.38, 'end': 212.54}, {'word': ' is,', 'start': 212.54, 'end': 212.92}, {'word': ' is', 'start': 212.92, 'end': 213.18}, {'word': ' that', 'start': 213.18, 'end': 213.32}, {'word': ' with', 'start': 213.32, 'end': 213.78}, {'word': ' the', 'start': 213.78, 'end': 214.0}, {'word': ' job', 'start': 214.0, 'end': 214.22}, {'word': ' market,', 'start': 214.22, 'end': 214.52}, {'word': ' the', 'start': 214.58, 'end': 214.7}, {'word': ' way', 'start': 214.7, 'end': 214.84}, {'word': ' it', 'start': 214.84, 'end': 214.92}, {'word': ' is', 'start': 214.92, 'end': 215.06}, {'word': ' now,', 'start': 215.06, 'end': 215.28}, {'word': ' your', 'start': 215.54, 'end': 216.16}, {'word': ' reputation,', 'start': 216.16, 'end': 216.66}, {'word': ' your', 'start': 216.86, 'end': 217.02}, {'word': ' brand', 'start': 217.02, 'end': 217.36}, {'word': ' and', 'start': 217.36, 'end': 217.46}, {'word': ' your', 'start': 217.46, 'end': 217.56}, {'word': ' network', 'start': 217.56, 'end': 217.88}, {'word': ' as', 'start': 217.88, 'end': 218.6}, {'word': ' an', 'start': 218.6, 'end': 218.76}, {'word': ' individual', 'start': 218.76, 'end': 219.2}, {'word': ' are', 'start': 219.2, 'end': 219.78}, {'word': ' going', 'start': 219.78, 'end': 219.98}, {'word': ' to', 'start': 219.98, 'end': 220.08}, {'word': ' be', 'start': 220.08, 'end': 220.14}, {'word': ' more', 'start': 220.14, 'end': 220.34}, {'word': ' important', 'start': 220.34, 'end': 220.66}, {'word': ' than', 'start': 220.66, 'end': 220.8}, {'word': ' ever.', 'start': 220.8, 'end': 220.96}, {'word': ' And', 'start': 221.04, 'end': 221.12}, {'word': ' part', 'start': 221.12, 'end': 221.22}, {'word': ' of', 'start': 221.22, 'end': 221.38}, {'word': ' that', 'start': 221.38, 'end': 221.5}, {'word': ' is', 'start': 221.5, 'end': 221.74}, {'word': ' how', 'start': 221.74, 'end': 222.1}, {'word': ' do', 'start': 222.1, 'end': 222.2}, {'word': ' you', 'start': 222.2, 'end': 222.42}, {'word': ' tell', 'start': 222.42, 'end': 222.8}, {'word': ' a', 'start': 222.8, 'end': 222.94}, {'word': ' particular', 'start': 222.94, 'end': 223.14}, {'word': ' employer', 'start': 223.14, 'end': 224.12}, {'word': ' what', 'start': 224.12, 'end': 224.6}, {'word': ' the', 'start': 224.6, 'end': 224.7}, {'word': ' projects', 'start': 224.7, 'end': 225.04}, {'word': \" you've\", 'start': 225.04, 'end': 225.24}, {'word': ' worked', 'start': 225.24, 'end': 225.4}, {'word': ' on', 'start': 225.4, 'end': 225.62}, {'word': ' and', 'start': 225.62, 'end': 225.82}, {'word': ' done', 'start': 225.82, 'end': 226.14}, {'word': ' it.', 'start': 226.14, 'end': 226.28}, {'word': ' But', 'start': 226.34, 'end': 226.68}, {'word': ' also', 'start': 226.68, 'end': 227.02}, {'word': ' as', 'start': 227.02, 'end': 227.38}, {'word': ' the', 'start': 227.38, 'end': 227.58}, {'word': ' employer,', 'start': 227.58, 'end': 227.94}, {'word': ' if', 'start': 228.04, 'end': 228.22}, {'word': ' I', 'start': 228.22, 'end': 228.34}, {'word': ' call', 'start': 228.34, 'end': 228.62}, {'word': ' you', 'start': 228.62, 'end': 228.88}, {'word': ' and', 'start': 228.88, 'end': 229.6}, {'word': ' say,', 'start': 229.6, 'end': 229.9}, {'word': ' Hey,', 'start': 230.0, 'end': 230.18}, {'word': ' you', 'start': 230.2, 'end': 230.3}, {'word': ' need', 'start': 230.3, 'end': 230.46}, {'word': ' to', 'start': 230.46, 'end': 230.54}, {'word': ' meet', 'start': 230.54, 'end': 230.66}, {'word': ' Matt.', 'start': 230.66, 'end': 230.82}, {'word': \" He's\", 'start': 231.48, 'end': 231.88}, {'word': ' perfect', 'start': 231.88, 'end': 232.18}, {'word': ' for', 'start': 232.18, 'end': 232.4}, {'word': ' what', 'start': 232.4, 'end': 232.5}, {'word': \" you're\", 'start': 232.5, 'end': 232.64}, {'word': ' working', 'start': 232.64, 'end': 232.9}, {'word': ' on.', 'start': 232.9, 'end': 233.14}, {'word': \" You're\", 'start': 233.18, 'end': 233.52}, {'word': ' more', 'start': 233.52, 'end': 233.72}, {'word': ' apt', 'start': 233.72, 'end': 234.0}, {'word': ' to', 'start': 234.0, 'end': 234.22}, {'word': ' talk', 'start': 234.22, 'end': 234.44}, {'word': ' to', 'start': 234.44, 'end': 234.64}, {'word': ' him', 'start': 234.64, 'end': 234.86}, {'word': ' and', 'start': 234.86, 'end': 235.06}, {'word': ' possibly', 'start': 235.06, 'end': 235.36}, {'word': ' hiring.', 'start': 235.36, 'end': 235.82}, {'word': ' And', 'start': 236.42, 'end': 236.6}, {'word': ' yet', 'start': 236.6, 'end': 237.3}, {'word': ' the', 'start': 237.3, 'end': 237.46}, {'word': ' younger', 'start': 237.46, 'end': 237.66}, {'word': ' generation', 'start': 237.66, 'end': 238.16}, {'word': ' is', 'start': 238.16, 'end': 238.4}, {'word': ' more', 'start': 238.4, 'end': 238.6}, {'word': ' apt', 'start': 238.6, 'end': 238.86}, {'word': ' not', 'start': 238.86, 'end': 239.16}, {'word': ' to', 'start': 239.16, 'end': 239.38}, {'word': ' go', 'start': 239.38, 'end': 239.5}, {'word': ' to', 'start': 239.5, 'end': 239.58}, {'word': ' networking', 'start': 239.58, 'end': 239.84}, {'word': ' events,', 'start': 239.84, 'end': 240.22}, {'word': ' not', 'start': 240.4, 'end': 240.6}, {'word': ' to', 'start': 240.6, 'end': 240.86}, {'word': ' join', 'start': 240.86, 'end': 241.1}, {'word': ' organizations,', 'start': 241.1, 'end': 241.74}, {'word': ' not', 'start': 242.04, 'end': 243.02}, {'word': ' to', 'start': 243.02, 'end': 243.64}, {'word': ' really', 'start': 243.64, 'end': 244.78}, {'word': ' go', 'start': 244.78, 'end': 244.98}, {'word': ' out', 'start': 244.98, 'end': 245.16}, {'word': ' even', 'start': 245.16, 'end': 245.38}, {'word': ' socialize', 'start': 245.38, 'end': 246.06}, {'word': ' with', 'start': 246.06, 'end': 246.32}, {'word': ' people', 'start': 246.32, 'end': 246.56}, {'word': ' in', 'start': 246.56, 'end': 246.72}, {'word': ' their', 'start': 246.72, 'end': 246.82}, {'word': ' industry.', 'start': 246.82, 'end': 247.16}, {'word': ' They', 'start': 247.22, 'end': 247.34}, {'word': \" don't\", 'start': 247.34, 'end': 247.46}, {'word': ' go', 'start': 247.46, 'end': 247.56}, {'word': ' to', 'start': 247.56, 'end': 247.66}, {'word': ' national', 'start': 247.66, 'end': 247.9}, {'word': ' conferences', 'start': 247.9, 'end': 248.44}, {'word': ' in', 'start': 248.44, 'end': 248.66}, {'word': ' their', 'start': 248.66, 'end': 248.74}, {'word': ' industry.', 'start': 248.74, 'end': 249.08}, {'word': ' And', 'start': 249.4, 'end': 249.8}, {'word': ' then', 'start': 249.8, 'end': 249.98}, {'word': ' they', 'start': 249.98, 'end': 250.12}, {'word': ' are', 'start': 250.12, 'end': 250.26}, {'word': ' like,', 'start': 250.26, 'end': 250.52}, {'word': ' oh,', 'start': 250.6, 'end': 250.78}, {'word': ' well,', 'start': 250.84, 'end': 250.98}, {'word': ' everybody', 'start': 251.06, 'end': 251.32}, {'word': ' gets', 'start': 251.32, 'end': 251.6}, {'word': ' their', 'start': 251.6, 'end': 251.72}, {'word': ' job', 'start': 251.72, 'end': 252.0}, {'word': ' through', 'start': 252.0, 'end': 252.16}, {'word': ' their', 'start': 252.16, 'end': 252.3}, {'word': ' network,', 'start': 252.3, 'end': 252.6}, {'word': ' but', 'start': 252.7, 'end': 252.82}, {'word': ' I', 'start': 252.82, 'end': 252.96}, {'word': \" don't\", 'start': 252.96, 'end': 253.04}, {'word': ' have', 'start': 253.04, 'end': 253.12}, {'word': ' a', 'start': 253.12, 'end': 253.18}, {'word': ' network.', 'start': 253.18, 'end': 253.42}, {'word': ' And', 'start': 253.6, 'end': 253.68}, {'word': \" it's\", 'start': 253.68, 'end': 253.84}, {'word': ' like,', 'start': 253.84, 'end': 253.96}, {'word': ' well,', 'start': 253.96, 'end': 254.02}, {'word': ' you', 'start': 254.1, 'end': 254.38}, {'word': ' have', 'start': 254.38, 'end': 254.54}, {'word': ' to', 'start': 254.54, 'end': 254.66}, {'word': ' create', 'start': 254.66, 'end': 254.94}, {'word': ' that', 'start': 254.94, 'end': 255.12}, {'word': ' yourself.', 'start': 255.12, 'end': 255.34}, {'word': ' And', 'start': 255.52, 'end': 255.88}, {'word': ' I', 'start': 255.88, 'end': 256.38}, {'word': ' call', 'start': 256.38, 'end': 256.64}, {'word': ' it', 'start': 256.64, 'end': 256.76}, {'word': ' human', 'start': 256.76, 'end': 256.96}, {'word': ' interaction,', 'start': 256.96, 'end': 257.4}, {'word': ' H', 'start': 257.64, 'end': 258.02}, {'word': '.I.', 'start': 258.02, 'end': 258.42}, {'word': ' And', 'start': 258.7, 'end': 259.1}, {'word': ' I', 'start': 259.1, 'end': 259.24}, {'word': ' talk', 'start': 259.24, 'end': 259.42}, {'word': ' about', 'start': 259.42, 'end': 259.62}, {'word': ' the', 'start': 259.62, 'end': 259.74}, {'word': ' fact', 'start': 259.74, 'end': 259.94}, {'word': ' that', 'start': 259.94, 'end': 260.24}, {'word': ' H', 'start': 260.24, 'end': 260.54}, {'word': '.I.', 'start': 260.54, 'end': 260.78}, {'word': ' is', 'start': 260.8, 'end': 260.88}, {'word': ' more', 'start': 260.88, 'end': 261.06}, {'word': ' important', 'start': 261.06, 'end': 261.44}, {'word': ' in', 'start': 261.44, 'end': 261.58}, {'word': ' the', 'start': 261.58, 'end': 261.66}, {'word': ' world', 'start': 261.66, 'end': 262.0}, {'word': ' than', 'start': 262.0, 'end': 262.6}, {'word': ' any', 'start': 262.6, 'end': 262.78}, {'word': ' time', 'start': 262.78, 'end': 262.98}, {'word': ' before,', 'start': 262.98, 'end': 263.28}, {'word': ' because', 'start': 263.42, 'end': 263.72}, {'word': ' anybody', 'start': 263.72, 'end': 264.66}, {'word': ' can', 'start': 264.66, 'end': 265.08}, {'word': ' generate', 'start': 265.08, 'end': 265.48}, {'word': ' a', 'start': 265.48, 'end': 265.64}, {'word': ' resume.', 'start': 265.64, 'end': 265.86}, {'word': ' Yeah.', 'start': 266.42, 'end': 266.52}, {'word': ' You', 'start': 266.66, 'end': 267.0}, {'word': ' know,', 'start': 267.0, 'end': 267.16}, {'word': ' anyone', 'start': 267.16, 'end': 267.48}, {'word': ' can', 'start': 267.48, 'end': 267.72}, {'word': ' generate', 'start': 267.72, 'end': 268.04}, {'word': ' a', 'start': 268.04, 'end': 268.18}, {'word': ' resume.', 'start': 268.18, 'end': 268.38}, {'word': ' Now,', 'start': 268.44, 'end': 268.56}, {'word': ' if', 'start': 268.58, 'end': 268.62}, {'word': ' you', 'start': 268.62, 'end': 268.74}, {'word': \" can't\", 'start': 268.74, 'end': 269.0}, {'word': ' write', 'start': 269.0, 'end': 269.2}, {'word': ' a', 'start': 269.2, 'end': 269.32}, {'word': ' resume', 'start': 269.32, 'end': 269.6}, {'word': ' using', 'start': 269.6, 'end': 270.18}, {'word': ' an', 'start': 270.18, 'end': 270.38}, {'word': ' A', 'start': 270.38, 'end': 270.5}, {'word': '.I.', 'start': 270.5, 'end': 270.66}, {'word': ' tool', 'start': 270.68, 'end': 270.82}, {'word': ' in', 'start': 270.82, 'end': 271.22}, {'word': ' five', 'start': 271.22, 'end': 271.44}, {'word': ' minutes,', 'start': 271.44, 'end': 271.76}, {'word': ' then,', 'start': 271.96, 'end': 272.7}, {'word': ' you', 'start': 272.76, 'end': 273.28}, {'word': ' know,', 'start': 273.28, 'end': 273.42}, {'word': ' shame', 'start': 273.44, 'end': 273.66}, {'word': ' on', 'start': 273.66, 'end': 273.78}, {'word': ' you.', 'start': 273.78, 'end': 273.94}, {'word': ' But', 'start': 274.64, 'end': 275.0}, {'word': ' how', 'start': 275.0, 'end': 275.36}, {'word': ' do', 'start': 275.36, 'end': 275.56}, {'word': ' you', 'start': 275.56, 'end': 275.62}, {'word': ' get', 'start': 275.62, 'end': 275.78}, {'word': ' that', 'start': 275.78, 'end': 275.94}, {'word': ' resume', 'start': 275.94, 'end': 276.3}, {'word': ' in', 'start': 276.3, 'end': 276.56}, {'word': ' front', 'start': 276.56, 'end': 276.72}, {'word': ' of', 'start': 276.72, 'end': 276.84}, {'word': ' a', 'start': 276.84, 'end': 276.9}, {'word': ' hiring', 'start': 276.9, 'end': 277.16}, {'word': ' manager?', 'start': 277.16, 'end': 277.48}, {'word': ' How', 'start': 277.62, 'end': 277.72}, {'word': ' do', 'start': 277.72, 'end': 277.86}, {'word': ' you', 'start': 277.86, 'end': 277.92}, {'word': ' get', 'start': 277.92, 'end': 278.06}, {'word': ' that', 'start': 278.06, 'end': 278.24}, {'word': ' resume,', 'start': 278.24, 'end': 278.68}, {'word': ' you', 'start': 278.76, 'end': 279.44}, {'word': ' know,', 'start': 279.44, 'end': 279.62}, {'word': ' in', 'start': 279.62, 'end': 279.8}, {'word': ' front', 'start': 279.8, 'end': 280.04}, {'word': ' of', 'start': 280.04, 'end': 280.14}, {'word': ' people?', 'start': 280.14, 'end': 280.34}, {'word': ' And', 'start': 280.38, 'end': 280.48}, {'word': ' how', 'start': 280.48, 'end': 280.54}, {'word': ' do', 'start': 280.54, 'end': 280.66}, {'word': ' you', 'start': 280.66, 'end': 280.72}, {'word': ' know', 'start': 280.72, 'end': 280.96}, {'word': ' that', 'start': 280.96, 'end': 281.16}, {'word': ' when', 'start': 281.16, 'end': 281.36}, {'word': ' a', 'start': 281.36, 'end': 281.54}, {'word': ' job', 'start': 281.54, 'end': 281.86}, {'word': ' is', 'start': 281.86, 'end': 282.02}, {'word': ' open,', 'start': 282.02, 'end': 282.34}, {'word': \" someone's\", 'start': 282.6, 'end': 283.52}, {'word': ' like,', 'start': 283.52, 'end': 283.64}, {'word': ' oh,', 'start': 283.74, 'end': 283.86}, {'word': ' my', 'start': 283.88, 'end': 283.96}, {'word': ' God,', 'start': 283.96, 'end': 284.14}, {'word': ' I', 'start': 284.18, 'end': 284.3}, {'word': ' just', 'start': 284.3, 'end': 284.52}, {'word': ' heard', 'start': 284.52, 'end': 284.72}, {'word': ' that', 'start': 284.72, 'end': 284.86}, {'word': ' Becky', 'start': 284.86, 'end': 285.14}, {'word': ' got', 'start': 285.14, 'end': 285.34}, {'word': ' laid', 'start': 285.34, 'end': 285.62}, {'word': ' off.', 'start': 285.62, 'end': 285.86}, {'word': \" She's\", 'start': 286.26, 'end': 286.62}, {'word': ' fantastic.', 'start': 286.62, 'end': 286.98}, {'word': \" Let's\", 'start': 287.32, 'end': 287.64}, {'word': ' hire', 'start': 287.64, 'end': 287.94}, {'word': ' her.', 'start': 287.94, 'end': 288.16}, {'word': ' Yeah.', 'start': 288.32, 'end': 288.66}, {'word': ' Earlier', 'start': 288.72, 'end': 289.08}, {'word': ' in', 'start': 289.08, 'end': 289.36}, {'word': ' my', 'start': 289.36, 'end': 289.52}, {'word': ' career,', 'start': 289.52, 'end': 289.82}, {'word': ' I', 'start': 289.96, 'end': 290.4}, {'word': ' got', 'start': 290.4, 'end': 290.5}, {'word': ' laid', 'start': 290.5, 'end': 290.7}, {'word': ' off', 'start': 290.7, 'end': 290.86}, {'word': ' three', 'start': 290.86, 'end': 291.06}, {'word': ' times', 'start': 291.06, 'end': 291.44}, {'word': ' because', 'start': 291.44, 'end': 291.84}, {'word': ' of', 'start': 291.84, 'end': 291.96}, {'word': ' companies,', 'start': 291.96, 'end': 292.28}, {'word': ' not', 'start': 292.46, 'end': 292.84}, {'word': ' anything', 'start': 292.84, 'end': 293.08}, {'word': ' I', 'start': 293.08, 'end': 293.3}, {'word': ' did.', 'start': 293.3, 'end': 293.46}, {'word': ' Companies', 'start': 293.96, 'end': 294.32}, {'word': ' that', 'start': 294.32, 'end': 294.54}, {'word': ' either', 'start': 294.54, 'end': 294.72}, {'word': ' entirely', 'start': 294.72, 'end': 295.1}, {'word': ' went', 'start': 295.1, 'end': 295.48}, {'word': ' out', 'start': 295.48, 'end': 295.6}, {'word': ' of', 'start': 295.6, 'end': 295.72}, {'word': ' business.', 'start': 295.72, 'end': 295.98}, {'word': ' Yeah.', 'start': 296.02, 'end': 296.32}, {'word': ' Or', 'start': 296.42, 'end': 296.54}, {'word': ' left', 'start': 296.54, 'end': 296.74}, {'word': ' Austin.', 'start': 296.74, 'end': 297.06}, {'word': ' Yeah.', 'start': 297.2, 'end': 297.3}, {'word': ' And', 'start': 297.42, 'end': 297.74}, {'word': ' every', 'start': 297.74, 'end': 298.22}, {'word': ' time', 'start': 298.22, 'end': 298.48}, {'word': ' I', 'start': 298.48, 'end': 298.72}, {'word': ' had', 'start': 298.72, 'end': 298.88}, {'word': ' a', 'start': 298.88, 'end': 298.96}, {'word': ' job', 'start': 298.96, 'end': 299.24}, {'word': ' within', 'start': 299.24, 'end': 299.4}, {'word': ' three', 'start': 299.4, 'end': 299.66}, {'word': ' weeks', 'start': 299.66, 'end': 300.02}, {'word': ' and', 'start': 300.02, 'end': 300.3}, {'word': ' I', 'start': 300.3, 'end': 300.44}, {'word': ' barely', 'start': 300.44, 'end': 300.86}, {'word': ' made', 'start': 300.86, 'end': 301.12}, {'word': ' an', 'start': 301.12, 'end': 301.26}, {'word': ' outbound', 'start': 301.26, 'end': 301.56}, {'word': ' call', 'start': 301.56, 'end': 301.9}, {'word': ' and', 'start': 301.9, 'end': 302.44}, {'word': ' people', 'start': 302.44, 'end': 302.84}, {'word': ' said,', 'start': 302.84, 'end': 303.08}, {'word': ' well,', 'start': 303.16, 'end': 303.22}, {'word': ' how?', 'start': 303.28, 'end': 303.58}, {'word': ' How', 'start': 303.76, 'end': 304.06}, {'word': ' do', 'start': 304.06, 'end': 304.16}, {'word': ' you', 'start': 304.16, 'end': 304.24}, {'word': ' get', 'start': 304.24, 'end': 304.42}, {'word': ' that?', 'start': 304.42, 'end': 304.56}, {'word': ' And', 'start': 304.6, 'end': 304.74}, {'word': ' it', 'start': 304.74, 'end': 304.82}, {'word': ' was', 'start': 304.82, 'end': 304.94}, {'word': ' a', 'start': 304.94, 'end': 305.02}, {'word': ' better', 'start': 305.02, 'end': 305.22}, {'word': ' job.', 'start': 305.22, 'end': 305.54}, {'word': ' They', 'start': 305.58, 'end': 305.68}, {'word': ' go,', 'start': 305.68, 'end': 305.8}, {'word': ' you', 'start': 305.82, 'end': 305.94}, {'word': ' get', 'start': 305.94, 'end': 306.1}, {'word': ' laid', 'start': 306.1, 'end': 306.28}, {'word': ' off', 'start': 306.28, 'end': 306.48}, {'word': ' up.', 'start': 306.48, 'end': 306.74}, {'word': ' How', 'start': 306.88, 'end': 307.02}, {'word': ' does', 'start': 307.02, 'end': 307.18}, {'word': ' that', 'start': 307.18, 'end': 307.32}, {'word': ' happen?', 'start': 307.32, 'end': 307.66}, {'word': ' And', 'start': 308.04, 'end': 308.44}, {'word': ' it', 'start': 308.44, 'end': 308.58}, {'word': ' was', 'start': 308.58, 'end': 308.7}, {'word': ' because', 'start': 308.7, 'end': 308.96}, {'word': ' I', 'start': 308.96, 'end': 309.36}, {'word': ' bought', 'start': 309.36, 'end': 309.84}, {'word': ' into', 'start': 309.84, 'end': 310.28}, {'word': ' this', 'start': 310.28, 'end': 310.6}, {'word': ' idea', 'start': 310.6, 'end': 310.88}, {'word': ' and', 'start': 310.88, 'end': 311.12}, {'word': ' I', 'start': 311.12, 'end': 311.22}, {'word': ' learned', 'start': 311.22, 'end': 311.44}, {'word': ' it', 'start': 311.44, 'end': 311.54}, {'word': ' from', 'start': 311.54, 'end': 311.66}, {'word': ' a', 'start': 311.66, 'end': 311.74}, {'word': ' gentleman', 'start': 311.74, 'end': 311.96}, {'word': ' named', 'start': 311.96, 'end': 312.2}, {'word': ' Harvey', 'start': 312.2, 'end': 312.4}, {'word': ' McKay,', 'start': 312.4, 'end': 312.88}, {'word': ' who', 'start': 312.88, 'end': 313.02}, {'word': ' wrote', 'start': 313.02, 'end': 313.22}, {'word': ' a', 'start': 313.22, 'end': 313.32}, {'word': ' famous', 'start': 313.32, 'end': 313.54}, {'word': ' book', 'start': 313.54, 'end': 313.74}, {'word': ' in', 'start': 313.74, 'end': 313.88}, {'word': ' the', 'start': 313.88, 'end': 313.96}, {'word': ' 90s', 'start': 313.96, 'end': 314.38}, {'word': ' called', 'start': 314.38, 'end': 314.56}, {'word': ' How', 'start': 314.56, 'end': 314.74}, {'word': ' to', 'start': 314.74, 'end': 314.86}, {'word': ' Swim', 'start': 314.86, 'end': 315.26}, {'word': ' with', 'start': 315.26, 'end': 315.38}, {'word': ' the', 'start': 315.38, 'end': 315.52}, {'word': ' Sharks', 'start': 315.52, 'end': 315.9}, {'word': ' Without', 'start': 315.9, 'end': 316.58}, {'word': ' Getting', 'start': 316.58, 'end': 316.84}, {'word': ' Eaten', 'start': 316.84, 'end': 317.16}, {'word': ' Alive.', 'start': 317.16, 'end': 317.48}, {'word': ' Yeah.', 'start': 317.52, 'end': 317.92}, {'word': ' And', 'start': 318.08, 'end': 318.48}, {'word': ' I', 'start': 318.48, 'end': 318.94}, {'word': ' met', 'start': 318.94, 'end': 319.28}, {'word': ' Harvey.', 'start': 319.28, 'end': 319.58}, {'word': ' He', 'start': 319.72, 'end': 319.86}, {'word': ' actually', 'start': 319.86, 'end': 320.36}, {'word': ' is', 'start': 320.36, 'end': 320.62}, {'word': ' the', 'start': 320.62, 'end': 320.72}, {'word': ' person', 'start': 320.72, 'end': 320.98}, {'word': ' who', 'start': 320.98, 'end': 321.1}, {'word': ' got', 'start': 321.1, 'end': 321.28}, {'word': ' me', 'start': 321.28, 'end': 321.54}, {'word': ' inspired', 'start': 321.54, 'end': 321.96}, {'word': ' to', 'start': 321.96, 'end': 322.2}, {'word': ' become', 'start': 322.2, 'end': 322.44}, {'word': ' a', 'start': 322.44, 'end': 322.62}, {'word': ' professional', 'start': 322.62, 'end': 322.92}, {'word': ' speaker,', 'start': 322.92, 'end': 323.36}, {'word': ' something', 'start': 323.52, 'end': 323.74}, {'word': ' that', 'start': 323.74, 'end': 324.1}, {'word': ' I', 'start': 324.1, 'end': 324.28}, {'word': ' do', 'start': 324.28, 'end': 324.38}, {'word': ' as', 'start': 324.38, 'end': 324.52}, {'word': ' well.', 'start': 324.52, 'end': 324.76}, {'word': ' And', 'start': 325.12, 'end': 325.52}, {'word': \" I've\", 'start': 325.52, 'end': 326.3}, {'word': ' met', 'start': 326.3, 'end': 326.4}, {'word': ' him.', 'start': 326.4, 'end': 326.4}, {'word': \" I've\", 'start': 326.42, 'end': 326.42}, {'word': ' known', 'start': 326.42, 'end': 326.42}, {'word': ' Harvey', 'start': 326.42, 'end': 326.68}, {'word': ' for', 'start': 326.68, 'end': 326.84}, {'word': ' a', 'start': 326.84, 'end': 326.94}, {'word': ' long', 'start': 326.94, 'end': 327.08}, {'word': ' time.', 'start': 327.08, 'end': 327.28}, {'word': ' But', 'start': 327.36, 'end': 327.46}, {'word': ' I', 'start': 327.46, 'end': 327.94}, {'word': ' read', 'start': 327.94, 'end': 328.74}, {'word': ' his', 'start': 328.74, 'end': 328.9}, {'word': ' books', 'start': 328.9, 'end': 329.24}, {'word': ' and', 'start': 329.24, 'end': 329.92}, {'word': ' I', 'start': 329.92, 'end': 330.1}, {'word': ' believed', 'start': 330.1, 'end': 330.54}, {'word': ' him.', 'start': 330.54, 'end': 330.9}, {'word': ' And', 'start': 331.04, 'end': 331.44}, {'word': ' he', 'start': 331.44, 'end': 331.56}, {'word': ' basically', 'start': 331.56, 'end': 331.92}, {'word': ' said', 'start': 331.92, 'end': 332.26}, {'word': ' that', 'start': 332.26, 'end': 332.46}, {'word': ' the', 'start': 332.46, 'end': 332.54}, {'word': ' friendships', 'start': 332.54, 'end': 332.86}, {'word': ' you', 'start': 332.86, 'end': 333.12}, {'word': ' build', 'start': 333.12, 'end': 333.36}, {'word': ' and', 'start': 333.36, 'end': 333.5}, {'word': ' the', 'start': 333.5, 'end': 333.54}, {'word': ' relationships', 'start': 333.54, 'end': 333.98}, {'word': ' you', 'start': 333.98, 'end': 334.3}, {'word': ' build', 'start': 334.3, 'end': 334.56}, {'word': ' and', 'start': 334.56, 'end': 334.76}, {'word': ' the', 'start': 334.76, 'end': 334.82}, {'word': ' reputation', 'start': 334.82, 'end': 335.1}, {'word': ' you', 'start': 335.1, 'end': 335.5}, {'word': ' build', 'start': 335.5, 'end': 335.8}, {'word': ' are', 'start': 335.8, 'end': 336.36}, {'word': ' going', 'start': 336.36, 'end': 336.52}, {'word': ' to', 'start': 336.52, 'end': 336.64}, {'word': ' be', 'start': 336.64, 'end': 336.74}, {'word': ' your', 'start': 336.74, 'end': 336.88}, {'word': ' secret', 'start': 336.88, 'end': 337.12}, {'word': ' weapon.', 'start': 337.12, 'end': 337.5}, {'word': ' And', 'start': 337.82, 'end': 338.22}, {'word': ' it', 'start': 338.22, 'end': 338.54}, {'word': \" doesn't\", 'start': 338.54, 'end': 338.96}, {'word': ' happen', 'start': 338.96, 'end': 339.26}, {'word': ' in', 'start': 339.26, 'end': 339.44}, {'word': ' the', 'start': 339.44, 'end': 339.5}, {'word': ' first', 'start': 339.5, 'end': 339.72}, {'word': ' five', 'start': 339.72, 'end': 340.02}, {'word': ' or', 'start': 340.02, 'end': 340.16}, {'word': ' 10', 'start': 340.16, 'end': 340.28}, {'word': ' years.', 'start': 340.28, 'end': 340.58}, {'word': ' No.', 'start': 340.68, 'end': 340.88}, {'word': ' If', 'start': 341.18, 'end': 341.58}, {'word': ' you', 'start': 341.58, 'end': 341.72}, {'word': ' fast', 'start': 341.72, 'end': 342.02}, {'word': ' forward', 'start': 342.02, 'end': 342.42}, {'word': ' from', 'start': 342.42, 'end': 342.76}, {'word': ' when', 'start': 342.76, 'end': 342.86}, {'word': ' I', 'start': 342.86, 'end': 343.0}, {'word': ' was', 'start': 343.0, 'end': 343.12}, {'word': ' in', 'start': 343.12, 'end': 343.24}, {'word': ' my', 'start': 343.24, 'end': 343.4}, {'word': ' 20s', 'start': 343.4, 'end': 343.78}, {'word': ' and', 'start': 343.78, 'end': 343.8}, {'word': ' 30s', 'start': 343.8, 'end': 344.2}, {'word': ' when', 'start': 344.2, 'end': 344.26}, {'word': ' I', 'start': 344.26, 'end': 344.36}, {'word': ' learned', 'start': 344.36, 'end': 344.6}, {'word': ' that', 'start': 344.6, 'end': 344.8}, {'word': ' to', 'start': 344.8, 'end': 345.06}, {'word': ' now,', 'start': 345.06, 'end': 345.3}, {'word': ' people', 'start': 345.56, 'end': 346.14}, {'word': ' always', 'start': 346.14, 'end': 346.34}, {'word': ' ask', 'start': 346.34, 'end': 346.6}, {'word': ' me,', 'start': 346.6, 'end': 346.72}, {'word': ' how', 'start': 346.76, 'end': 346.8}, {'word': ' did', 'start': 346.8, 'end': 346.9}, {'word': ' you', 'start': 346.9, 'end': 347.0}, {'word': ' get', 'start': 347.0, 'end': 347.08}, {'word': ' a', 'start': 347.08, 'end': 347.12}, {'word': ' job', 'start': 347.12, 'end': 347.36}, {'word': ' at', 'start': 347.36, 'end': 347.46}, {'word': ' ATC?', 'start': 347.46, 'end': 347.86}, {'word': \" I'm\", 'start': 347.94, 'end': 348.08}, {'word': ' like,', 'start': 348.08, 'end': 348.16}, {'word': ' well,', 'start': 348.2, 'end': 348.28}, {'word': ' they', 'start': 348.32, 'end': 348.38}, {'word': ' were', 'start': 348.38, 'end': 348.5}, {'word': ' looking', 'start': 348.5, 'end': 348.74}, {'word': ' for', 'start': 348.74, 'end': 348.98}, {'word': ' somebody', 'start': 348.98, 'end': 349.26}, {'word': ' to', 'start': 349.26, 'end': 349.46}, {'word': ' reinvent', 'start': 349.46, 'end': 349.76}, {'word': ' it.', 'start': 349.76, 'end': 350.08}, {'word': ' Yeah.', 'start': 350.16, 'end': 350.4}, {'word': ' And', 'start': 350.46, 'end': 350.58}, {'word': ' half', 'start': 350.58, 'end': 350.82}, {'word': ' the', 'start': 350.82, 'end': 351.0}, {'word': ' board', 'start': 351.0, 'end': 351.2}, {'word': ' knew', 'start': 351.2, 'end': 351.4}, {'word': ' who', 'start': 351.4, 'end': 351.5}, {'word': ' I', 'start': 351.5, 'end': 351.64}, {'word': ' was.', 'start': 351.64, 'end': 351.96}, {'word': ' Yeah.', 'start': 352.04, 'end': 352.38}, {'word': ' And', 'start': 352.46, 'end': 352.66}, {'word': ' they', 'start': 352.66, 'end': 353.16}, {'word': ' asked', 'start': 353.16, 'end': 353.44}, {'word': ' me.', 'start': 353.44, 'end': 353.66}, {'word': ' Yeah.', 'start': 353.76, 'end': 354.06}, {'word': ' Exactly.', 'start': 354.66, 'end': 355.06}, {'word': ' What', 'start': 355.18, 'end': 355.56}, {'word': ' you', 'start': 355.56, 'end': 355.7}, {'word': ' just', 'start': 355.7, 'end': 355.92}, {'word': ' laid.', 'start': 355.92, 'end': 356.18}, {'word': ' We', 'start': 356.42, 'end': 357.14}, {'word': ' are', 'start': 357.14, 'end': 358.4}, {'word': ' doing', 'start': 358.4, 'end': 358.56}, {'word': ' a', 'start': 358.56, 'end': 358.7}, {'word': ' fit', 'start': 358.7, 'end': 358.84}, {'word': ' gap.', 'start': 358.84, 'end': 359.08}, {'word': ' If', 'start': 360.04, 'end': 360.56}, {'word': ' a', 'start': 360.56, 'end': 360.7}, {'word': ' role', 'start': 360.7, 'end': 360.94}, {'word': ' demands.', 'start': 360.94, 'end': 361.36}, {'word': ' You', 'start': 363.00000000000006, 'end': 363.52000000000004}, {'word': ' get', 'start': 363.52000000000004, 'end': 364.04}, {'word': ' shocked', 'start': 364.04, 'end': 364.94}, {'word': ' how', 'start': 364.94, 'end': 365.3}, {'word': ' many', 'start': 365.3, 'end': 365.46}, {'word': ' people', 'start': 365.46, 'end': 365.76}, {'word': ' apply', 'start': 365.76, 'end': 366.02}, {'word': ' not', 'start': 366.02, 'end': 366.52}, {'word': ' knowing', 'start': 366.52, 'end': 366.9}, {'word': ' what', 'start': 366.9, 'end': 367.16}, {'word': ' job,', 'start': 367.16, 'end': 367.5}, {'word': ' what', 'start': 367.58, 'end': 367.72}, {'word': ' company', 'start': 367.72, 'end': 368.14}, {'word': ' that.', 'start': 368.14, 'end': 368.38}, {'word': ' Are', 'start': 369.78000000000003, 'end': 370.3}, {'word': ' you', 'start': 370.3, 'end': 370.42}, {'word': ' applying', 'start': 370.42, 'end': 370.68}, {'word': ' to', 'start': 370.68, 'end': 371.02}, {'word': ' a', 'start': 371.02, 'end': 371.14}, {'word': ' job', 'start': 371.14, 'end': 371.64}, {'word': ' in', 'start': 371.64, 'end': 371.98}, {'word': ' a', 'start': 371.98, 'end': 372.16}, {'word': ' mass?', 'start': 372.16, 'end': 372.66}, {'word': ' Like', 'start': 373.20000000000005, 'end': 373.72}, {'word': ' not.', 'start': 373.72, 'end': 373.98}, {'word': ' I', 'start': 374.24, 'end': 374.76}, {'word': ' understand', 'start': 374.76, 'end': 375.14}, {'word': ' the', 'start': 375.14, 'end': 375.42}, {'word': ' desperation', 'start': 375.42, 'end': 375.76}, {'word': ' to', 'start': 375.76, 'end': 376.0}, {'word': ' need', 'start': 376.0, 'end': 376.24}, {'word': ' a', 'start': 376.24, 'end': 376.34}, {'word': ' job.', 'start': 376.34, 'end': 376.6}, {'word': ' But', 'start': 377.48, 'end': 378.0}, {'word': ' spend', 'start': 378.0, 'end': 378.44}, {'word': ' time.', 'start': 378.44, 'end': 378.88}, {'word': ' What', 'start': 379.14, 'end': 379.5}, {'word': ' this', 'start': 379.5, 'end': 379.72}, {'word': ' company', 'start': 379.72, 'end': 380.08}, {'word': ' does.', 'start': 380.08, 'end': 380.44}, {'word': ' Go', 'start': 380.7, 'end': 380.86}, {'word': ' to', 'start': 380.86, 'end': 380.94}, {'word': ' their', 'start': 380.94, 'end': 380.96}, {'word': ' website.', 'start': 380.96, 'end': 381.3}, {'word': ' I', 'start': 381.5, 'end': 381.56}, {'word': ' mean,', 'start': 381.56, 'end': 381.64}, {'word': ' in', 'start': 381.66, 'end': 381.9}, {'word': ' five', 'start': 381.9, 'end': 382.28}, {'word': ' minutes,', 'start': 382.28, 'end': 382.7}, {'word': ' you', 'start': 382.72, 'end': 382.92}, {'word': ' can', 'start': 382.92, 'end': 383.04}, {'word': ' get', 'start': 383.04, 'end': 383.22}, {'word': ' the', 'start': 383.22, 'end': 383.34}, {'word': ' gist', 'start': 383.34, 'end': 383.54}, {'word': ' of', 'start': 383.54, 'end': 383.66}, {'word': ' what', 'start': 383.66, 'end': 383.78}, {'word': ' a', 'start': 383.78, 'end': 383.9}, {'word': ' company', 'start': 383.9, 'end': 384.14}, {'word': ' does.', 'start': 384.14, 'end': 384.42}, {'word': ' Yeah.', 'start': 384.62, 'end': 384.82}, {'word': ' You', 'start': 384.82, 'end': 385.04}, {'word': ' got', 'start': 385.04, 'end': 385.2}, {'word': ' to', 'start': 385.2, 'end': 385.38}, {'word': ' know', 'start': 385.38, 'end': 385.64}, {'word': ' the', 'start': 385.64, 'end': 385.82}, {'word': ' company.', 'start': 385.82, 'end': 386.16}, {'word': ' You', 'start': 386.16, 'end': 386.3}, {'word': ' got', 'start': 386.3, 'end': 386.48}, {'word': ' to', 'start': 386.48, 'end': 386.6}, {'word': ' know', 'start': 386.6, 'end': 386.78}, {'word': ' what', 'start': 386.78, 'end': 387.04}, {'word': ' you', 'start': 387.04, 'end': 387.2}, {'word': ' are', 'start': 387.2, 'end': 387.34}, {'word': ' pitching', 'start': 387.34, 'end': 387.76}, {'word': ' to', 'start': 387.76, 'end': 388.06}, {'word': ' the', 'start': 388.06, 'end': 388.16}, {'word': ' company.', 'start': 388.16, 'end': 388.46}, {'word': ' Yeah.', 'start': 388.54, 'end': 388.9}, {'word': ' What', 'start': 388.9, 'end': 389.4}, {'word': ' exactly', 'start': 389.4, 'end': 390.5}, {'word': ' you', 'start': 390.5, 'end': 391.1}, {'word': ' fit', 'start': 391.1, 'end': 391.36}, {'word': ' in,', 'start': 391.36, 'end': 391.6}, {'word': ' why', 'start': 391.64, 'end': 391.9}, {'word': ' you', 'start': 391.9, 'end': 392.16}, {'word': ' are', 'start': 392.16, 'end': 392.34}, {'word': ' the', 'start': 392.34, 'end': 392.48}, {'word': ' best', 'start': 392.48, 'end': 392.74}, {'word': ' guy.', 'start': 392.74, 'end': 393.0}, {'word': ' You', 'start': 393.18, 'end': 393.4}, {'word': ' need', 'start': 393.4, 'end': 393.58}, {'word': ' to', 'start': 393.58, 'end': 393.72}, {'word': ' articulate', 'start': 393.72, 'end': 394.06}, {'word': ' that', 'start': 394.06, 'end': 394.32}, {'word': ' really.', 'start': 394.32, 'end': 394.6}, {'word': ' I', 'start': 394.84, 'end': 395.1}, {'word': \" don't\", 'start': 395.1, 'end': 395.32}, {'word': ' care.', 'start': 395.32, 'end': 395.62}, {'word': ' Oh,', 'start': 396.08, 'end': 396.6}, {'word': ' nobody', 'start': 396.6, 'end': 397.1}, {'word': ' reads', 'start': 397.1, 'end': 397.4}, {'word': ' all', 'start': 397.4, 'end': 397.66}, {'word': ' the', 'start': 397.66, 'end': 397.84}, {'word': ' pages.', 'start': 397.84, 'end': 398.06}, {'word': ' We', 'start': 398.2, 'end': 398.34}, {'word': ' get', 'start': 398.34, 'end': 398.54}, {'word': ' some', 'start': 398.54, 'end': 398.76}, {'word': ' resumes,', 'start': 398.76, 'end': 399.06}, {'word': ' 20', 'start': 399.24, 'end': 399.42}, {'word': ' pages.', 'start': 399.42, 'end': 399.78}, {'word': ' I', 'start': 400.86, 'end': 401.38}, {'word': \" don't\", 'start': 401.38, 'end': 401.74}, {'word': ' blame', 'start': 401.74, 'end': 402.04}, {'word': ' them.', 'start': 402.04, 'end': 402.34}, {'word': \" They're\", 'start': 402.36, 'end': 402.62}, {'word': ' very', 'start': 402.62, 'end': 402.96}, {'word': ' proud', 'start': 402.96, 'end': 403.58}, {'word': ' of', 'start': 403.58, 'end': 403.86}, {'word': ' what', 'start': 403.86, 'end': 404.02}, {'word': \" they've\", 'start': 404.02, 'end': 404.3}, {'word': ' done.', 'start': 404.3, 'end': 404.52}, {'word': ' We', 'start': 404.74, 'end': 405.16}, {'word': ' are', 'start': 405.16, 'end': 405.3}, {'word': ' all', 'start': 405.3, 'end': 405.5}, {'word': ' proud', 'start': 405.5, 'end': 405.76}, {'word': ' of.', 'start': 405.76, 'end': 406.02}, {'word': \" We're\", 'start': 406.04, 'end': 406.26}, {'word': ' not', 'start': 406.26, 'end': 406.26}, {'word': ' going', 'start': 406.26, 'end': 406.48}, {'word': ' to', 'start': 406.48, 'end': 406.54}, {'word': ' read', 'start': 406.54, 'end': 406.7}, {'word': ' it.', 'start': 406.7, 'end': 406.82}, {'word': ' Yeah.', 'start': 406.92, 'end': 407.22}, {'word': \" Nobody's\", 'start': 407.26, 'end': 407.76}, {'word': ' going', 'start': 407.76, 'end': 407.88}, {'word': ' to', 'start': 407.88, 'end': 408.02}, {'word': ' read', 'start': 408.02, 'end': 408.3}, {'word': ' your', 'start': 408.3, 'end': 408.66}, {'word': ' 20', 'start': 408.66, 'end': 408.96}, {'word': ' pages.', 'start': 408.96, 'end': 409.32}, {'word': ' What', 'start': 409.7, 'end': 410.22}, {'word': ' we', 'start': 410.22, 'end': 410.5}, {'word': ' need', 'start': 410.5, 'end': 410.7}, {'word': ' is', 'start': 410.7, 'end': 410.98}, {'word': ' that', 'start': 410.98, 'end': 411.18}, {'word': ' cover', 'start': 411.18, 'end': 411.9}, {'word': ' letter', 'start': 411.9, 'end': 412.3}, {'word': ' tells', 'start': 412.3, 'end': 412.86}, {'word': ' me', 'start': 412.86, 'end': 413.18}, {'word': ' what', 'start': 413.18, 'end': 413.5}, {'word': ' you', 'start': 413.5, 'end': 413.72}, {'word': ' can', 'start': 413.72, 'end': 413.82}, {'word': ' bring', 'start': 413.82, 'end': 414.14}, {'word': ' it', 'start': 414.14, 'end': 414.3}, {'word': ' to', 'start': 414.3, 'end': 414.46}, {'word': ' our', 'start': 414.46, 'end': 414.7}, {'word': ' company.', 'start': 414.7, 'end': 415.24}, {'word': ' Our', 'start': 415.24, 'end': 415.78}, {'word': ' region.', 'start': 415.78, 'end': 416.36}, {'word': ' You', 'start': 416.5, 'end': 416.96}, {'word': ' show', 'start': 416.96, 'end': 417.26}, {'word': ' me', 'start': 417.26, 'end': 417.52}, {'word': ' what', 'start': 417.52, 'end': 417.94}, {'word': ' we', 'start': 417.94, 'end': 418.4}, {'word': ' are', 'start': 418.4, 'end': 418.54}, {'word': ' doing.', 'start': 418.54, 'end': 418.82}, {'word': ' You', 'start': 419.34, 'end': 419.82}, {'word': ' can', 'start': 419.82, 'end': 420.0}, {'word': ' do', 'start': 420.0, 'end': 420.24}, {'word': ' different.', 'start': 420.24, 'end': 420.5}, {'word': ' And', 'start': 420.8, 'end': 420.98}, {'word': ' I', 'start': 420.98, 'end': 421.32}, {'word': ' flip', 'start': 421.32, 'end': 421.56}, {'word': ' that', 'start': 421.56, 'end': 421.96}, {'word': ' around', 'start': 421.96, 'end': 422.24}, {'word': ' to', 'start': 422.24, 'end': 422.4}, {'word': ' the', 'start': 422.4, 'end': 422.48}, {'word': ' companies', 'start': 422.48, 'end': 422.82}, {'word': ' too.', 'start': 422.82, 'end': 423.12}, {'word': ' You', 'start': 423.28, 'end': 423.28}, {'word': ' know,', 'start': 423.28, 'end': 423.4}, {'word': ' it', 'start': 423.4, 'end': 423.46}, {'word': ' used', 'start': 423.46, 'end': 423.66}, {'word': ' to', 'start': 423.66, 'end': 423.84}, {'word': ' be', 'start': 423.84, 'end': 424.02}, {'word': ' companies', 'start': 424.02, 'end': 424.78}, {'word': ' wanted', 'start': 424.78, 'end': 425.64}, {'word': ' to', 'start': 425.64, 'end': 425.9}, {'word': ' be', 'start': 425.9, 'end': 426.12}, {'word': ' really', 'start': 426.12, 'end': 426.48}, {'word': ' active', 'start': 426.48, 'end': 426.78}, {'word': ' in', 'start': 426.78, 'end': 426.98}, {'word': ' the', 'start': 426.98, 'end': 427.04}, {'word': ' community.', 'start': 427.04, 'end': 427.3}, {'word': ' They', 'start': 427.44, 'end': 427.56}, {'word': ' wanted', 'start': 427.56, 'end': 427.78}, {'word': ' to', 'start': 427.78, 'end': 427.94}, {'word': ' support', 'start': 427.94, 'end': 428.24}, {'word': ' the', 'start': 428.24, 'end': 428.48}, {'word': ' Austin', 'start': 428.48, 'end': 428.74}, {'word': ' Technology', 'start': 428.74, 'end': 429.0}, {'word': ' Council.', 'start': 429.0, 'end': 429.56}, {'word': ' They', 'start': 429.68, 'end': 429.76}, {'word': ' wanted', 'start': 429.76, 'end': 429.96}, {'word': ' to', 'start': 429.96, 'end': 430.1}, {'word': ' support', 'start': 430.1, 'end': 430.36}, {'word': ' the', 'start': 430.36, 'end': 430.56}, {'word': ' Chamber', 'start': 430.56, 'end': 430.72}, {'word': ' of', 'start': 430.72, 'end': 430.94}, {'word': ' Commerce', 'start': 430.94, 'end': 431.26}, {'word': ' or', 'start': 431.26, 'end': 431.44}, {'word': ' whatever', 'start': 431.44, 'end': 431.64}, {'word': ' group.', 'start': 431.64, 'end': 432.0}, {'word': ' And', 'start': 432.14, 'end': 432.54}, {'word': ' they', 'start': 432.54, 'end': 432.64}, {'word': ' wanted', 'start': 432.64, 'end': 432.88}, {'word': ' their', 'start': 432.88, 'end': 433.04}, {'word': ' logos', 'start': 433.04, 'end': 433.32}, {'word': ' to', 'start': 433.32, 'end': 433.58}, {'word': ' be', 'start': 433.58, 'end': 433.7}, {'word': ' out', 'start': 433.7, 'end': 433.86}, {'word': ' there', 'start': 433.86, 'end': 434.06}, {'word': ' that', 'start': 434.06, 'end': 434.26}, {'word': ' they', 'start': 434.26, 'end': 434.42}, {'word': ' were', 'start': 434.42, 'end': 434.58}, {'word': ' part', 'start': 434.58, 'end': 434.94}, {'word': ' of', 'start': 434.94, 'end': 435.08}, {'word': ' the', 'start': 435.08, 'end': 435.14}, {'word': ' community', 'start': 435.14, 'end': 435.4}, {'word': ' because', 'start': 435.4, 'end': 436.16}, {'word': ' they', 'start': 436.16, 'end': 437.04}, {'word': ' knew', 'start': 437.04, 'end': 437.32}, {'word': ' that', 'start': 437.32, 'end': 437.72}, {'word': ' their', 'start': 437.72, 'end': 438.0}, {'word': ' employees', 'start': 438.0, 'end': 438.38}, {'word': ' appreciated', 'start': 438.38, 'end': 438.96}, {'word': ' seeing', 'start': 438.96, 'end': 439.46}, {'word': ' the', 'start': 439.46, 'end': 439.98}, {'word': ' support', 'start': 439.98, 'end': 440.28}, {'word': ' of', 'start': 440.28, 'end': 440.44}, {'word': ' the', 'start': 440.44, 'end': 440.46}, {'word': ' community.', 'start': 440.46, 'end': 440.76}, {'word': ' But', 'start': 440.86, 'end': 441.0}, {'word': ' they', 'start': 441.0, 'end': 441.1}, {'word': ' also', 'start': 441.1, 'end': 441.36}, {'word': ' knew', 'start': 441.36, 'end': 441.62}, {'word': ' that', 'start': 441.62, 'end': 441.74}, {'word': ' when', 'start': 441.74, 'end': 441.86}, {'word': ' they', 'start': 441.86, 'end': 441.96}, {'word': ' were', 'start': 441.96, 'end': 442.1}, {'word': ' looking', 'start': 442.1, 'end': 442.34}, {'word': ' for', 'start': 442.34, 'end': 442.58}, {'word': ' employees,', 'start': 442.58, 'end': 442.98}, {'word': ' if', 'start': 443.24, 'end': 443.8}, {'word': \" I've\", 'start': 443.8, 'end': 444.1}, {'word': ' never', 'start': 444.1, 'end': 444.28}, {'word': ' heard', 'start': 444.28, 'end': 444.6}, {'word': ' of', 'start': 444.6, 'end': 444.76}, {'word': ' the', 'start': 444.76, 'end': 444.82}, {'word': ' company,', 'start': 444.82, 'end': 445.16}, {'word': ' I', 'start': 445.68, 'end': 446.0}, {'word': ' could', 'start': 446.0, 'end': 446.18}, {'word': ' totally', 'start': 446.18, 'end': 446.48}, {'word': ' miss', 'start': 446.48, 'end': 446.8}, {'word': ' the', 'start': 446.8, 'end': 447.12}, {'word': ' opportunity', 'start': 447.12, 'end': 447.58}, {'word': ' and', 'start': 447.58, 'end': 447.86}, {'word': ' not', 'start': 447.86, 'end': 448.08}, {'word': ' even', 'start': 448.08, 'end': 448.28}, {'word': ' apply.', 'start': 448.28, 'end': 448.52}, {'word': ' But', 'start': 449.06, 'end': 449.38}, {'word': ' if', 'start': 449.38, 'end': 449.56}, {'word': ' the', 'start': 449.56, 'end': 449.68}, {'word': ' company', 'start': 449.68, 'end': 450.08}, {'word': ' builds', 'start': 450.08, 'end': 450.38}, {'word': ' a', 'start': 450.38, 'end': 450.5}, {'word': ' reputation', 'start': 450.5, 'end': 450.86}, {'word': ' and', 'start': 450.86, 'end': 451.26}, {'word': ' the', 'start': 451.26, 'end': 451.38}, {'word': ' leadership', 'start': 451.38, 'end': 451.7}, {'word': ' of', 'start': 451.7, 'end': 451.96}, {'word': ' the', 'start': 451.96, 'end': 452.04}, {'word': ' company', 'start': 452.04, 'end': 452.38}, {'word': ' and', 'start': 452.38, 'end': 452.74}, {'word': ' you', 'start': 452.74, 'end': 452.94}, {'word': ' do', 'start': 452.94, 'end': 453.04}, {'word': ' a', 'start': 453.04, 'end': 453.18}, {'word': ' great', 'start': 453.18, 'end': 453.36}, {'word': ' job', 'start': 453.36, 'end': 453.56}, {'word': ' of', 'start': 453.56, 'end': 453.72}, {'word': ' this,', 'start': 453.72, 'end': 453.86}, {'word': ' you', 'start': 453.96, 'end': 454.06}, {'word': ' know,', 'start': 454.06, 'end': 454.14}, {'word': ' you', 'start': 454.16, 'end': 454.32}, {'word': ' show', 'start': 454.32, 'end': 454.78}, {'word': ' up', 'start': 454.78, 'end': 455.12}, {'word': ' at', 'start': 455.12, 'end': 455.26}, {'word': ' different', 'start': 455.26, 'end': 455.58}, {'word': ' events', 'start': 455.58, 'end': 455.96}, {'word': ' around', 'start': 455.96, 'end': 456.22}, {'word': ' town.', 'start': 456.22, 'end': 456.52}, {'word': ' I', 'start': 456.72, 'end': 457.04}, {'word': ' would', 'start': 457.04, 'end': 457.2}, {'word': ' love', 'start': 457.2, 'end': 457.4}, {'word': ' to', 'start': 457.4, 'end': 457.56}, {'word': ' see', 'start': 457.56, 'end': 457.78}, {'word': ' because', 'start': 457.78, 'end': 457.94}, {'word': \" that's\", 'start': 457.94, 'end': 458.32}, {'word': ' where', 'start': 458.32, 'end': 458.34}, {'word': ' I', 'start': 458.34, 'end': 458.5}, {'word': ' pick', 'start': 458.5, 'end': 458.66}, {'word': ' on', 'start': 458.66, 'end': 458.78}, {'word': ' some', 'start': 458.78, 'end': 458.94}, {'word': ' people.', 'start': 458.94, 'end': 459.24}, {'word': ' Well,', 'start': 459.44, 'end': 459.66}, {'word': ' right.', 'start': 459.68, 'end': 459.8}, {'word': ' But', 'start': 460.1, 'end': 460.42}, {'word': \" that's\", 'start': 460.42, 'end': 460.82}, {'word': ' how', 'start': 460.82, 'end': 460.86}, {'word': ' you', 'start': 460.86, 'end': 460.98}, {'word': ' find', 'start': 460.98, 'end': 461.22}, {'word': ' people.', 'start': 461.22, 'end': 461.42}, {'word': ' But', 'start': 461.54, 'end': 461.7}, {'word': ' also,', 'start': 461.7, 'end': 462.12}, {'word': ' then', 'start': 462.22, 'end': 462.78}, {'word': ' if', 'start': 462.78, 'end': 463.26}, {'word': ' someone', 'start': 463.26, 'end': 463.52}, {'word': ' comes', 'start': 463.52, 'end': 463.84}, {'word': ' to', 'start': 463.84, 'end': 464.06}, {'word': ' me', 'start': 464.06, 'end': 464.26}, {'word': ' and', 'start': 464.26, 'end': 464.52}, {'word': ' says,', 'start': 464.52, 'end': 464.78}, {'word': ' hey,', 'start': 464.92, 'end': 465.08}, {'word': ' have', 'start': 465.1, 'end': 465.16}, {'word': ' you', 'start': 465.16, 'end': 465.24}, {'word': ' heard', 'start': 465.24, 'end': 465.38}, {'word': ' of', 'start': 465.38, 'end': 465.48}, {'word': ' this', 'start': 465.48, 'end': 465.54}, {'word': ' company?', 'start': 465.54, 'end': 465.84}, {'word': \" I'm\", 'start': 465.94, 'end': 466.06}, {'word': ' like,', 'start': 466.06, 'end': 466.18}, {'word': ' oh,', 'start': 466.24, 'end': 466.42}, {'word': ' that', 'start': 466.42, 'end': 466.54}, {'word': ' was', 'start': 466.54, 'end': 466.7}, {'word': ' great.', 'start': 466.7, 'end': 466.98}, {'word': \" You've\", 'start': 467.12, 'end': 467.28}, {'word': ' got', 'start': 467.28, 'end': 467.38}, {'word': ' to', 'start': 467.38, 'end': 467.5}, {'word': ' go.', 'start': 467.5, 'end': 467.6}, {'word': ' If', 'start': 468.02000000000004, 'end': 468.34000000000003}, {'word': ' you', 'start': 468.34000000000003, 'end': 468.66}, {'word': ' stay', 'start': 468.66, 'end': 469.0}, {'word': ' behind', 'start': 469.0, 'end': 469.32}, {'word': ' the', 'start': 469.32, 'end': 469.54}, {'word': ' wall', 'start': 469.54, 'end': 469.72}, {'word': ' and', 'start': 469.72, 'end': 469.88}, {'word': ' just', 'start': 469.88, 'end': 470.02}, {'word': ' build', 'start': 470.02, 'end': 470.26}, {'word': ' your', 'start': 470.26, 'end': 470.38}, {'word': ' product', 'start': 470.38, 'end': 470.74}, {'word': ' and', 'start': 470.74, 'end': 471.22}, {'word': ' somebody', 'start': 471.22, 'end': 471.52}, {'word': ' comes', 'start': 471.52, 'end': 471.72}, {'word': ' to', 'start': 471.72, 'end': 471.94}, {'word': ' me', 'start': 471.94, 'end': 472.04}, {'word': ' and', 'start': 472.04, 'end': 472.16}, {'word': ' says,', 'start': 472.16, 'end': 472.3}, {'word': ' oh,', 'start': 472.34, 'end': 472.48}, {'word': \" they're\", 'start': 472.48, 'end': 472.6}, {'word': ' looking', 'start': 472.6, 'end': 472.76}, {'word': ' for', 'start': 472.76, 'end': 472.98}, {'word': ' somebody.', 'start': 472.98, 'end': 473.18}, {'word': ' And', 'start': 473.26, 'end': 473.32}, {'word': ' I', 'start': 473.32, 'end': 473.4}, {'word': ' go,', 'start': 473.4, 'end': 473.54}, {'word': ' I', 'start': 473.58, 'end': 473.66}, {'word': \" don't\", 'start': 473.66, 'end': 473.82}, {'word': ' know', 'start': 473.82, 'end': 473.88}, {'word': ' who', 'start': 473.88, 'end': 473.98}, {'word': ' they', 'start': 473.98, 'end': 474.1}, {'word': ' are.', 'start': 474.1, 'end': 474.36}, {'word': ' A', 'start': 474.36, 'end': 475.02}, {'word': ' great', 'start': 475.02, 'end': 475.36}, {'word': ' person', 'start': 475.36, 'end': 475.88}, {'word': ' may', 'start': 475.88, 'end': 476.44}, {'word': ' not', 'start': 476.44, 'end': 476.62}, {'word': ' be', 'start': 476.62, 'end': 476.8}, {'word': ' as', 'start': 476.8, 'end': 476.92}, {'word': ' inspired', 'start': 476.92, 'end': 477.24}, {'word': ' to', 'start': 477.24, 'end': 477.48}, {'word': ' apply.', 'start': 477.48, 'end': 477.72}, {'word': ' And', 'start': 478.08, 'end': 478.4}, {'word': ' so', 'start': 478.4, 'end': 478.58}, {'word': ' I', 'start': 478.58, 'end': 478.8}, {'word': ' think', 'start': 478.8, 'end': 479.02}, {'word': ' that', 'start': 479.02, 'end': 479.16}, {'word': ' the', 'start': 479.16, 'end': 479.28}, {'word': ' people', 'start': 479.28, 'end': 479.56}, {'word': ' have', 'start': 479.56, 'end': 479.8}, {'word': ' to', 'start': 479.8, 'end': 480.0}, {'word': ' build', 'start': 480.0, 'end': 480.2}, {'word': ' a', 'start': 480.2, 'end': 480.28}, {'word': ' reputation', 'start': 480.28, 'end': 480.64}, {'word': ' in', 'start': 480.64, 'end': 481.34}, {'word': ' their', 'start': 481.34, 'end': 481.7}, {'word': ' community', 'start': 481.7, 'end': 482.1}, {'word': ' and', 'start': 482.1, 'end': 482.4}, {'word': ' in', 'start': 482.4, 'end': 482.48}, {'word': ' their', 'start': 482.48, 'end': 482.62}, {'word': ' industry.', 'start': 482.62, 'end': 482.96}, {'word': ' And', 'start': 483.12, 'end': 483.58}, {'word': ' I', 'start': 483.58, 'end': 483.68}, {'word': ' think', 'start': 483.68, 'end': 483.86}, {'word': ' companies', 'start': 483.86, 'end': 484.4}, {'word': ' have', 'start': 484.4, 'end': 485.02}, {'word': ' to', 'start': 485.02, 'end': 485.24}, {'word': ' go', 'start': 485.24, 'end': 485.36}, {'word': ' back', 'start': 485.36, 'end': 485.68}, {'word': ' to', 'start': 485.68, 'end': 485.92}, {'word': ' the', 'start': 485.92, 'end': 486.02}, {'word': ' days', 'start': 486.02, 'end': 486.38}, {'word': ' of', 'start': 486.38, 'end': 486.6}, {'word': ' building', 'start': 486.6, 'end': 486.92}, {'word': ' a', 'start': 486.92, 'end': 487.08}, {'word': ' reputation', 'start': 487.08, 'end': 487.42}, {'word': ' in', 'start': 487.42, 'end': 487.8}, {'word': ' their', 'start': 487.8, 'end': 487.88}, {'word': ' community.', 'start': 487.88, 'end': 488.2}, {'word': ' They', 'start': 488.22, 'end': 488.4}, {'word': ' got', 'start': 488.4, 'end': 488.46}, {'word': ' to', 'start': 488.46, 'end': 488.52}, {'word': ' be', 'start': 488.52, 'end': 488.62}, {'word': ' there.', 'start': 488.62, 'end': 488.82}, {'word': \" It's\", 'start': 488.88, 'end': 489.36}, {'word': ' a', 'start': 489.36, 'end': 489.36}, {'word': ' both', 'start': 489.36, 'end': 489.56}, {'word': ' ways.', 'start': 489.56, 'end': 489.74}, {'word': ' Yeah,', 'start': 489.82, 'end': 490.14}, {'word': \" it's\", 'start': 490.68, 'end': 490.98}, {'word': ' a', 'start': 490.98, 'end': 491.02}, {'word': ' both', 'start': 491.02, 'end': 491.26}, {'word': ' ways.', 'start': 491.26, 'end': 491.52}, {'word': ' Another', 'start': 492.34, 'end': 492.82}, {'word': ' one', 'start': 492.82, 'end': 493.3}, {'word': ' thing', 'start': 493.3, 'end': 493.56}, {'word': ' we', 'start': 493.56, 'end': 493.78}, {'word': ' have', 'start': 493.78, 'end': 493.88}, {'word': ' done', 'start': 493.88, 'end': 494.1}, {'word': ' in', 'start': 494.1, 'end': 494.36}, {'word': ' Onanu', 'start': 494.36, 'end': 494.7}, {'word': ' is', 'start': 494.7, 'end': 495.02}, {'word': ' we', 'start': 495.02, 'end': 495.6}, {'word': ' welcome', 'start': 495.6, 'end': 496.1}, {'word': ' anybody', 'start': 496.1, 'end': 496.58}, {'word': ' who', 'start': 496.58, 'end': 496.86}, {'word': ' wants', 'start': 496.86, 'end': 497.02}, {'word': ' an', 'start': 497.02, 'end': 497.18}, {'word': ' internship.', 'start': 497.18, 'end': 497.56}, {'word': ' I', 'start': 498.46, 'end': 498.94}, {'word': ' know', 'start': 498.94, 'end': 499.28}, {'word': ' we', 'start': 499.28, 'end': 499.5}, {'word': ' are', 'start': 499.5, 'end': 499.56}, {'word': ' a', 'start': 499.56, 'end': 499.7}, {'word': ' bootstrapping', 'start': 499.7, 'end': 500.18}, {'word': ' it.', 'start': 500.18, 'end': 500.5}, {'word': ' We', 'start': 500.56, 'end': 500.66}, {'word': \" don't\", 'start': 500.66, 'end': 500.86}, {'word': ' pay.', 'start': 500.86, 'end': 501.08}, {'word': ' So', 'start': 501.26, 'end': 501.46}, {'word': ' what', 'start': 501.46, 'end': 501.64}, {'word': ' do', 'start': 501.64, 'end': 501.74}, {'word': ' we', 'start': 501.74, 'end': 501.82}, {'word': ' give?', 'start': 501.82, 'end': 501.98}, {'word': ' Three', 'start': 502.06, 'end': 502.26}, {'word': ' months', 'start': 502.26, 'end': 502.54}, {'word': ' time', 'start': 502.54, 'end': 502.94}, {'word': ' to', 'start': 502.94, 'end': 503.38}, {'word': ' prove', 'start': 503.38, 'end': 503.62}, {'word': ' them.', 'start': 503.62, 'end': 503.9}, {'word': ' Why?', 'start': 503.9, 'end': 504.14}, {'word': ' Because', 'start': 504.36, 'end': 504.44}, {'word': ' we', 'start': 504.44, 'end': 504.44}, {'word': ' know', 'start': 504.44, 'end': 504.44}, {'word': ' that', 'start': 504.44, 'end': 504.44}, {'word': ' you', 'start': 504.44, 'end': 504.44}, {'word': ' need', 'start': 504.44, 'end': 504.44}, {'word': ' to', 'start': 504.44, 'end': 504.58}, {'word': ' hire', 'start': 504.58, 'end': 504.76}, {'word': ' them.', 'start': 504.76, 'end': 504.96}, {'word': ' Some', 'start': 505.5, 'end': 505.94}, {'word': ' kind', 'start': 505.94, 'end': 506.3}, {'word': ' of', 'start': 506.3, 'end': 506.5}, {'word': ' a', 'start': 506.5, 'end': 506.58}, {'word': ' job.', 'start': 506.58, 'end': 506.8}, {'word': \" That's\", 'start': 507.22, 'end': 507.66}, {'word': ' what', 'start': 507.66, 'end': 507.74}, {'word': ' we', 'start': 507.74, 'end': 507.92}, {'word': ' have', 'start': 507.92, 'end': 508.02}, {'word': ' done', 'start': 508.02, 'end': 508.22}, {'word': ' with', 'start': 508.22, 'end': 508.42}, {'word': ' a', 'start': 508.42, 'end': 508.52}, {'word': ' lot', 'start': 508.52, 'end': 508.8}, {'word': ' of', 'start': 508.8, 'end': 508.98}, {'word': ' people.', 'start': 508.98, 'end': 509.2}, {'word': ' Some', 'start': 509.64, 'end': 510.08}, {'word': ' people', 'start': 510.08, 'end': 510.34}, {'word': ' came', 'start': 510.34, 'end': 510.62}, {'word': ' and', 'start': 510.62, 'end': 510.96}, {'word': ' they', 'start': 510.96, 'end': 511.06}, {'word': ' said,', 'start': 511.06, 'end': 511.24}, {'word': ' OK,', 'start': 511.32, 'end': 511.48}, {'word': ' they', 'start': 511.66, 'end': 511.94}, {'word': \" didn't.\", 'start': 511.94, 'end': 512.38}, {'word': ' We', 'start': 512.44, 'end': 512.88}, {'word': ' are', 'start': 512.88, 'end': 512.96}, {'word': ' not', 'start': 512.96, 'end': 513.12}, {'word': ' looking', 'start': 513.12, 'end': 513.32}, {'word': ' for', 'start': 513.32, 'end': 513.54}, {'word': ' paper', 'start': 513.54, 'end': 513.8}, {'word': ' files.', 'start': 513.8, 'end': 514.2}, {'word': ' Right.', 'start': 514.36, 'end': 514.66}, {'word': ' We', 'start': 514.76, 'end': 515.08}, {'word': \" don't\", 'start': 515.08, 'end': 515.42}, {'word': ' guide', 'start': 515.42, 'end': 515.66}, {'word': ' you.', 'start': 515.66, 'end': 515.9}, {'word': \" We'll\", 'start': 515.92, 'end': 516.1}, {'word': ' give', 'start': 516.1, 'end': 516.32}, {'word': ' you', 'start': 516.32, 'end': 516.46}, {'word': ' a', 'start': 516.46, 'end': 516.48}, {'word': ' project.', 'start': 516.48, 'end': 516.88}, {'word': ' You', 'start': 517.02, 'end': 517.14}, {'word': ' come', 'start': 517.14, 'end': 517.38}, {'word': ' and', 'start': 517.38, 'end': 517.56}, {'word': ' beat', 'start': 517.56, 'end': 517.82}, {'word': ' us', 'start': 517.82, 'end': 517.98}, {'word': ' down.', 'start': 517.98, 'end': 518.26}, {'word': ' This', 'start': 518.34, 'end': 518.5}, {'word': ' is', 'start': 518.5, 'end': 518.62}, {'word': ' what', 'start': 518.62, 'end': 518.74}, {'word': ' you', 'start': 518.74, 'end': 518.86}, {'word': ' should', 'start': 518.86, 'end': 519.08}, {'word': ' do.', 'start': 519.08, 'end': 519.22}, {'word': \" That's\", 'start': 520.18, 'end': 520.62}, {'word': ' what', 'start': 520.62, 'end': 520.74}, {'word': ' we', 'start': 520.74, 'end': 520.92}, {'word': ' have.', 'start': 520.92, 'end': 521.06}, {'word': ' I', 'start': 521.12, 'end': 521.3}, {'word': ' think', 'start': 521.3, 'end': 521.64}, {'word': ' that', 'start': 521.64, 'end': 521.86}, {'word': ' is', 'start': 521.86, 'end': 522.14}, {'word': ' where', 'start': 522.14, 'end': 522.32}, {'word': ' the', 'start': 522.32, 'end': 523.12}, {'word': ' market', 'start': 523.12, 'end': 523.5}, {'word': ' is', 'start': 523.5, 'end': 523.72}, {'word': ' going', 'start': 523.72, 'end': 523.94}, {'word': ' in', 'start': 523.94, 'end': 524.16}, {'word': ' the', 'start': 524.16, 'end': 524.22}, {'word': ' jobs', 'start': 524.22, 'end': 524.54}, {'word': ' now', 'start': 524.54, 'end': 524.82}, {'word': ' is', 'start': 524.82, 'end': 525.08}, {'word': ' every', 'start': 525.08, 'end': 525.86}, {'word': ' company', 'start': 525.86, 'end': 526.46}, {'word': ' with', 'start': 526.46, 'end': 526.92}, {'word': ' the', 'start': 526.92, 'end': 526.96}, {'word': ' AI.', 'start': 526.96, 'end': 527.16}, {'word': ' Because', 'start': 528.1999999999999, 'end': 528.64}, {'word': ' you', 'start': 528.64, 'end': 529.08}, {'word': ' have', 'start': 529.08, 'end': 529.34}, {'word': ' now', 'start': 529.34, 'end': 529.86}, {'word': ' you', 'start': 529.86, 'end': 530.38}, {'word': ' have', 'start': 530.38, 'end': 530.64}, {'word': ' we', 'start': 530.64, 'end': 531.18}, {'word': ' crossed', 'start': 531.18, 'end': 531.62}, {'word': ' information', 'start': 531.62, 'end': 532.04}, {'word': ' is', 'start': 532.04, 'end': 532.56}, {'word': ' now', 'start': 532.56, 'end': 532.86}, {'word': ' we', 'start': 532.86, 'end': 533.06}, {'word': ' are', 'start': 533.06, 'end': 533.16}, {'word': ' in', 'start': 533.16, 'end': 533.3}, {'word': ' technology', 'start': 533.3, 'end': 533.8}, {'word': ' age.', 'start': 533.8, 'end': 534.28}, {'word': ' We', 'start': 534.7, 'end': 535.1}, {'word': ' have', 'start': 535.1, 'end': 535.48}, {'word': ' the', 'start': 535.48, 'end': 535.98}, {'word': ' AI', 'start': 535.98, 'end': 536.2}, {'word': ' tools', 'start': 536.2, 'end': 536.52}, {'word': ' for', 'start': 536.52, 'end': 536.78}, {'word': ' everything', 'start': 536.78, 'end': 537.32}, {'word': ' you', 'start': 537.32, 'end': 537.68}, {'word': ' can', 'start': 537.68, 'end': 537.96}, {'word': ' do.', 'start': 537.96, 'end': 538.3}, {'word': ' Now,', 'start': 539.4000000000001, 'end': 539.8000000000001}, {'word': ' what', 'start': 539.8000000000001, 'end': 540.2}, {'word': ' you', 'start': 540.2, 'end': 540.42}, {'word': ' can', 'start': 540.42, 'end': 540.68}, {'word': ' do', 'start': 540.68, 'end': 541.02}, {'word': ' with', 'start': 541.02, 'end': 541.3}, {'word': ' the', 'start': 541.3, 'end': 541.42}, {'word': ' AI', 'start': 541.42, 'end': 541.64}, {'word': ' tools,', 'start': 541.64, 'end': 541.96}, {'word': ' the', 'start': 542.06, 'end': 542.14}, {'word': ' information', 'start': 542.14, 'end': 542.46}, {'word': ' you', 'start': 542.46, 'end': 542.78}, {'word': ' already', 'start': 542.78, 'end': 543.1}, {'word': ' have,', 'start': 543.1, 'end': 543.44}, {'word': ' just', 'start': 543.84, 'end': 544.74}, {'word': ' a', 'start': 544.74, 'end': 544.9}, {'word': ' click', 'start': 544.9, 'end': 545.1}, {'word': ' of', 'start': 545.1, 'end': 545.28}, {'word': ' a', 'start': 545.28, 'end': 545.42}, {'word': ' button.', 'start': 545.42, 'end': 545.66}, {'word': ' Right.', 'start': 545.98, 'end': 546.0}, {'word': ' And', 'start': 546.14, 'end': 546.32}, {'word': ' people', 'start': 546.32, 'end': 546.56}, {'word': ' are', 'start': 546.56, 'end': 546.72}, {'word': ' scared', 'start': 546.72, 'end': 547.0}, {'word': ' of', 'start': 547.0, 'end': 547.18}, {'word': ' the', 'start': 547.18, 'end': 547.26}, {'word': ' AI', 'start': 547.26, 'end': 547.44}, {'word': ' tools.', 'start': 547.44, 'end': 547.76}, {'word': ' But,', 'start': 547.96, 'end': 548.18}, {'word': ' you', 'start': 548.18, 'end': 548.7}, {'word': ' know,', 'start': 548.7, 'end': 548.88}, {'word': \" we're\", 'start': 548.9, 'end': 549.4}, {'word': ' still', 'start': 549.4, 'end': 549.58}, {'word': ' in', 'start': 549.58, 'end': 549.74}, {'word': ' the', 'start': 549.74, 'end': 549.84}, {'word': ' infancy.', 'start': 549.84, 'end': 550.3}, {'word': ' Yeah.', 'start': 550.44, 'end': 550.8}, {'word': ' So', 'start': 550.8, 'end': 551.08}, {'word': \" we're\", 'start': 551.08, 'end': 551.4}, {'word': ' not', 'start': 551.4, 'end': 551.52}, {'word': ' even', 'start': 551.52, 'end': 551.7}, {'word': ' started.', 'start': 551.7, 'end': 552.04}, {'word': \" We're\", 'start': 552.28, 'end': 552.58}, {'word': ' not', 'start': 552.58, 'end': 552.66}, {'word': ' even', 'start': 552.66, 'end': 552.86}, {'word': ' started.', 'start': 552.86, 'end': 553.24}, {'word': ' Right.', 'start': 553.38, 'end': 553.54}, {'word': ' I', 'start': 553.58, 'end': 553.7}, {'word': ' mean,', 'start': 553.7, 'end': 553.8}, {'word': ' ChatGPT', 'start': 553.86, 'end': 554.46}, {'word': ' was', 'start': 554.46, 'end': 554.72}, {'word': ' two', 'start': 554.72, 'end': 554.94}, {'word': ' and', 'start': 554.94, 'end': 555.16}, {'word': ' a', 'start': 555.16, 'end': 555.26}, {'word': ' half', 'start': 555.26, 'end': 555.36}, {'word': ' years', 'start': 555.36, 'end': 555.56}, {'word': ' ago.', 'start': 555.56, 'end': 555.98}, {'word': ' Yeah.', 'start': 556.04, 'end': 556.32}, {'word': ' And,', 'start': 556.38, 'end': 556.68}, {'word': ' you', 'start': 556.84, 'end': 557.1}, {'word': ' know,', 'start': 557.1, 'end': 557.22}, {'word': ' I', 'start': 557.24, 'end': 557.32}, {'word': ' credit', 'start': 557.32, 'end': 557.78}, {'word': ' a', 'start': 557.78, 'end': 557.98}, {'word': ' friend', 'start': 557.98, 'end': 558.12}, {'word': ' of', 'start': 558.12, 'end': 558.24}, {'word': ' mine', 'start': 558.24, 'end': 558.42}, {'word': ' who', 'start': 558.42, 'end': 558.6}, {'word': ' ChatGPT', 'start': 558.6, 'end': 559.24}, {'word': ' had', 'start': 559.24, 'end': 559.44}, {'word': ' released', 'start': 559.44, 'end': 559.72}, {'word': ' like', 'start': 559.72, 'end': 559.94}, {'word': ' three', 'start': 559.94, 'end': 560.16}, {'word': ' days', 'start': 560.16, 'end': 560.34}, {'word': ' before.', 'start': 560.34, 'end': 560.66}, {'word': ' And', 'start': 560.72, 'end': 560.84}, {'word': ' he', 'start': 560.84, 'end': 560.88}, {'word': ' said,', 'start': 560.88, 'end': 561.06}, {'word': ' you', 'start': 561.1, 'end': 561.18}, {'word': ' got', 'start': 561.18, 'end': 561.32}, {'word': ' to', 'start': 561.32, 'end': 561.36}, {'word': ' look', 'start': 561.36, 'end': 561.52}, {'word': ' at', 'start': 561.52, 'end': 561.64}, {'word': ' this.', 'start': 561.64, 'end': 561.84}, {'word': ' Not', 'start': 561.84, 'end': 562.42}, {'word': ' just', 'start': 562.42, 'end': 562.74}, {'word': ' because', 'start': 562.74, 'end': 563.06}, {'word': ' of', 'start': 563.06, 'end': 563.5}, {'word': ' your', 'start': 563.5, 'end': 563.66}, {'word': ' role', 'start': 563.66, 'end': 563.88}, {'word': ' with', 'start': 563.88, 'end': 564.08}, {'word': ' ATC,', 'start': 564.08, 'end': 564.52}, {'word': ' but', 'start': 564.54, 'end': 564.74}, {'word': ' your', 'start': 564.74, 'end': 564.94}, {'word': ' role', 'start': 564.94, 'end': 565.14}, {'word': ' as', 'start': 565.14, 'end': 565.28}, {'word': ' a', 'start': 565.28, 'end': 565.36}, {'word': ' professional', 'start': 565.36, 'end': 565.6}, {'word': ' speaker.', 'start': 565.6, 'end': 565.94}, {'word': \" You've\", 'start': 566.14, 'end': 566.3}, {'word': ' got', 'start': 566.3, 'end': 566.36}, {'word': ' to', 'start': 566.36, 'end': 566.46}, {'word': ' learn', 'start': 566.46, 'end': 566.62}, {'word': \" what's\", 'start': 566.62, 'end': 566.84}, {'word': ' going', 'start': 566.84, 'end': 566.96}, {'word': ' on', 'start': 566.96, 'end': 567.24}, {'word': ' because', 'start': 567.24, 'end': 567.42}, {'word': ' this', 'start': 567.42, 'end': 567.6}, {'word': ' is', 'start': 567.6, 'end': 567.72}, {'word': ' going', 'start': 567.72, 'end': 567.82}, {'word': ' to', 'start': 567.82, 'end': 567.9}, {'word': ' affect', 'start': 567.9, 'end': 568.08}, {'word': ' things.', 'start': 568.08, 'end': 568.38}, {'word': ' So', 'start': 568.52, 'end': 568.82}, {'word': ' I', 'start': 568.82, 'end': 568.98}, {'word': ' was', 'start': 568.98, 'end': 569.2}, {'word': ' accidentally', 'start': 569.2, 'end': 570.1}, {'word': ' early', 'start': 570.1, 'end': 570.68}, {'word': ' exposed', 'start': 570.68, 'end': 571.28}, {'word': ' to', 'start': 571.28, 'end': 571.62}, {'word': ' it.', 'start': 571.62, 'end': 571.82}, {'word': ' And,', 'start': 572.18, 'end': 572.54}, {'word': ' you', 'start': 572.58, 'end': 573.34}, {'word': ' know,', 'start': 573.34, 'end': 573.46}, {'word': \" it's\", 'start': 573.48, 'end': 573.62}, {'word': ' come', 'start': 573.62, 'end': 573.74}, {'word': ' a', 'start': 573.74, 'end': 573.9}, {'word': ' long', 'start': 573.9, 'end': 574.06}, {'word': ' way', 'start': 574.06, 'end': 574.28}, {'word': ' in', 'start': 574.28, 'end': 574.34}, {'word': ' two', 'start': 574.34, 'end': 574.48}, {'word': ' and', 'start': 574.48, 'end': 574.6}, {'word': ' a', 'start': 574.6, 'end': 574.66}, {'word': ' half', 'start': 574.66, 'end': 574.76}, {'word': ' years.', 'start': 574.76, 'end': 574.96}, {'word': ' It', 'start': 575.14, 'end': 575.28}, {'word': \" doesn't\", 'start': 575.28, 'end': 575.5}, {'word': ' hallucinate', 'start': 575.5, 'end': 575.98}, {'word': ' as', 'start': 575.98, 'end': 576.16}, {'word': ' much.', 'start': 576.16, 'end': 576.52}, {'word': ' You', 'start': 576.54, 'end': 576.7}, {'word': ' know,', 'start': 576.7, 'end': 576.76}, {'word': ' it', 'start': 576.82, 'end': 576.96}, {'word': \" doesn't\", 'start': 576.96, 'end': 577.4}, {'word': ' use', 'start': 577.4, 'end': 577.6}, {'word': ' as', 'start': 577.6, 'end': 577.82}, {'word': ' many', 'start': 577.82, 'end': 577.98}, {'word': ' goofy', 'start': 577.98, 'end': 578.52}, {'word': ' phrases.', 'start': 578.52, 'end': 578.88}, {'word': ' However,', 'start': 580.3199999999999, 'end': 580.68}, {'word': \" it's\", 'start': 580.68, 'end': 581.04}, {'word': ' still', 'start': 581.04, 'end': 581.22}, {'word': ' in', 'start': 581.22, 'end': 581.38}, {'word': ' its', 'start': 581.38, 'end': 581.54}, {'word': ' infancy.', 'start': 581.54, 'end': 581.94}, {'word': ' And', 'start': 582.02, 'end': 582.12}, {'word': ' so', 'start': 582.12, 'end': 582.24}, {'word': ' people', 'start': 582.24, 'end': 582.56}, {'word': ' who', 'start': 582.56, 'end': 582.76}, {'word': ' are', 'start': 582.76, 'end': 582.86}, {'word': ' like,', 'start': 582.86, 'end': 583.08}, {'word': ' oh,', 'start': 583.12, 'end': 583.3}, {'word': ' AI', 'start': 583.42, 'end': 583.74}, {'word': ' can', 'start': 583.74, 'end': 584.04}, {'word': ' do', 'start': 584.04, 'end': 584.26}, {'word': ' the', 'start': 584.26, 'end': 584.52}, {'word': ' writing.', 'start': 584.52, 'end': 584.78}, {'word': ' No,', 'start': 584.8, 'end': 584.98}, {'word': ' you', 'start': 585.06, 'end': 585.46}, {'word': \" can't\", 'start': 585.46, 'end': 585.94}, {'word': ' trust', 'start': 585.94, 'end': 586.28}, {'word': ' it.', 'start': 586.28, 'end': 586.46}, {'word': ' If', 'start': 586.46, 'end': 586.66}, {'word': \" you're\", 'start': 586.66, 'end': 586.8}, {'word': ' looking', 'start': 586.8, 'end': 586.9}, {'word': ' for', 'start': 586.9, 'end': 587.06}, {'word': ' AI', 'start': 587.06, 'end': 587.3}, {'word': ' to', 'start': 587.3, 'end': 587.72}, {'word': ' do', 'start': 587.72, 'end': 587.88}, {'word': ' coding', 'start': 587.88, 'end': 588.26}, {'word': ' or', 'start': 588.26, 'end': 589.02}, {'word': ' writing,', 'start': 589.02, 'end': 589.38}, {'word': ' you', 'start': 589.62, 'end': 590.3}, {'word': \" can't\", 'start': 590.3, 'end': 590.64}, {'word': ' trust', 'start': 590.64, 'end': 590.88}, {'word': ' it', 'start': 590.88, 'end': 591.06}, {'word': ' to', 'start': 591.06, 'end': 591.18}, {'word': ' give', 'start': 591.18, 'end': 591.28}, {'word': ' you', 'start': 591.28, 'end': 591.4}, {'word': ' the', 'start': 591.4, 'end': 591.44}, {'word': ' final', 'start': 591.44, 'end': 591.66}, {'word': ' product.', 'start': 591.66, 'end': 591.82}, {'word': ' You', 'start': 591.84, 'end': 592.1}, {'word': ' still', 'start': 592.1, 'end': 592.36}, {'word': ' need', 'start': 592.36, 'end': 592.66}, {'word': ' humans', 'start': 592.66, 'end': 593.04}, {'word': ' who', 'start': 593.04, 'end': 593.24}, {'word': ' are', 'start': 593.24, 'end': 593.28}, {'word': ' going', 'start': 593.28, 'end': 593.36}, {'word': ' to', 'start': 593.36, 'end': 593.48}, {'word': ' tweak', 'start': 593.48, 'end': 593.72}, {'word': ' it.', 'start': 593.72, 'end': 593.92}, {'word': ' I', 'start': 593.96, 'end': 594.38}, {'word': ' know', 'start': 594.38, 'end': 594.64}, {'word': ' somebody', 'start': 594.64, 'end': 594.94}, {'word': \" who's\", 'start': 594.94, 'end': 595.18}, {'word': ' a', 'start': 595.18, 'end': 595.22}, {'word': ' coder.', 'start': 595.22, 'end': 595.6}, {'word': ' And', 'start': 595.68, 'end': 596.08}, {'word': ' they', 'start': 596.08, 'end': 596.28}, {'word': ' use', 'start': 596.28, 'end': 596.66}, {'word': ' AI.', 'start': 596.66, 'end': 596.94}, {'word': ' But', 'start': 597.18, 'end': 597.56}, {'word': ' he', 'start': 597.56, 'end': 597.68}, {'word': ' said,', 'start': 597.68, 'end': 597.8}, {'word': ' look,', 'start': 597.84, 'end': 597.96}, {'word': \" it's\", 'start': 598.04, 'end': 598.18}, {'word': ' not', 'start': 598.18, 'end': 598.32}, {'word': ' writing', 'start': 598.32, 'end': 598.66}, {'word': ' the', 'start': 598.66, 'end': 598.9}, {'word': ' best', 'start': 598.9, 'end': 599.14}, {'word': ' code.', 'start': 599.14, 'end': 599.56}, {'word': ' So', 'start': 599.74, 'end': 599.98}, {'word': ' he', 'start': 599.98, 'end': 600.1}, {'word': ' treats', 'start': 600.1, 'end': 600.38}, {'word': ' it', 'start': 600.38, 'end': 600.54}, {'word': ' in', 'start': 600.54, 'end': 600.66}, {'word': ' one', 'start': 600.66, 'end': 600.8}, {'word': ' of', 'start': 600.8, 'end': 600.88}, {'word': ' two', 'start': 600.88, 'end': 601.04}, {'word': ' ways.', 'start': 601.04, 'end': 601.32}, {'word': ' He', 'start': 601.68, 'end': 602.12}, {'word': ' either', 'start': 602.12, 'end': 602.42}, {'word': ' treats', 'start': 602.42, 'end': 602.66}, {'word': ' it', 'start': 602.66, 'end': 602.86}, {'word': ' as', 'start': 602.86, 'end': 603.0}, {'word': ' a', 'start': 603.0, 'end': 603.14}, {'word': ' junior', 'start': 603.14, 'end': 603.62}, {'word': ' coder', 'start': 603.62, 'end': 604.22}, {'word': ' who', 'start': 604.22, 'end': 604.44}, {'word': ' he', 'start': 604.44, 'end': 604.62}, {'word': ' has', 'start': 604.62, 'end': 604.8}, {'word': ' to', 'start': 604.8, 'end': 604.94}, {'word': ' review', 'start': 604.94, 'end': 605.2}, {'word': ' everything', 'start': 605.2, 'end': 605.56}, {'word': ' they', 'start': 605.56, 'end': 605.82}, {'word': ' do.', 'start': 605.82, 'end': 606.12}, {'word': ' Yeah.', 'start': 606.14, 'end': 606.42}, {'word': ' Or', 'start': 606.5, 'end': 606.82}, {'word': ' as', 'start': 606.82, 'end': 607.06}, {'word': ' the', 'start': 607.06, 'end': 607.2}, {'word': ' most', 'start': 607.2, 'end': 607.36}, {'word': ' senior', 'start': 607.36, 'end': 607.84}, {'word': ' creative', 'start': 607.84, 'end': 608.36}, {'word': ' coder', 'start': 608.36, 'end': 608.92}, {'word': \" he's\", 'start': 608.92, 'end': 609.12}, {'word': ' ever', 'start': 609.12, 'end': 609.26}, {'word': ' seen.', 'start': 609.26, 'end': 609.64}, {'word': ' Yeah.', 'start': 609.8, 'end': 609.96}, {'word': \" Who's\", 'start': 610.04, 'end': 610.34}, {'word': ' on', 'start': 610.34, 'end': 610.58}, {'word': ' LSD.', 'start': 610.58, 'end': 611.14}, {'word': ' Yeah.', 'start': 611.36, 'end': 611.78}, {'word': ' Which', 'start': 611.86, 'end': 612.06}, {'word': ' means', 'start': 612.06, 'end': 612.28}, {'word': ' he', 'start': 612.28, 'end': 612.46}, {'word': ' still', 'start': 612.46, 'end': 612.74}, {'word': ' has', 'start': 612.74, 'end': 612.92}, {'word': ' to', 'start': 612.92, 'end': 613.06}, {'word': ' go', 'start': 613.06, 'end': 613.22}, {'word': ' back', 'start': 613.22, 'end': 613.42}, {'word': ' and', 'start': 613.42, 'end': 613.58}, {'word': ' review', 'start': 613.58, 'end': 613.8}, {'word': ' everything', 'start': 613.8, 'end': 614.22}, {'word': ' that', 'start': 614.22, 'end': 614.5}, {'word': ' it', 'start': 614.5, 'end': 614.6}, {'word': ' does.', 'start': 614.6, 'end': 614.84}, {'word': ' And', 'start': 615.04, 'end': 615.48}, {'word': ' then', 'start': 615.48, 'end': 615.62}, {'word': \" it's\", 'start': 615.62, 'end': 615.78}, {'word': ' a', 'start': 615.78, 'end': 615.84}, {'word': ' great', 'start': 615.84, 'end': 616.02}, {'word': ' productivity', 'start': 616.02, 'end': 616.48}, {'word': ' tool.', 'start': 616.48, 'end': 616.86}, {'word': ' But', 'start': 617.04, 'end': 617.46}, {'word': ' people', 'start': 617.46, 'end': 617.88}, {'word': ' need', 'start': 617.88, 'end': 618.12}, {'word': ' to,', 'start': 618.12, 'end': 618.3}, {'word': ' for', 'start': 618.42, 'end': 618.64}, {'word': ' the', 'start': 618.64, 'end': 618.76}, {'word': ' time', 'start': 618.76, 'end': 619.02}, {'word': ' being,', 'start': 619.02, 'end': 619.32}, {'word': ' see', 'start': 619.4, 'end': 619.66}, {'word': ' it', 'start': 619.66, 'end': 619.9}, {'word': ' as', 'start': 619.9, 'end': 620.04}, {'word': ' just', 'start': 620.04, 'end': 620.26}, {'word': ' that.', 'start': 620.26, 'end': 620.7}, {'word': ' Yeah.', 'start': 620.74, 'end': 621.16}, {'word': ' And', 'start': 621.84, 'end': 621.94}, {'word': ' then,', 'start': 621.94, 'end': 622.0}, {'word': ' oh,', 'start': 622.04, 'end': 622.14}, {'word': \" it's\", 'start': 622.16, 'end': 622.26}, {'word': ' going', 'start': 622.26, 'end': 622.38}, {'word': ' to', 'start': 622.38, 'end': 622.4}, {'word': ' take', 'start': 622.4, 'end': 622.58}, {'word': ' away', 'start': 622.58, 'end': 622.72}, {'word': ' my', 'start': 622.72, 'end': 622.9}, {'word': ' job.', 'start': 622.9, 'end': 623.18}, {'word': ' No.', 'start': 623.26, 'end': 623.38}, {'word': ' If', 'start': 623.46, 'end': 623.6}, {'word': ' it', 'start': 623.6, 'end': 623.7}, {'word': ' makes', 'start': 623.7, 'end': 623.96}, {'word': ' you', 'start': 623.96, 'end': 624.1}, {'word': ' more', 'start': 624.1, 'end': 624.26}, {'word': ' productive,', 'start': 624.26, 'end': 624.68}, {'word': \" you're\", 'start': 624.92, 'end': 626.08}, {'word': ' going', 'start': 626.08, 'end': 626.16}, {'word': ' to', 'start': 626.16, 'end': 626.32}, {'word': ' keep', 'start': 626.32, 'end': 626.6}, {'word': ' your', 'start': 626.6, 'end': 626.74}, {'word': ' job', 'start': 626.74, 'end': 626.98}, {'word': ' longer', 'start': 626.98, 'end': 627.22}, {'word': ' and', 'start': 627.22, 'end': 627.4}, {'word': \" you're\", 'start': 627.4, 'end': 627.54}, {'word': ' going', 'start': 627.54, 'end': 627.58}, {'word': ' to', 'start': 627.58, 'end': 627.7}, {'word': ' get', 'start': 627.7, 'end': 627.84}, {'word': ' more', 'start': 627.84, 'end': 628.04}, {'word': ' jobs', 'start': 628.04, 'end': 628.42}, {'word': ' if', 'start': 628.42, 'end': 628.58}, {'word': ' you', 'start': 628.58, 'end': 628.68}, {'word': ' know', 'start': 628.68, 'end': 628.72}, {'word': ' how', 'start': 628.72, 'end': 628.86}, {'word': ' to', 'start': 628.86, 'end': 628.92}, {'word': ' use', 'start': 628.92, 'end': 629.08}, {'word': ' these', 'start': 629.08, 'end': 629.28}, {'word': ' tools.', 'start': 629.28, 'end': 629.56}, {'word': ' And', 'start': 629.66, 'end': 629.74}, {'word': \" it's\", 'start': 629.74, 'end': 629.88}, {'word': ' not', 'start': 629.88, 'end': 630.02}, {'word': ' just,', 'start': 630.02, 'end': 630.38}, {'word': ' you', 'start': 630.38, 'end': 631.04}, {'word': ' know,', 'start': 631.04, 'end': 631.18}, {'word': ' LLMs.', 'start': 631.2, 'end': 631.84}, {'word': \" There's\", 'start': 632.44, 'end': 632.58}, {'word': ' so', 'start': 632.58, 'end': 632.76}, {'word': ' much', 'start': 632.76, 'end': 632.98}, {'word': ' more', 'start': 632.98, 'end': 633.24}, {'word': ' coming', 'start': 633.24, 'end': 633.54}, {'word': ' out', 'start': 633.54, 'end': 633.8}, {'word': ' and', 'start': 633.8, 'end': 634.1}, {'word': ' becoming', 'start': 634.1, 'end': 634.46}, {'word': ' more', 'start': 634.46, 'end': 634.84}, {'word': ' ubiquitous', 'start': 634.84, 'end': 635.72}, {'word': ' in', 'start': 635.72, 'end': 635.92}, {'word': ' the', 'start': 635.92, 'end': 635.98}, {'word': ' world', 'start': 635.98, 'end': 636.2}, {'word': ' of', 'start': 636.2, 'end': 636.34}, {'word': ' AI.', 'start': 636.34, 'end': 636.48}, {'word': ' And', 'start': 637.02, 'end': 637.34}, {'word': ' then', 'start': 637.34, 'end': 637.48}, {'word': ' when', 'start': 637.48, 'end': 637.6}, {'word': ' we', 'start': 637.6, 'end': 637.7}, {'word': ' get', 'start': 637.7, 'end': 637.82}, {'word': ' to', 'start': 637.82, 'end': 637.92}, {'word': ' quantum', 'start': 637.92, 'end': 638.18}, {'word': ' computing,', 'start': 638.18, 'end': 638.58}, {'word': ' if', 'start': 638.76, 'end': 639.02}, {'word': ' they', 'start': 639.02, 'end': 639.16}, {'word': ' can', 'start': 639.16, 'end': 639.26}, {'word': ' figure', 'start': 639.26, 'end': 639.46}, {'word': ' that', 'start': 639.46, 'end': 639.64}, {'word': ' out,', 'start': 639.64, 'end': 639.86}, {'word': ' God', 'start': 639.92, 'end': 640.08}, {'word': ' save', 'start': 640.08, 'end': 640.32}, {'word': ' us', 'start': 640.32, 'end': 640.44}, {'word': ' all,', 'start': 640.44, 'end': 640.66}, {'word': ' the', 'start': 640.76, 'end': 640.88}, {'word': ' changes', 'start': 640.88, 'end': 641.14}, {'word': ' are', 'start': 641.14, 'end': 641.36}, {'word': ' going', 'start': 641.36, 'end': 641.46}, {'word': ' to', 'start': 641.46, 'end': 641.56}, {'word': ' go', 'start': 641.56, 'end': 641.66}, {'word': ' even', 'start': 641.66, 'end': 641.8}, {'word': ' faster.', 'start': 641.8, 'end': 642.18}, {'word': ' Yeah.', 'start': 642.36, 'end': 642.44}, {'word': ' And', 'start': 642.54, 'end': 642.66}, {'word': ' I', 'start': 642.66, 'end': 642.72}, {'word': ' think,', 'start': 642.72, 'end': 642.98}, {'word': ' like,', 'start': 643.0, 'end': 643.5}, {'word': ' something', 'start': 643.62, 'end': 643.9}, {'word': ' really', 'start': 643.9, 'end': 644.2}, {'word': ' that', 'start': 644.2, 'end': 644.44}, {'word': ' you', 'start': 644.44, 'end': 644.56}, {'word': ' say,', 'start': 644.56, 'end': 644.76}, {'word': ' like,', 'start': 644.8, 'end': 645.0}, {'word': ' the', 'start': 645.1, 'end': 645.34}, {'word': ' younger', 'start': 645.34, 'end': 645.7}, {'word': ' generation,', 'start': 645.7, 'end': 646.08}, {'word': ' like,', 'start': 646.28, 'end': 646.46}, {'word': \" it's\", 'start': 646.5, 'end': 646.66}, {'word': ' kind', 'start': 646.66, 'end': 646.82}, {'word': ' of', 'start': 646.82, 'end': 646.92}, {'word': ' scary', 'start': 646.92, 'end': 647.18}, {'word': ' to', 'start': 647.18, 'end': 647.42}, {'word': ' go', 'start': 647.42, 'end': 647.58}, {'word': ' out', 'start': 647.58, 'end': 647.78}, {'word': ' and', 'start': 647.78, 'end': 647.88}, {'word': ' network,', 'start': 647.88, 'end': 648.16}, {'word': ' you', 'start': 648.38, 'end': 648.48}, {'word': ' know?', 'start': 648.48, 'end': 648.56}, {'word': ' Like,', 'start': 648.6, 'end': 648.78}, {'word': \" we're\", 'start': 648.8, 'end': 648.92}, {'word': ' not', 'start': 648.92, 'end': 649.04}, {'word': ' used', 'start': 649.04, 'end': 649.22}, {'word': ' to', 'start': 649.22, 'end': 649.44}, {'word': ' it.', 'start': 649.44, 'end': 649.62}, {'word': ' So', 'start': 649.76, 'end': 650.08}, {'word': \" I'm\", 'start': 650.08, 'end': 650.26}, {'word': ' going', 'start': 650.26, 'end': 650.32}, {'word': ' to', 'start': 650.32, 'end': 650.42}, {'word': ' ask', 'start': 650.42, 'end': 650.58}, {'word': ' you.', 'start': 650.58, 'end': 650.8}, {'word': \" I'm\", 'start': 650.8, 'end': 650.96}, {'word': ' going', 'start': 650.96, 'end': 651.02}, {'word': ' to', 'start': 651.02, 'end': 651.08}, {'word': ' throw', 'start': 651.08, 'end': 651.3}, {'word': ' that', 'start': 651.3, 'end': 651.44}, {'word': ' back', 'start': 651.44, 'end': 651.7}, {'word': ' at', 'start': 651.7, 'end': 651.84}, {'word': ' you,', 'start': 651.84, 'end': 651.94}, {'word': ' Max.', 'start': 652.0, 'end': 652.2}, {'word': ' Why', 'start': 652.38, 'end': 652.68}, {'word': ' is', 'start': 652.68, 'end': 653.02}, {'word': ' it', 'start': 653.02, 'end': 653.18}, {'word': ' scary', 'start': 653.18, 'end': 653.6}, {'word': ' to', 'start': 653.6, 'end': 653.88}, {'word': ' go', 'start': 653.88, 'end': 653.98}, {'word': ' network?', 'start': 653.98, 'end': 654.34}, {'word': ' Are', 'start': 654.48, 'end': 654.54}, {'word': ' people', 'start': 654.54, 'end': 654.72}, {'word': ' like', 'start': 654.72, 'end': 655.08}, {'word': ' Babu', 'start': 655.08, 'end': 655.8}, {'word': ' and', 'start': 655.8, 'end': 655.98}, {'word': ' I,', 'start': 655.98, 'end': 656.1}, {'word': ' are', 'start': 656.2, 'end': 656.38}, {'word': ' we', 'start': 656.38, 'end': 656.7}, {'word': ' mean?', 'start': 656.7, 'end': 657.0}, {'word': ' Well,', 'start': 657.76, 'end': 658.12}, {'word': ' because,', 'start': 658.28, 'end': 658.6}, {'word': ' like,', 'start': 658.78, 'end': 659.08}, {'word': ' this', 'start': 659.08, 'end': 659.3}, {'word': ' is', 'start': 659.3, 'end': 659.48}, {'word': ' the', 'start': 659.48, 'end': 659.58}, {'word': ' most,', 'start': 659.58, 'end': 659.92}, {'word': ' like,', 'start': 659.92, 'end': 660.14}, {'word': ' crazy', 'start': 660.14, 'end': 660.5}, {'word': ' part', 'start': 660.5, 'end': 660.78}, {'word': ' about', 'start': 660.78, 'end': 661.06}, {'word': ' all', 'start': 661.06, 'end': 661.36}, {'word': ' of', 'start': 661.36, 'end': 661.48}, {'word': ' this', 'start': 661.48, 'end': 661.66}, {'word': ' to', 'start': 661.66, 'end': 661.82}, {'word': ' me', 'start': 661.82, 'end': 661.92}, {'word': ' is', 'start': 661.92, 'end': 662.14}, {'word': ' that,', 'start': 662.14, 'end': 662.3}, {'word': ' like,', 'start': 662.32, 'end': 662.48}, {'word': ' we', 'start': 662.48, 'end': 662.58}, {'word': ' went', 'start': 662.58, 'end': 662.76}, {'word': ' to', 'start': 662.76, 'end': 662.86}, {'word': ' school.', 'start': 662.86, 'end': 663.3}, {'word': \" We're\", 'start': 663.32, 'end': 663.58}, {'word': ' learning', 'start': 663.58, 'end': 663.84}, {'word': ' this', 'start': 663.84, 'end': 664.02}, {'word': ' stuff,', 'start': 664.02, 'end': 664.3}, {'word': ' like', 'start': 664.38, 'end': 664.6}, {'word': ' accounting.', 'start': 664.6, 'end': 665.04}, {'word': \" We're\", 'start': 665.18, 'end': 665.4}, {'word': ' learning', 'start': 665.4, 'end': 665.52}, {'word': ' other', 'start': 665.52, 'end': 665.74}, {'word': ' stuff.', 'start': 665.74, 'end': 665.98}, {'word': ' And', 'start': 666.22, 'end': 666.58}, {'word': ' then', 'start': 666.58, 'end': 666.7}, {'word': \" we're\", 'start': 666.7, 'end': 666.86}, {'word': ' out', 'start': 666.86, 'end': 666.98}, {'word': ' now', 'start': 666.98, 'end': 667.2}, {'word': ' and', 'start': 667.2, 'end': 667.36}, {'word': \" it's\", 'start': 667.36, 'end': 667.56}, {'word': ' four', 'start': 667.56, 'end': 667.68}, {'word': ' years', 'start': 667.68, 'end': 667.92}, {'word': ' after,', 'start': 667.92, 'end': 668.24}, {'word': ' you', 'start': 668.36, 'end': 668.64}, {'word': ' know,', 'start': 668.64, 'end': 668.82}, {'word': ' like', 'start': 668.82, 'end': 669.22}, {'word': ' two', 'start': 669.22, 'end': 669.44}, {'word': ' years', 'start': 669.44, 'end': 669.62}, {'word': ' after', 'start': 669.62, 'end': 669.9}, {'word': ' AI', 'start': 669.9, 'end': 670.26}, {'word': ' comes', 'start': 670.26, 'end': 670.5}, {'word': ' out.', 'start': 670.5, 'end': 670.76}, {'word': \" There's\", 'start': 671.02, 'end': 671.38}, {'word': ' no', 'start': 671.38, 'end': 671.54}, {'word': ' one,', 'start': 671.54, 'end': 671.68}, {'word': \" there's\", 'start': 671.74, 'end': 671.96}, {'word': ' no', 'start': 671.96, 'end': 672.04}, {'word': ' educational,', 'start': 672.04, 'end': 672.56}, {'word': ' you', 'start': 672.72, 'end': 672.96}, {'word': ' know,', 'start': 672.96, 'end': 673.12}, {'word': ' like,', 'start': 673.12, 'end': 673.3}, {'word': \" who's\", 'start': 673.34, 'end': 673.62}, {'word': ' the', 'start': 673.62, 'end': 673.78}, {'word': ' best', 'start': 673.78, 'end': 674.08}, {'word': ' at', 'start': 674.08, 'end': 674.22}, {'word': ' doing', 'start': 674.22, 'end': 674.38}, {'word': ' AI?', 'start': 674.38, 'end': 674.64}, {'word': ' They', 'start': 674.68, 'end': 674.92}, {'word': ' started', 'start': 674.92, 'end': 675.18}, {'word': ' learning', 'start': 675.18, 'end': 675.5}, {'word': ' three,', 'start': 675.5, 'end': 675.98}, {'word': ' four', 'start': 676.04, 'end': 676.18}, {'word': ' years', 'start': 676.18, 'end': 676.4}, {'word': ' ago.', 'start': 676.4, 'end': 676.54}, {'word': ' You', 'start': 676.74, 'end': 677.1}, {'word': ' know,', 'start': 677.1, 'end': 677.24}, {'word': ' so', 'start': 677.24, 'end': 677.32}, {'word': \" that's\", 'start': 677.32, 'end': 677.6}, {'word': ' why', 'start': 677.6, 'end': 677.7}, {'word': \" it's\", 'start': 677.7, 'end': 677.84}, {'word': ' scary', 'start': 677.84, 'end': 678.02}, {'word': ' because', 'start': 678.02, 'end': 678.28}, {'word': \" you're\", 'start': 678.28, 'end': 678.56}, {'word': ' going', 'start': 678.56, 'end': 678.7}, {'word': ' out', 'start': 678.7, 'end': 678.94}, {'word': ' and', 'start': 678.94, 'end': 679.08}, {'word': \" you're\", 'start': 679.08, 'end': 679.22}, {'word': ' not', 'start': 679.22, 'end': 679.32}, {'word': ' classically', 'start': 679.32, 'end': 679.76}, {'word': ' trained.', 'start': 679.76, 'end': 680.04}, {'word': ' I', 'start': 680.04, 'end': 680.04}, {'word': ' mean,', 'start': 680.04, 'end': 680.12}, {'word': ' the', 'start': 680.12, 'end': 680.2}, {'word': ' more', 'start': 680.2, 'end': 680.32}, {'word': ' you', 'start': 680.32, 'end': 680.42}, {'word': ' go', 'start': 680.42, 'end': 680.54}, {'word': ' out,', 'start': 680.54, 'end': 680.7}, {'word': ' the', 'start': 680.76, 'end': 680.82}, {'word': ' more', 'start': 680.82, 'end': 680.98}, {'word': ' people', 'start': 680.98, 'end': 681.28}, {'word': ' you', 'start': 681.28, 'end': 681.42}, {'word': ' meet,', 'start': 681.42, 'end': 681.56}, {'word': ' the', 'start': 681.6, 'end': 681.66}, {'word': ' more', 'start': 681.66, 'end': 681.78}, {'word': ' network', 'start': 681.78, 'end': 682.04}, {'word': ' you', 'start': 682.04, 'end': 682.2}, {'word': ' have.', 'start': 682.2, 'end': 682.4}, {'word': ' And', 'start': 682.7, 'end': 683.02}, {'word': \" it's\", 'start': 683.02, 'end': 683.2}, {'word': ' also', 'start': 683.2, 'end': 683.38}, {'word': ' why', 'start': 683.38, 'end': 683.54}, {'word': ' you', 'start': 683.54, 'end': 683.68}, {'word': ' can', 'start': 683.68, 'end': 683.78}, {'word': ' see,', 'start': 683.78, 'end': 683.96}, {'word': ' like,', 'start': 684.02, 'end': 684.16}, {'word': ' the', 'start': 684.18, 'end': 684.22}, {'word': ' rise', 'start': 684.22, 'end': 684.46}, {'word': ' of,', 'start': 684.46, 'end': 684.74}, {'word': ' like,', 'start': 684.76, 'end': 684.9}, {'word': ' YouTube.', 'start': 684.9, 'end': 685.22}, {'word': ' Like,', 'start': 685.4, 'end': 685.64}, {'word': ' I', 'start': 685.7, 'end': 686.04}, {'word': ' just', 'start': 686.04, 'end': 686.38}, {'word': ' looked', 'start': 686.38, 'end': 686.58}, {'word': ' it', 'start': 686.58, 'end': 686.68}, {'word': ' up.', 'start': 686.68, 'end': 686.84}, {'word': \" It's\", 'start': 686.86, 'end': 686.98}, {'word': ' three,', 'start': 686.98, 'end': 687.26}, {'word': ' almost', 'start': 687.32, 'end': 687.6}, {'word': ' 400', 'start': 687.6, 'end': 687.88}, {'word': ',000', 'start': 687.88, 'end': 688.26}, {'word': ' people', 'start': 688.26, 'end': 688.5}, {'word': ' make', 'start': 688.5, 'end': 688.68}, {'word': ' a', 'start': 688.68, 'end': 688.8}, {'word': ' living', 'start': 688.8, 'end': 689.08}, {'word': ' off', 'start': 689.08, 'end': 689.64}, {'word': ' of', 'start': 689.64, 'end': 689.76}, {'word': ' platforms', 'start': 689.76, 'end': 690.12}, {'word': ' like', 'start': 690.12, 'end': 690.32}, {'word': ' YouTube,', 'start': 690.32, 'end': 690.66}, {'word': ' which', 'start': 690.74, 'end': 690.92}, {'word': ' is', 'start': 690.92, 'end': 691.04}, {'word': ' just', 'start': 691.04, 'end': 691.18}, {'word': ' a', 'start': 691.18, 'end': 691.28}, {'word': ' large', 'start': 691.28, 'end': 691.5}, {'word': ' network', 'start': 691.5, 'end': 691.88}, {'word': \" that's\", 'start': 691.88, 'end': 692.26}, {'word': ' deployed', 'start': 692.26, 'end': 692.58}, {'word': ' digitally.', 'start': 692.58, 'end': 692.92}, {'word': ' And', 'start': 693.28, 'end': 693.28}, {'word': ' they,', 'start': 693.28, 'end': 693.38}, {'word': ' you', 'start': 693.48, 'end': 693.9}, {'word': ' know,', 'start': 693.9, 'end': 694.02}, {'word': ' so', 'start': 694.04, 'end': 694.6}, {'word': ' because', 'start': 694.6, 'end': 695.5}, {'word': \" it's\", 'start': 695.5, 'end': 697.38}, {'word': ' just', 'start': 697.38, 'end': 697.48}, {'word': ' so', 'start': 697.48, 'end': 697.6}, {'word': ' interesting,', 'start': 697.6, 'end': 698.02}, {'word': ' like,', 'start': 698.12, 'end': 698.3}, {'word': \" there's\", 'start': 698.3, 'end': 698.48}, {'word': ' no', 'start': 698.48, 'end': 698.6}, {'word': ' education.', 'start': 698.6, 'end': 699.0}, {'word': \" Who's\", 'start': 699.24, 'end': 699.48}, {'word': ' going', 'start': 699.48, 'end': 699.6}, {'word': ' to', 'start': 699.6, 'end': 699.68}, {'word': ' be', 'start': 699.68, 'end': 699.76}, {'word': ' the', 'start': 699.76, 'end': 699.9}, {'word': ' best?', 'start': 699.9, 'end': 700.16}, {'word': ' Like,', 'start': 700.32, 'end': 700.58}, {'word': ' can', 'start': 700.62, 'end': 700.78}, {'word': ' you', 'start': 700.78, 'end': 700.94}, {'word': ' apply', 'start': 700.94, 'end': 701.22}, {'word': ' to', 'start': 701.22, 'end': 701.44}, {'word': ' a', 'start': 701.44, 'end': 701.52}, {'word': ' job', 'start': 701.52, 'end': 701.7}, {'word': ' at', 'start': 701.7, 'end': 701.82}, {'word': ' a', 'start': 701.82, 'end': 701.9}, {'word': ' company?', 'start': 701.9, 'end': 702.16}, {'word': ' You', 'start': 702.24, 'end': 702.38}, {'word': ' could', 'start': 702.38, 'end': 702.52}, {'word': ' have', 'start': 702.52, 'end': 702.64}, {'word': ' almost', 'start': 702.64, 'end': 703.02}, {'word': ' no', 'start': 703.02, 'end': 703.24}, {'word': ' experience,', 'start': 703.24, 'end': 703.6}, {'word': ' no', 'start': 703.68, 'end': 703.84}, {'word': ' education.', 'start': 703.84, 'end': 704.16}, {'word': ' But', 'start': 704.38, 'end': 704.54}, {'word': ' if', 'start': 704.54, 'end': 704.64}, {'word': ' you', 'start': 704.64, 'end': 704.76}, {'word': ' have', 'start': 704.76, 'end': 704.88}, {'word': ' three', 'start': 704.88, 'end': 705.06}, {'word': ' or', 'start': 705.06, 'end': 705.22}, {'word': ' four', 'start': 705.22, 'end': 705.36}, {'word': ' projects', 'start': 705.36, 'end': 706.3}, {'word': ' that', 'start': 706.3, 'end': 707.06}, {'word': \" you're\", 'start': 707.06, 'end': 707.16}, {'word': ' successful', 'start': 707.16, 'end': 707.54}, {'word': ' with,', 'start': 707.54, 'end': 707.86}, {'word': ' then', 'start': 707.86, 'end': 708.0}, {'word': \" you're\", 'start': 708.0, 'end': 708.12}, {'word': ' going', 'start': 708.12, 'end': 708.18}, {'word': ' to', 'start': 708.18, 'end': 708.3}, {'word': ' solve', 'start': 708.3, 'end': 708.46}, {'word': ' a', 'start': 708.46, 'end': 708.58}, {'word': ' problem', 'start': 708.58, 'end': 708.88}, {'word': ' at', 'start': 708.88, 'end': 709.02}, {'word': ' that', 'start': 709.02, 'end': 709.2}, {'word': ' company.', 'start': 709.2, 'end': 709.56}, {'word': ' Right.', 'start': 709.7, 'end': 710.02}, {'word': \" You're\", 'start': 710.04, 'end': 710.26}, {'word': ' going', 'start': 710.26, 'end': 710.36}, {'word': ' to', 'start': 710.36, 'end': 710.48}, {'word': ' get', 'start': 710.48, 'end': 710.88}, {'word': ' the', 'start': 710.88, 'end': 711.04}, {'word': ' job,', 'start': 711.04, 'end': 711.22}, {'word': ' you', 'start': 711.34, 'end': 711.52}, {'word': ' know,', 'start': 711.52, 'end': 711.66}, {'word': ' because', 'start': 711.68, 'end': 711.94}, {'word': ' it', 'start': 711.94, 'end': 712.12}, {'word': ' is', 'start': 712.12, 'end': 712.22}, {'word': ' just', 'start': 712.22, 'end': 712.38}, {'word': ' so', 'start': 712.38, 'end': 712.6}, {'word': ' new', 'start': 712.6, 'end': 712.84}, {'word': ' and', 'start': 712.84, 'end': 713.4}, {'word': \" there's\", 'start': 713.4, 'end': 713.56}, {'word': ' no', 'start': 713.56, 'end': 713.68}, {'word': ' education.', 'start': 713.68, 'end': 714.06}, {'word': ' But', 'start': 714.36, 'end': 714.64}, {'word': ' why', 'start': 714.64, 'end': 715.08}, {'word': ' the', 'start': 715.08, 'end': 715.46}, {'word': ' scary', 'start': 715.46, 'end': 715.84}, {'word': ' to', 'start': 715.84, 'end': 716.14}, {'word': ' go', 'start': 716.14, 'end': 716.32}, {'word': ' network', 'start': 716.32, 'end': 716.94}, {'word': ' to', 'start': 716.94, 'end': 717.12}, {'word': ' find', 'start': 717.12, 'end': 717.38}, {'word': ' these', 'start': 717.38, 'end': 717.52}, {'word': ' connections', 'start': 717.52, 'end': 717.88}, {'word': ' and', 'start': 717.88, 'end': 718.22}, {'word': ' open', 'start': 718.22, 'end': 718.46}, {'word': ' those', 'start': 718.46, 'end': 718.66}, {'word': ' doors?', 'start': 718.66, 'end': 718.9}, {'word': ' Well,', 'start': 719.3000000000001, 'end': 719.7}, {'word': ' like,', 'start': 719.8, 'end': 720.16}, {'word': ' you', 'start': 720.2, 'end': 720.66}, {'word': ' expect', 'start': 720.66, 'end': 721.26}, {'word': ' one', 'start': 721.26, 'end': 721.68}, {'word': ' thing', 'start': 721.68, 'end': 721.96}, {'word': ' and', 'start': 721.96, 'end': 722.16}, {'word': ' then', 'start': 722.16, 'end': 722.28}, {'word': ' you', 'start': 722.28, 'end': 722.38}, {'word': ' get', 'start': 722.38, 'end': 722.54}, {'word': ' another,', 'start': 722.54, 'end': 722.74}, {'word': ' you', 'start': 722.88, 'end': 722.98}, {'word': ' know.', 'start': 722.98, 'end': 723.1}, {'word': ' So', 'start': 723.12, 'end': 723.28}, {'word': \" it's\", 'start': 723.28, 'end': 723.46}, {'word': ' like', 'start': 723.46, 'end': 723.52}, {'word': ' everybody', 'start': 723.52, 'end': 723.92}, {'word': ' is', 'start': 723.92, 'end': 724.28}, {'word': ' living', 'start': 724.28, 'end': 724.44}, {'word': ' in', 'start': 724.44, 'end': 724.62}, {'word': ' a', 'start': 724.62, 'end': 724.66}, {'word': ' world', 'start': 724.66, 'end': 724.82}, {'word': ' where', 'start': 724.82, 'end': 724.98}, {'word': \" it's\", 'start': 724.98, 'end': 725.06}, {'word': ' like', 'start': 725.06, 'end': 725.16}, {'word': ' we', 'start': 725.16, 'end': 725.38}, {'word': \" don't\", 'start': 725.38, 'end': 725.52}, {'word': ' really', 'start': 725.52, 'end': 725.64}, {'word': ' know', 'start': 725.64, 'end': 725.82}, {'word': ' how', 'start': 725.82, 'end': 725.98}, {'word': ' to', 'start': 725.98, 'end': 726.04}, {'word': ' use', 'start': 726.04, 'end': 726.2}, {'word': ' AI.', 'start': 726.2, 'end': 726.38}, {'word': ' Right.', 'start': 726.58, 'end': 726.86}, {'word': ' See,', 'start': 727.04, 'end': 727.44}, {'word': ' the', 'start': 727.9, 'end': 728.06}, {'word': ' personality,', 'start': 728.06, 'end': 728.54}, {'word': ' I', 'start': 728.9, 'end': 729.2}, {'word': ' came', 'start': 729.2, 'end': 729.44}, {'word': ' from', 'start': 729.44, 'end': 729.7}, {'word': ' a,', 'start': 729.7, 'end': 729.86}, {'word': ' you', 'start': 729.86, 'end': 730.0}, {'word': ' know,', 'start': 730.0, 'end': 730.1}, {'word': ' very', 'start': 730.16, 'end': 730.66}, {'word': ' shy,', 'start': 730.66, 'end': 731.0}, {'word': ' you', 'start': 731.14, 'end': 731.4}, {'word': ' know,', 'start': 731.4, 'end': 731.6}, {'word': ' I', 'start': 731.62, 'end': 731.84}, {'word': ' opened', 'start': 731.84, 'end': 732.4}, {'word': ' up.', 'start': 732.4, 'end': 732.66}, {'word': ' I', 'start': 732.68, 'end': 732.76}, {'word': ' learned', 'start': 732.76, 'end': 733.06}, {'word': ' in', 'start': 733.06, 'end': 733.3}, {'word': ' the', 'start': 733.3, 'end': 733.44}, {'word': ' U', 'start': 733.44, 'end': 733.44}, {'word': '.S.', 'start': 733.44, 'end': 733.72}, {'word': ' being', 'start': 733.72, 'end': 734.06}, {'word': ' staying', 'start': 734.06, 'end': 734.36}, {'word': ' this', 'start': 734.36, 'end': 734.7}, {'word': ' long', 'start': 734.7, 'end': 735.06}, {'word': ' in', 'start': 735.06, 'end': 735.84}, {'word': ' Austin.', 'start': 735.84, 'end': 736.2}, {'word': ' I', 'start': 736.4, 'end': 736.66}, {'word': ' never', 'start': 736.66, 'end': 736.9}, {'word': ' used', 'start': 736.9, 'end': 737.18}, {'word': ' to', 'start': 737.18, 'end': 737.38}, {'word': ' talk', 'start': 737.38, 'end': 737.56}, {'word': ' up.', 'start': 737.56, 'end': 737.76}, {'word': ' You', 'start': 738.5600000000001, 'end': 738.96}, {'word': ' have', 'start': 738.96, 'end': 739.34}, {'word': ' to', 'start': 739.34, 'end': 739.58}, {'word': ' get', 'start': 739.58, 'end': 739.86}, {'word': ' up.', 'start': 739.86, 'end': 740.02}, {'word': ' You', 'start': 740.02, 'end': 740.1}, {'word': ' have', 'start': 740.1, 'end': 740.1}, {'word': ' to', 'start': 740.1, 'end': 740.1}, {'word': ' get', 'start': 740.1, 'end': 740.1}, {'word': ' out', 'start': 740.1, 'end': 740.1}, {'word': ' of', 'start': 740.1, 'end': 740.36}, {'word': ' your', 'start': 740.36, 'end': 740.5}, {'word': ' comfort', 'start': 740.5, 'end': 740.88}, {'word': ' zone.', 'start': 740.88, 'end': 741.22}, {'word': ' Right.', 'start': 741.5, 'end': 741.64}, {'word': ' The', 'start': 742.16, 'end': 742.56}, {'word': ' only', 'start': 742.56, 'end': 742.84}, {'word': ' way', 'start': 742.84, 'end': 743.18}, {'word': ' to', 'start': 743.18, 'end': 743.38}, {'word': ' get', 'start': 743.38, 'end': 743.58}, {'word': ' off', 'start': 743.58, 'end': 743.8}, {'word': ' the', 'start': 743.8, 'end': 743.86}, {'word': ' continent,', 'start': 743.86, 'end': 744.1}, {'word': ' two', 'start': 744.32, 'end': 744.48}, {'word': ' things', 'start': 744.48, 'end': 744.76}, {'word': ' you', 'start': 744.76, 'end': 744.92}, {'word': ' have', 'start': 744.92, 'end': 745.02}, {'word': ' to', 'start': 745.02, 'end': 745.16}, {'word': ' do.', 'start': 745.16, 'end': 745.26}, {'word': ' Either', 'start': 745.38, 'end': 745.64}, {'word': ' you', 'start': 745.64, 'end': 745.9}, {'word': ' go', 'start': 745.9, 'end': 746.16}, {'word': ' all', 'start': 746.16, 'end': 746.4}, {'word': ' in', 'start': 746.4, 'end': 746.62}, {'word': ' or', 'start': 746.62, 'end': 746.8}, {'word': ' you', 'start': 746.8, 'end': 746.92}, {'word': ' have', 'start': 746.92, 'end': 747.1}, {'word': ' one', 'start': 747.1, 'end': 747.34}, {'word': ' glass', 'start': 747.34, 'end': 748.46}, {'word': ' of', 'start': 748.46, 'end': 748.62}, {'word': ' beer', 'start': 748.62, 'end': 748.84}, {'word': ' and', 'start': 748.84, 'end': 749.52}, {'word': ' then', 'start': 749.52, 'end': 749.72}, {'word': ' get', 'start': 749.72, 'end': 749.94}, {'word': ' a', 'start': 749.94, 'end': 750.14}, {'word': ' little', 'start': 750.14, 'end': 750.3}, {'word': ' bit', 'start': 750.3, 'end': 750.46}, {'word': ' out', 'start': 750.46, 'end': 750.66}, {'word': ' of', 'start': 750.66, 'end': 750.84}, {'word': ' shyness', 'start': 750.84, 'end': 751.3}, {'word': ' and', 'start': 751.3, 'end': 752.5}, {'word': ' then', 'start': 752.5, 'end': 753.46}, {'word': ' go', 'start': 753.46, 'end': 753.7}, {'word': ' and', 'start': 753.7, 'end': 753.9}, {'word': ' talk.', 'start': 753.9, 'end': 754.14}, {'word': ' You', 'start': 754.32, 'end': 754.66}, {'word': ' have', 'start': 754.66, 'end': 754.82}, {'word': ' to', 'start': 754.82, 'end': 754.96}, {'word': ' start', 'start': 754.96, 'end': 755.16}, {'word': ' with', 'start': 755.16, 'end': 755.34}, {'word': ' strangers.', 'start': 755.34, 'end': 755.76}, {'word': ' And', 'start': 755.94, 'end': 756.04}, {'word': \" it's\", 'start': 756.04, 'end': 756.24}, {'word': ' a', 'start': 756.24, 'end': 756.26}, {'word': ' learned', 'start': 756.26, 'end': 756.56}, {'word': ' skill.', 'start': 756.56, 'end': 756.94}, {'word': ' If', 'start': 757.04, 'end': 757.32}, {'word': ' you', 'start': 757.32, 'end': 758.1}, {'word': ' say,', 'start': 758.1, 'end': 758.32}, {'word': \" I'm\", 'start': 758.42, 'end': 758.8}, {'word': ' going', 'start': 758.8, 'end': 758.92}, {'word': ' to', 'start': 758.92, 'end': 759.08}, {'word': ' go', 'start': 759.08, 'end': 759.28}, {'word': ' to', 'start': 759.28, 'end': 759.64}, {'word': ' two', 'start': 759.64, 'end': 759.88}, {'word': ' networking', 'start': 759.88, 'end': 760.2}, {'word': ' events', 'start': 760.2, 'end': 760.58}, {'word': ' a', 'start': 760.58, 'end': 761.14}, {'word': ' week', 'start': 761.14, 'end': 761.42}, {'word': ' for', 'start': 761.42, 'end': 761.68}, {'word': ' the', 'start': 761.68, 'end': 761.8}, {'word': ' next', 'start': 761.8, 'end': 761.98}, {'word': ' six', 'start': 761.98, 'end': 762.26}, {'word': ' months,', 'start': 762.26, 'end': 762.64}, {'word': ' at', 'start': 762.84, 'end': 763.18}, {'word': ' the', 'start': 763.18, 'end': 763.32}, {'word': ' end', 'start': 763.32, 'end': 763.48}, {'word': ' of', 'start': 763.48, 'end': 763.56}, {'word': ' six', 'start': 763.56, 'end': 763.68}, {'word': ' months,', 'start': 763.68, 'end': 763.98}, {'word': \" you're\", 'start': 764.06, 'end': 764.18}, {'word': ' going', 'start': 764.18, 'end': 764.28}, {'word': ' to', 'start': 764.28, 'end': 764.34}, {'word': ' be', 'start': 764.34, 'end': 764.4}, {'word': ' like,', 'start': 764.4, 'end': 764.52}, {'word': ' I', 'start': 764.62, 'end': 764.74}, {'word': ' can', 'start': 764.74, 'end': 765.12}, {'word': ' talk', 'start': 765.12, 'end': 765.32}, {'word': ' to', 'start': 765.32, 'end': 765.7}, {'word': ' anybody.', 'start': 765.7, 'end': 765.9}, {'word': ' I', 'start': 766.1, 'end': 766.16}, {'word': ' can', 'start': 766.16, 'end': 766.24}, {'word': ' go', 'start': 766.24, 'end': 766.36}, {'word': ' do', 'start': 766.36, 'end': 766.48}, {'word': ' this.', 'start': 766.48, 'end': 766.72}, {'word': ' I', 'start': 766.76, 'end': 766.94}, {'word': ' just', 'start': 766.94, 'end': 767.18}, {'word': ' go', 'start': 767.18, 'end': 767.46}, {'word': ' and', 'start': 767.46, 'end': 767.66}, {'word': ' break', 'start': 767.66, 'end': 767.94}, {'word': ' into', 'start': 767.94, 'end': 768.18}, {'word': ' some', 'start': 768.18, 'end': 768.44}, {'word': ' group', 'start': 768.44, 'end': 768.78}, {'word': ' and', 'start': 768.78, 'end': 769.12}, {'word': ' stand', 'start': 769.12, 'end': 769.38}, {'word': ' there.', 'start': 769.38, 'end': 769.68}, {'word': ' Yeah.', 'start': 769.68, 'end': 769.94}, {'word': ' Well,', 'start': 770.04, 'end': 770.46}, {'word': ' and', 'start': 770.56, 'end': 770.72}, {'word': \" don't\", 'start': 770.72, 'end': 771.18}, {'word': ' you', 'start': 771.18, 'end': 771.3}, {'word': \" don't\", 'start': 771.3, 'end': 771.62}, {'word': ' need', 'start': 771.62, 'end': 771.7}, {'word': ' to', 'start': 771.7, 'end': 771.84}, {'word': ' do', 'start': 771.84, 'end': 771.94}, {'word': ' anything.', 'start': 771.94, 'end': 772.14}, {'word': ' What', 'start': 772.4, 'end': 772.62}, {'word': ' I', 'start': 772.62, 'end': 772.7}, {'word': ' say', 'start': 772.7, 'end': 772.86}, {'word': ' is', 'start': 772.86, 'end': 773.32}, {'word': ' like', 'start': 773.32, 'end': 774.5}, {'word': ' back', 'start': 774.5, 'end': 774.8}, {'word': ' to', 'start': 774.8, 'end': 774.96}, {'word': ' your', 'start': 774.96, 'end': 775.02}, {'word': ' generation,', 'start': 775.02, 'end': 775.24}, {'word': ' a', 'start': 775.26, 'end': 775.5}, {'word': ' lot', 'start': 775.5, 'end': 775.56}, {'word': ' of', 'start': 775.56, 'end': 775.62}, {'word': ' people', 'start': 775.62, 'end': 775.78}, {'word': ' grew', 'start': 775.78, 'end': 775.92}, {'word': ' up', 'start': 775.92, 'end': 776.08}, {'word': ' with', 'start': 776.08, 'end': 776.18}, {'word': ' everything', 'start': 776.18, 'end': 776.46}, {'word': ' was', 'start': 776.46, 'end': 776.62}, {'word': ' digital.', 'start': 776.62, 'end': 776.92}, {'word': ' You', 'start': 776.98, 'end': 777.08}, {'word': ' talk', 'start': 777.08, 'end': 777.24}, {'word': ' to', 'start': 777.24, 'end': 777.46}, {'word': ' your', 'start': 777.46, 'end': 777.56}, {'word': ' friends', 'start': 777.56, 'end': 777.84}, {'word': ' via', 'start': 777.84, 'end': 778.04}, {'word': ' text,', 'start': 778.04, 'end': 778.58}, {'word': ' you', 'start': 778.78, 'end': 778.96}, {'word': ' know,', 'start': 778.96, 'end': 779.06}, {'word': ' you', 'start': 779.1, 'end': 779.18}, {'word': ' hang', 'start': 779.18, 'end': 779.32}, {'word': ' out', 'start': 779.32, 'end': 779.62}, {'word': ' and,', 'start': 779.62, 'end': 779.92}, {'word': ' you', 'start': 779.92, 'end': 780.58}, {'word': ' know,', 'start': 780.58, 'end': 780.84}, {'word': ' and', 'start': 780.84, 'end': 781.14}, {'word': ' chat', 'start': 781.14, 'end': 781.52}, {'word': ' via', 'start': 781.52, 'end': 781.82}, {'word': ' the', 'start': 781.82, 'end': 782.04}, {'word': ' screen.', 'start': 782.04, 'end': 782.66}, {'word': ' And', 'start': 783.04, 'end': 783.46}, {'word': \" here's\", 'start': 783.46, 'end': 783.7}, {'word': ' what', 'start': 783.7, 'end': 783.76}, {'word': ' I', 'start': 783.76, 'end': 783.86}, {'word': ' tell', 'start': 783.86, 'end': 783.96}, {'word': ' them.', 'start': 783.96, 'end': 784.08}, {'word': ' We', 'start': 784.14, 'end': 784.24}, {'word': ' have', 'start': 784.24, 'end': 784.42}, {'word': ' to', 'start': 784.42, 'end': 784.52}, {'word': ' remember,', 'start': 784.52, 'end': 784.78}, {'word': ' though,', 'start': 784.88, 'end': 785.08}, {'word': ' is', 'start': 785.14, 'end': 785.36}, {'word': ' that', 'start': 785.36, 'end': 785.5}, {'word': ' all', 'start': 785.5, 'end': 785.74}, {'word': ' opportunities', 'start': 785.74, 'end': 786.2}, {'word': ' in', 'start': 786.2, 'end': 786.58}, {'word': ' life', 'start': 786.58, 'end': 786.82}, {'word': ' and', 'start': 786.82, 'end': 786.96}, {'word': ' this', 'start': 786.96, 'end': 787.08}, {'word': ' will', 'start': 787.08, 'end': 787.26}, {'word': ' not', 'start': 787.26, 'end': 787.52}, {'word': ' change', 'start': 787.52, 'end': 787.9}, {'word': ' no', 'start': 787.9, 'end': 788.26}, {'word': ' matter', 'start': 788.26, 'end': 788.44}, {'word': ' what', 'start': 788.44, 'end': 788.66}, {'word': ' the', 'start': 788.66, 'end': 788.9}, {'word': ' technology', 'start': 788.9, 'end': 789.2}, {'word': ' does.', 'start': 789.2, 'end': 789.64}, {'word': ' All', 'start': 789.86, 'end': 790.28}, {'word': ' opportunities', 'start': 790.28, 'end': 790.68}, {'word': ' in', 'start': 790.68, 'end': 790.9}, {'word': ' life', 'start': 790.9, 'end': 791.1}, {'word': ' come', 'start': 791.1, 'end': 791.26}, {'word': ' from', 'start': 791.26, 'end': 791.48}, {'word': ' people.', 'start': 791.48, 'end': 791.86}, {'word': ' Yeah.', 'start': 791.96, 'end': 792.36}, {'word': ' And', 'start': 792.44, 'end': 792.78}, {'word': ' so', 'start': 792.78, 'end': 793.16}, {'word': ' earlier', 'start': 793.16, 'end': 793.94}, {'word': ' on,', 'start': 793.94, 'end': 794.28}, {'word': ' if', 'start': 794.54, 'end': 794.8}, {'word': ' you', 'start': 794.8, 'end': 794.94}, {'word': ' can', 'start': 794.94, 'end': 795.16}, {'word': ' collect', 'start': 795.16, 'end': 795.58}, {'word': ' people', 'start': 795.58, 'end': 795.96}, {'word': ' in', 'start': 795.96, 'end': 796.22}, {'word': ' a', 'start': 796.22, 'end': 796.3}, {'word': ' good', 'start': 796.3, 'end': 796.44}, {'word': ' way,', 'start': 796.44, 'end': 796.66}, {'word': ' if', 'start': 796.74, 'end': 797.2}, {'word': ' you', 'start': 797.2, 'end': 797.3}, {'word': ' can', 'start': 797.3, 'end': 797.4}, {'word': ' collect', 'start': 797.4, 'end': 797.68}, {'word': ' relationships,', 'start': 797.68, 'end': 798.28}, {'word': ' not', 'start': 798.36, 'end': 798.78}, {'word': ' like', 'start': 798.78, 'end': 799.02}, {'word': ' Courtney', 'start': 799.02, 'end': 799.32}, {'word': ' and', 'start': 799.32, 'end': 799.48}, {'word': ' I,', 'start': 799.48, 'end': 799.58}, {'word': ' not', 'start': 799.68, 'end': 799.76}, {'word': ' like,', 'start': 799.76, 'end': 799.9}, {'word': ' ha', 'start': 799.9, 'end': 799.96}, {'word': ' ha', 'start': 799.96, 'end': 800.16}, {'word': ' ha,', 'start': 800.16, 'end': 800.26}, {'word': ' what', 'start': 800.32, 'end': 800.68}, {'word': ' can', 'start': 800.68, 'end': 800.82}, {'word': ' they', 'start': 800.82, 'end': 800.96}, {'word': ' do', 'start': 800.96, 'end': 801.1}, {'word': ' for', 'start': 801.1, 'end': 801.28}, {'word': ' me?', 'start': 801.28, 'end': 801.46}, {'word': ' But', 'start': 801.62, 'end': 801.98}, {'word': ' if', 'start': 801.98, 'end': 802.1}, {'word': ' you', 'start': 802.1, 'end': 802.22}, {'word': ' can', 'start': 802.22, 'end': 802.36}, {'word': ' get', 'start': 802.36, 'end': 802.56}, {'word': ' to', 'start': 802.56, 'end': 802.68}, {'word': ' know', 'start': 802.68, 'end': 802.82}, {'word': ' people', 'start': 802.82, 'end': 803.14}, {'word': ' and', 'start': 803.14, 'end': 803.42}, {'word': ' build', 'start': 803.42, 'end': 803.64}, {'word': ' relationships', 'start': 803.64, 'end': 804.16}, {'word': ' with', 'start': 804.16, 'end': 804.54}, {'word': ' people', 'start': 804.54, 'end': 804.82}, {'word': ' and', 'start': 804.82, 'end': 805.28}, {'word': ' show', 'start': 805.28, 'end': 805.68}, {'word': ' them', 'start': 805.68, 'end': 805.98}, {'word': ' through', 'start': 805.98, 'end': 806.3}, {'word': ' actions', 'start': 806.3, 'end': 806.78}, {'word': ' over', 'start': 806.78, 'end': 807.18}, {'word': ' five', 'start': 807.18, 'end': 807.62}, {'word': ' years,', 'start': 807.62, 'end': 807.92}, {'word': ' 10', 'start': 808.04, 'end': 808.14}, {'word': ' years,', 'start': 808.14, 'end': 808.42}, {'word': ' etc.,', 'start': 808.5, 'end': 809.06}, {'word': ' then', 'start': 809.06, 'end': 809.64}, {'word': \" you're\", 'start': 809.64, 'end': 810.1}, {'word': ' a', 'start': 810.1, 'end': 810.16}, {'word': ' doer.', 'start': 810.16, 'end': 810.56}, {'word': ' You', 'start': 810.56, 'end': 810.66}, {'word': ' care', 'start': 810.66, 'end': 810.84}, {'word': ' about', 'start': 810.84, 'end': 811.06}, {'word': ' the', 'start': 811.06, 'end': 811.22}, {'word': ' community.', 'start': 811.22, 'end': 811.58}, {'word': \" You're\", 'start': 812.18, 'end': 812.54}, {'word': ' someone', 'start': 812.54, 'end': 812.74}, {'word': ' who', 'start': 812.74, 'end': 812.88}, {'word': ' helps', 'start': 812.88, 'end': 813.08}, {'word': ' other', 'start': 813.08, 'end': 813.24}, {'word': ' people.', 'start': 813.24, 'end': 813.54}, {'word': \" You're\", 'start': 813.62, 'end': 813.72}, {'word': ' not', 'start': 813.72, 'end': 813.86}, {'word': ' just', 'start': 813.86, 'end': 814.1}, {'word': ' out', 'start': 814.1, 'end': 814.28}, {'word': ' for', 'start': 814.28, 'end': 814.42}, {'word': ' yourself.', 'start': 814.42, 'end': 814.6}, {'word': \" What's\", 'start': 814.9, 'end': 815.26}, {'word': ' going', 'start': 815.26, 'end': 815.34}, {'word': ' to', 'start': 815.34, 'end': 815.44}, {'word': ' happen', 'start': 815.44, 'end': 815.68}, {'word': ' is,', 'start': 815.68, 'end': 815.98}, {'word': ' is', 'start': 816.0, 'end': 816.14}, {'word': ' opportunities', 'start': 816.14, 'end': 816.84}, {'word': ' are', 'start': 816.84, 'end': 817.16}, {'word': ' going', 'start': 817.16, 'end': 817.26}, {'word': ' to', 'start': 817.26, 'end': 817.3}, {'word': ' come', 'start': 817.3, 'end': 817.44}, {'word': ' up.', 'start': 817.44, 'end': 817.58}, {'word': ' People', 'start': 817.62, 'end': 817.8}, {'word': ' are', 'start': 817.8, 'end': 817.96}, {'word': ' going', 'start': 817.96, 'end': 818.06}, {'word': ' to', 'start': 818.06, 'end': 818.1}, {'word': ' pull', 'start': 818.1, 'end': 818.34}, {'word': ' you', 'start': 818.34, 'end': 818.54}, {'word': ' in.', 'start': 818.54, 'end': 818.88}, {'word': ' But', 'start': 819.22, 'end': 819.58}, {'word': ' if', 'start': 819.58, 'end': 819.7}, {'word': ' you', 'start': 819.7, 'end': 819.8}, {'word': \" don't\", 'start': 819.8, 'end': 820.18}, {'word': ' do', 'start': 820.18, 'end': 820.26}, {'word': ' those', 'start': 820.26, 'end': 820.44}, {'word': ' things,', 'start': 820.44, 'end': 820.66}, {'word': ' they', 'start': 820.72, 'end': 820.82}, {'word': \" don't\", 'start': 820.82, 'end': 821.04}, {'word': ' know', 'start': 821.04, 'end': 821.08}, {'word': ' who', 'start': 821.08, 'end': 821.24}, {'word': ' you', 'start': 821.24, 'end': 821.36}, {'word': ' are.', 'start': 821.36, 'end': 821.66}, {'word': ' Even', 'start': 822.26, 'end': 822.62}, {'word': ' if', 'start': 822.62, 'end': 822.86}, {'word': ' they', 'start': 822.86, 'end': 822.96}, {'word': ' would', 'start': 822.96, 'end': 823.12}, {'word': ' want', 'start': 823.12, 'end': 823.36}, {'word': ' to', 'start': 823.36, 'end': 823.52}, {'word': ' pull', 'start': 823.52, 'end': 823.68}, {'word': ' you', 'start': 823.68, 'end': 823.82}, {'word': ' in,', 'start': 823.82, 'end': 823.92}, {'word': ' they', 'start': 823.92, 'end': 824.02}, {'word': \" don't\", 'start': 824.02, 'end': 824.18}, {'word': ' know', 'start': 824.18, 'end': 824.32}, {'word': ' you.', 'start': 824.32, 'end': 824.48}, {'word': ' They', 'start': 824.54, 'end': 824.64}, {'word': \" can't\", 'start': 824.64, 'end': 824.86}, {'word': ' do', 'start': 824.86, 'end': 825.0}, {'word': ' anything.', 'start': 825.0, 'end': 825.22}, {'word': ' So', 'start': 825.32, 'end': 825.42}, {'word': ' I', 'start': 825.42, 'end': 825.54}, {'word': ' try', 'start': 825.54, 'end': 825.82}, {'word': ' to', 'start': 825.82, 'end': 825.96}, {'word': ' teach', 'start': 825.96, 'end': 826.22}, {'word': ' younger', 'start': 826.22, 'end': 826.44}, {'word': ' people,', 'start': 826.44, 'end': 826.78}, {'word': ' you', 'start': 826.98, 'end': 827.58}, {'word': ' know,', 'start': 827.58, 'end': 827.76}, {'word': ' look', 'start': 827.76, 'end': 828.0}, {'word': ' at', 'start': 828.0, 'end': 828.12}, {'word': ' everything', 'start': 828.12, 'end': 828.38}, {'word': ' good', 'start': 828.38, 'end': 828.72}, {'word': \" that's\", 'start': 828.72, 'end': 828.96}, {'word': ' happened', 'start': 828.96, 'end': 829.2}, {'word': ' in', 'start': 829.2, 'end': 829.34}, {'word': ' your', 'start': 829.34, 'end': 829.42}, {'word': ' life.', 'start': 829.42, 'end': 829.66}, {'word': \" It's\", 'start': 829.66, 'end': 830.26}, {'word': ' happened', 'start': 830.26, 'end': 830.48}, {'word': ' because', 'start': 830.48, 'end': 830.7}, {'word': ' of', 'start': 830.7, 'end': 830.9}, {'word': ' a', 'start': 830.9, 'end': 831.02}, {'word': ' person.', 'start': 831.02, 'end': 831.42}, {'word': ' You', 'start': 831.84, 'end': 832.24}, {'word': ' know,', 'start': 832.24, 'end': 832.4}, {'word': ' somehow', 'start': 832.4, 'end': 832.84}, {'word': ' you', 'start': 832.84, 'end': 833.16}, {'word': ' can', 'start': 833.16, 'end': 833.3}, {'word': ' trace', 'start': 833.3, 'end': 833.54}, {'word': ' it', 'start': 833.54, 'end': 833.66}, {'word': ' back', 'start': 833.66, 'end': 833.84}, {'word': ' to', 'start': 833.84, 'end': 834.0}, {'word': ' people.', 'start': 834.0, 'end': 834.26}, {'word': ' And', 'start': 834.42, 'end': 834.82}, {'word': ' the', 'start': 834.82, 'end': 834.96}, {'word': ' more', 'start': 834.96, 'end': 835.2}, {'word': ' you', 'start': 835.2, 'end': 835.4}, {'word': ' can', 'start': 835.4, 'end': 835.6}, {'word': ' connect', 'start': 835.6, 'end': 835.96}, {'word': ' in', 'start': 835.96, 'end': 836.22}, {'word': ' a', 'start': 836.22, 'end': 836.3}, {'word': ' community', 'start': 836.3, 'end': 836.56}, {'word': ' like', 'start': 836.56, 'end': 836.9}, {'word': ' Austin,', 'start': 836.9, 'end': 837.26}, {'word': ' and', 'start': 837.48, 'end': 837.96}, {'word': \" that's\", 'start': 837.96, 'end': 838.22}, {'word': ' why', 'start': 838.22, 'end': 838.38}, {'word': ' we', 'start': 838.38, 'end': 838.94}, {'word': ' have', 'start': 838.94, 'end': 839.08}, {'word': ' to', 'start': 839.08, 'end': 839.22}, {'word': ' work', 'start': 839.22, 'end': 839.4}, {'word': ' hard', 'start': 839.4, 'end': 839.64}, {'word': ' as', 'start': 839.64, 'end': 839.8}, {'word': ' a', 'start': 839.8, 'end': 839.88}, {'word': ' community', 'start': 839.88, 'end': 840.12}, {'word': ' to', 'start': 840.12, 'end': 840.4}, {'word': ' keep', 'start': 840.4, 'end': 840.68}, {'word': ' that', 'start': 840.68, 'end': 841.02}, {'word': ' vibe', 'start': 841.02, 'end': 841.88}, {'word': ' of', 'start': 841.88, 'end': 842.32}, {'word': ' connectivity,', 'start': 842.32, 'end': 843.0}, {'word': ' of', 'start': 843.24, 'end': 843.64}, {'word': ' community.', 'start': 843.64, 'end': 843.98}, {'word': ' Because', 'start': 844.58, 'end': 844.98}, {'word': ' if', 'start': 844.98, 'end': 845.38}, {'word': ' you', 'start': 845.38, 'end': 845.58}, {'word': ' can', 'start': 845.58, 'end': 845.7}, {'word': ' still', 'start': 845.7, 'end': 846.02}, {'word': ' reach', 'start': 846.02, 'end': 846.26}, {'word': ' people', 'start': 846.26, 'end': 846.56}, {'word': ' and', 'start': 846.56, 'end': 846.8}, {'word': ' share', 'start': 846.8, 'end': 847.02}, {'word': ' with', 'start': 847.02, 'end': 847.18}, {'word': ' people,', 'start': 847.18, 'end': 847.48}, {'word': ' you', 'start': 847.88, 'end': 848.04}, {'word': ' know,', 'start': 848.04, 'end': 848.18}, {'word': ' not', 'start': 848.2, 'end': 848.76}, {'word': \" everybody's\", 'start': 848.76, 'end': 849.14}, {'word': ' going', 'start': 849.14, 'end': 849.22}, {'word': ' to', 'start': 849.22, 'end': 849.3}, {'word': ' like', 'start': 849.3, 'end': 849.44}, {'word': ' you', 'start': 849.44, 'end': 849.6}, {'word': ' and', 'start': 849.6, 'end': 849.7}, {'word': \" that's\", 'start': 849.7, 'end': 849.88}, {'word': ' fine.', 'start': 849.88, 'end': 850.04}, {'word': ' But', 'start': 850.18, 'end': 850.3}, {'word': ' if', 'start': 850.3, 'end': 850.38}, {'word': ' you', 'start': 850.38, 'end': 850.44}, {'word': ' just', 'start': 850.44, 'end': 850.6}, {'word': ' keep', 'start': 850.6, 'end': 850.76}, {'word': ' trying,', 'start': 850.76, 'end': 851.14}, {'word': ' and', 'start': 851.28, 'end': 851.68}, {'word': ' when', 'start': 851.68, 'end': 852.24}, {'word': ' people', 'start': 852.24, 'end': 852.48}, {'word': ' say,', 'start': 852.48, 'end': 852.66}, {'word': ' oh,', 'start': 852.7, 'end': 852.8}, {'word': ' I', 'start': 852.82, 'end': 852.88}, {'word': \" don't\", 'start': 852.88, 'end': 853.02}, {'word': ' like', 'start': 853.02, 'end': 853.1}, {'word': ' to', 'start': 853.1, 'end': 853.26}, {'word': ' go', 'start': 853.26, 'end': 853.32}, {'word': ' to', 'start': 853.32, 'end': 853.4}, {'word': ' networking', 'start': 853.4, 'end': 853.64}, {'word': ' events', 'start': 853.64, 'end': 853.96}, {'word': ' because', 'start': 853.96, 'end': 854.2}, {'word': ' I', 'start': 854.2, 'end': 854.68}, {'word': ' go', 'start': 854.68, 'end': 854.86}, {'word': ' once', 'start': 854.86, 'end': 855.42}, {'word': ' and', 'start': 855.42, 'end': 855.72}, {'word': ' nothing', 'start': 855.72, 'end': 855.86}, {'word': ' happens.', 'start': 855.86, 'end': 856.3}, {'word': ' Well,', 'start': 856.36, 'end': 856.64}, {'word': ' no,', 'start': 856.68, 'end': 856.74}, {'word': \" you've\", 'start': 856.78, 'end': 856.92}, {'word': ' got', 'start': 856.92, 'end': 857.04}, {'word': ' to', 'start': 857.04, 'end': 857.18}, {'word': ' go', 'start': 857.18, 'end': 857.3}, {'word': ' to', 'start': 857.3, 'end': 857.44}, {'word': ' that', 'start': 857.44, 'end': 857.54}, {'word': ' same', 'start': 857.54, 'end': 857.82}, {'word': ' organization', 'start': 857.82, 'end': 858.36}, {'word': ' like', 'start': 858.36, 'end': 858.82}, {'word': ' for', 'start': 858.82, 'end': 858.96}, {'word': ' a', 'start': 858.96, 'end': 859.1}, {'word': ' year.', 'start': 859.1, 'end': 859.38}, {'word': ' Not', 'start': 859.82, 'end': 860.22}, {'word': ' before', 'start': 860.22, 'end': 860.54}, {'word': \" you're\", 'start': 860.54, 'end': 860.74}, {'word': ' going', 'start': 860.74, 'end': 860.84}, {'word': ' to', 'start': 860.84, 'end': 860.94}, {'word': ' get', 'start': 860.94, 'end': 861.06}, {'word': ' benefit,', 'start': 861.06, 'end': 861.38}, {'word': ' but', 'start': 861.5, 'end': 861.6}, {'word': ' before', 'start': 861.6, 'end': 861.8}, {'word': ' people', 'start': 861.8, 'end': 862.02}, {'word': ' even', 'start': 862.02, 'end': 862.2}, {'word': ' notice', 'start': 862.2, 'end': 862.6}, {'word': ' that', 'start': 862.6, 'end': 862.8}, {'word': \" you're\", 'start': 862.8, 'end': 862.92}, {'word': ' there.', 'start': 862.92, 'end': 863.12}, {'word': \" You've\", 'start': 863.32, 'end': 863.72}, {'word': ' got', 'start': 863.72, 'end': 863.82}, {'word': ' to', 'start': 863.82, 'end': 863.82}, {'word': ' keep', 'start': 863.82, 'end': 863.96}, {'word': ' showing', 'start': 863.96, 'end': 864.16}, {'word': ' up.', 'start': 864.16, 'end': 864.34}, {'word': \" You've\", 'start': 864.34, 'end': 864.48}, {'word': ' got', 'start': 864.48, 'end': 864.56}, {'word': ' to', 'start': 864.56, 'end': 864.56}, {'word': ' keep', 'start': 864.56, 'end': 864.7}, {'word': ' showing', 'start': 864.7, 'end': 864.92}, {'word': ' up.', 'start': 864.92, 'end': 865.12}, {'word': ' And', 'start': 865.18, 'end': 865.24}, {'word': ' after', 'start': 865.24, 'end': 865.42}, {'word': ' a', 'start': 865.42, 'end': 865.5}, {'word': ' while,', 'start': 865.5, 'end': 865.68}, {'word': \" they're\", 'start': 865.74, 'end': 865.82}, {'word': ' like,', 'start': 865.82, 'end': 865.92}, {'word': ' I', 'start': 866.0, 'end': 866.14}, {'word': ' see', 'start': 866.14, 'end': 866.46}, {'word': ' you', 'start': 866.46, 'end': 866.56}, {'word': ' around.', 'start': 866.56, 'end': 866.72}, {'word': ' What', 'start': 866.88, 'end': 866.98}, {'word': ' do', 'start': 866.98, 'end': 867.08}, {'word': ' you', 'start': 867.08, 'end': 867.1}, {'word': ' do?', 'start': 867.1, 'end': 867.3}, {'word': ' And', 'start': 867.38, 'end': 867.54}, {'word': ' then', 'start': 867.54, 'end': 867.74}, {'word': ' you', 'start': 867.74, 'end': 867.98}, {'word': ' start', 'start': 867.98, 'end': 868.24}, {'word': ' to', 'start': 868.24, 'end': 868.38}, {'word': ' learn.', 'start': 868.38, 'end': 868.56}, {'word': ' Yeah.', 'start': 868.74, 'end': 868.9}, {'word': ' The', 'start': 869.14, 'end': 869.54}, {'word': ' more', 'start': 869.54, 'end': 869.78}, {'word': ' persistent', 'start': 869.78, 'end': 870.2}, {'word': ' you', 'start': 870.2, 'end': 870.66}, {'word': ' are', 'start': 870.66, 'end': 870.8}, {'word': ' in', 'start': 870.8, 'end': 870.96}, {'word': ' showing', 'start': 870.96, 'end': 871.2}, {'word': ' up', 'start': 871.2, 'end': 871.48}, {'word': ' is', 'start': 871.48, 'end': 871.68}, {'word': ' going', 'start': 871.68, 'end': 871.96}, {'word': ' to', 'start': 871.96, 'end': 872.1}, {'word': ' value.', 'start': 872.1, 'end': 872.32}, {'word': ' Another', 'start': 872.66, 'end': 873.06}, {'word': ' one', 'start': 873.06, 'end': 873.3}, {'word': ' is', 'start': 873.3, 'end': 873.62}, {'word': ' we', 'start': 873.62, 'end': 874.2}, {'word': ' know', 'start': 874.2, 'end': 874.64}, {'word': ' the', 'start': 874.64, 'end': 874.8}, {'word': ' stats.', 'start': 874.8, 'end': 875.06}, {'word': ' 80', 'start': 875.24, 'end': 875.38}, {'word': '%', 'start': 875.38, 'end': 875.7}, {'word': ' of', 'start': 875.7, 'end': 875.96}, {'word': ' the', 'start': 875.96, 'end': 876.06}, {'word': ' jobs', 'start': 876.06, 'end': 876.34}, {'word': ' in', 'start': 876.34, 'end': 876.62}, {'word': ' America', 'start': 876.62, 'end': 876.96}, {'word': ' are', 'start': 876.96, 'end': 877.16}, {'word': ' filled', 'start': 877.16, 'end': 877.4}, {'word': ' by', 'start': 877.4, 'end': 877.64}, {'word': ' repros.', 'start': 877.64, 'end': 878.0}, {'word': ' So', 'start': 879.72, 'end': 880.12}, {'word': ' you', 'start': 880.12, 'end': 880.52}, {'word': ' have', 'start': 880.52, 'end': 880.72}, {'word': ' left', 'start': 880.72, 'end': 881.0}, {'word': ' with', 'start': 881.0, 'end': 881.18}, {'word': ' 20%.', 'start': 881.18, 'end': 881.84}, {'word': ' You', 'start': 881.84, 'end': 882.08}, {'word': ' are.', 'start': 882.08, 'end': 882.28}, {'word': ' And', 'start': 882.84, 'end': 883.24}, {'word': ' most', 'start': 883.24, 'end': 883.64}, {'word': ' of', 'start': 883.64, 'end': 883.84}, {'word': ' the,', 'start': 883.84, 'end': 884.0}, {'word': ' unfortunately,', 'start': 884.14, 'end': 885.24}, {'word': ' these', 'start': 885.78, 'end': 886.32}, {'word': ' federal', 'start': 886.32, 'end': 886.62}, {'word': ' laws,', 'start': 886.62, 'end': 887.0}, {'word': ' compliance,', 'start': 887.2, 'end': 887.54}, {'word': ' HR', 'start': 887.66, 'end': 887.94}, {'word': ' laws,', 'start': 887.94, 'end': 888.3}, {'word': ' and', 'start': 888.52, 'end': 888.64}, {'word': ' state', 'start': 888.64, 'end': 888.9}, {'word': ' laws', 'start': 888.9, 'end': 889.22}, {'word': ' create', 'start': 889.22, 'end': 889.56}, {'word': ' a', 'start': 889.56, 'end': 889.64}, {'word': ' lot', 'start': 889.64, 'end': 889.64}, {'word': ' of', 'start': 889.64, 'end': 889.64}, {'word': ' problems.', 'start': 889.64, 'end': 889.64}, {'word': ' And', 'start': 889.66, 'end': 889.8}, {'word': ' they', 'start': 889.8, 'end': 889.8}, {'word': ' create', 'start': 889.8, 'end': 889.8}, {'word': ' a', 'start': 889.8, 'end': 889.84}, {'word': ' big', 'start': 889.84, 'end': 890.12}, {'word': ' mess.', 'start': 890.12, 'end': 890.96}, {'word': ' Oh,', 'start': 891.44, 'end': 891.84}, {'word': \" you've\", 'start': 891.9, 'end': 892.1}, {'word': ' got', 'start': 892.1, 'end': 892.14}, {'word': ' to', 'start': 892.14, 'end': 892.28}, {'word': ' post', 'start': 892.28, 'end': 892.48}, {'word': ' a', 'start': 892.48, 'end': 892.62}, {'word': ' job', 'start': 892.62, 'end': 892.98}, {'word': ' two', 'start': 892.98, 'end': 893.94}, {'word': ' weeks', 'start': 893.94, 'end': 894.34}, {'word': ' before,', 'start': 894.34, 'end': 895.48}, {'word': ' you', 'start': 895.74, 'end': 895.94}, {'word': ' know,', 'start': 895.94, 'end': 896.1}, {'word': \" it's\", 'start': 896.18, 'end': 896.44}, {'word': ' all', 'start': 896.44, 'end': 896.98}, {'word': ' this.', 'start': 896.98, 'end': 897.22}, {'word': ' I', 'start': 897.3, 'end': 897.4}, {'word': \" don't\", 'start': 897.4, 'end': 897.58}, {'word': ' know', 'start': 897.58, 'end': 897.66}, {'word': ' if', 'start': 897.66, 'end': 897.82}, {'word': \" it's\", 'start': 897.82, 'end': 897.96}, {'word': ' really', 'start': 897.96, 'end': 898.14}, {'word': ' helping', 'start': 898.14, 'end': 898.5}, {'word': ' anybody.', 'start': 898.5, 'end': 898.98}, {'word': ' Well,', 'start': 899.34, 'end': 899.74}, {'word': ' it', 'start': 899.9, 'end': 900.14}, {'word': ' depends.', 'start': 900.14, 'end': 900.92}, {'word': ' Right.', 'start': 901.18, 'end': 901.44}, {'word': ' But', 'start': 901.54, 'end': 901.64}, {'word': ' in', 'start': 901.64, 'end': 901.76}, {'word': ' a', 'start': 901.76, 'end': 901.86}, {'word': ' lot', 'start': 901.86, 'end': 902.0}, {'word': ' of', 'start': 902.0, 'end': 902.08}, {'word': ' cases,', 'start': 902.08, 'end': 902.44}, {'word': ' what', 'start': 902.64, 'end': 903.18}, {'word': ' happens', 'start': 903.18, 'end': 903.54}, {'word': ' is', 'start': 903.54, 'end': 903.8}, {'word': ' if', 'start': 903.8, 'end': 903.98}, {'word': \" they're\", 'start': 903.98, 'end': 904.34}, {'word': ' taking', 'start': 904.34, 'end': 904.58}, {'word': ' somebody', 'start': 904.58, 'end': 904.98}, {'word': ' internally', 'start': 904.98, 'end': 905.6}, {'word': ' or', 'start': 905.6, 'end': 905.96}, {'word': \" they're\", 'start': 905.96, 'end': 906.1}, {'word': ' taking', 'start': 906.1, 'end': 906.3}, {'word': ' someone', 'start': 906.3, 'end': 906.6}, {'word': ' through.', 'start': 906.6, 'end': 906.8}, {'word': ' I', 'start': 906.94, 'end': 907.0}, {'word': ' already', 'start': 907.0, 'end': 907.3}, {'word': ' know', 'start': 907.3, 'end': 907.5}, {'word': ' who', 'start': 907.5, 'end': 907.68}, {'word': ' they', 'start': 907.68, 'end': 907.92}, {'word': ' are.', 'start': 907.92, 'end': 908.02}, {'word': ' They', 'start': 908.04, 'end': 908.44}, {'word': ' post', 'start': 908.44, 'end': 908.68}, {'word': ' the', 'start': 908.68, 'end': 908.82}, {'word': ' job.', 'start': 908.82, 'end': 908.98}, {'word': ' I', 'start': 909.02, 'end': 909.2}, {'word': \" can't\", 'start': 909.2, 'end': 909.48}, {'word': ' tell', 'start': 909.48, 'end': 909.62}, {'word': ' you', 'start': 909.62, 'end': 909.7}, {'word': ' how', 'start': 909.7, 'end': 909.78}, {'word': ' many', 'start': 909.78, 'end': 909.9}, {'word': ' people', 'start': 909.9, 'end': 910.16}, {'word': ' tell', 'start': 910.16, 'end': 910.36}, {'word': ' me', 'start': 910.36, 'end': 910.48}, {'word': ' they', 'start': 910.48, 'end': 910.58}, {'word': ' apply', 'start': 910.58, 'end': 910.76}, {'word': ' to', 'start': 910.76, 'end': 910.96}, {'word': ' 100', 'start': 910.96, 'end': 911.12}, {'word': ' jobs', 'start': 911.12, 'end': 911.54}, {'word': ' and', 'start': 911.54, 'end': 911.66}, {'word': ' only', 'start': 911.66, 'end': 911.96}, {'word': ' hear', 'start': 911.96, 'end': 912.16}, {'word': ' back', 'start': 912.16, 'end': 912.48}, {'word': ' from', 'start': 912.48, 'end': 913.66}, {'word': ' two,', 'start': 913.66, 'end': 913.82}, {'word': ' three', 'start': 913.9, 'end': 914.08}, {'word': ' companies.', 'start': 914.08, 'end': 914.46}, {'word': \" It's\", 'start': 914.56, 'end': 914.74}, {'word': ' like', 'start': 914.74, 'end': 914.86}, {'word': \" you're\", 'start': 914.86, 'end': 915.52}, {'word': ' applying', 'start': 915.52, 'end': 915.72}, {'word': ' to', 'start': 915.72, 'end': 916.04}, {'word': ' a', 'start': 916.04, 'end': 916.14}, {'word': ' job.', 'start': 916.14, 'end': 916.32}, {'word': \" You're\", 'start': 916.36, 'end': 916.46}, {'word': ' not', 'start': 916.46, 'end': 916.6}, {'word': ' even', 'start': 916.6, 'end': 916.76}, {'word': ' getting', 'start': 916.76, 'end': 916.98}, {'word': ' a', 'start': 916.98, 'end': 917.14}, {'word': ' thing', 'start': 917.14, 'end': 917.3}, {'word': ' back', 'start': 917.3, 'end': 917.58}, {'word': ' that', 'start': 917.58, 'end': 917.78}, {'word': ' says,', 'start': 917.78, 'end': 917.96}, {'word': ' thank', 'start': 917.98, 'end': 918.2}, {'word': ' you', 'start': 918.2, 'end': 918.3}, {'word': ' for', 'start': 918.3, 'end': 918.44}, {'word': ' applying.', 'start': 918.44, 'end': 918.7}, {'word': \" We've\", 'start': 918.7, 'end': 919.1}, {'word': ' gone', 'start': 919.1, 'end': 919.2}, {'word': ' a', 'start': 919.2, 'end': 919.34}, {'word': ' different', 'start': 919.34, 'end': 919.52}, {'word': ' direction.', 'start': 919.52, 'end': 919.9}, {'word': \" You're\", 'start': 920.18, 'end': 920.56}, {'word': ' just', 'start': 920.56, 'end': 920.76}, {'word': ' never,', 'start': 920.76, 'end': 921.08}, {'word': \" it's\", 'start': 921.16, 'end': 921.3}, {'word': ' an', 'start': 921.3, 'end': 921.38}, {'word': ' abyss.', 'start': 921.38, 'end': 921.98}, {'word': \" I'll\", 'start': 922.06, 'end': 922.38}, {'word': ' tell', 'start': 922.38, 'end': 922.48}, {'word': ' you', 'start': 922.48, 'end': 922.6}, {'word': ' why', 'start': 922.6, 'end': 922.82}, {'word': ' people', 'start': 922.82, 'end': 923.2}, {'word': \" don't\", 'start': 923.2, 'end': 923.54}, {'word': ' respond.', 'start': 923.54, 'end': 923.82}, {'word': ' There', 'start': 924.38, 'end': 924.82}, {'word': ' is', 'start': 924.82, 'end': 924.98}, {'word': ' a', 'start': 924.98, 'end': 925.04}, {'word': ' compliance.', 'start': 925.04, 'end': 925.46}, {'word': ' They', 'start': 925.72, 'end': 926.16}, {'word': ' could', 'start': 926.16, 'end': 926.58}, {'word': ' be', 'start': 926.58, 'end': 926.8}, {'word': ' you', 'start': 926.8, 'end': 926.88}, {'word': ' are', 'start': 926.88, 'end': 927.0}, {'word': ' pulled', 'start': 927.0, 'end': 927.22}, {'word': ' into', 'start': 927.22, 'end': 927.44}, {'word': ' a', 'start': 927.44, 'end': 927.54}, {'word': ' lawsuit.', 'start': 927.54, 'end': 927.84}, {'word': ' Right.', 'start': 928.08, 'end': 928.36}, {'word': ' I', 'start': 929.3599999999999, 'end': 929.8}, {'word': ' think', 'start': 929.8, 'end': 930.24}, {'word': \" it's\", 'start': 930.24, 'end': 930.48}, {'word': ' a', 'start': 930.48, 'end': 930.82}, {'word': ' part', 'start': 930.82, 'end': 931.06}, {'word': ' of,', 'start': 931.06, 'end': 931.28}, {'word': ' because', 'start': 931.44, 'end': 932.94}, {'word': ' the', 'start': 932.94, 'end': 933.12}, {'word': ' communication', 'start': 933.12, 'end': 933.46}, {'word': ' sometimes', 'start': 933.46, 'end': 934.0}, {'word': ' can', 'start': 934.0, 'end': 934.46}, {'word': ' turn', 'start': 934.46, 'end': 934.72}, {'word': ' on', 'start': 934.72, 'end': 934.9}, {'word': ' your,', 'start': 934.9, 'end': 935.04}, {'word': ' against', 'start': 935.18, 'end': 935.68}, {'word': ' you.', 'start': 935.68, 'end': 935.98}, {'word': \" That's\", 'start': 936.06, 'end': 936.38}, {'word': ' true.', 'start': 936.38, 'end': 936.54}, {'word': ' So', 'start': 936.9, 'end': 937.34}, {'word': ' companies', 'start': 937.34, 'end': 938.58}, {'word': ' are', 'start': 938.58, 'end': 938.8}, {'word': ' scared', 'start': 938.8, 'end': 939.04}, {'word': ' to', 'start': 939.04, 'end': 939.24}, {'word': ' do', 'start': 939.24, 'end': 939.38}, {'word': ' anything.', 'start': 939.38, 'end': 939.7}, {'word': ' Right.', 'start': 940.14, 'end': 940.58}, {'word': \" It's\", 'start': 940.64, 'end': 940.92}, {'word': ' sensitive', 'start': 940.92, 'end': 941.18}, {'word': ' to', 'start': 941.18, 'end': 941.7}, {'word': ' somebody.', 'start': 941.7, 'end': 942.08}, {'word': \" He's\", 'start': 942.54, 'end': 942.98}, {'word': ' desperate.', 'start': 942.98, 'end': 943.3}, {'word': ' He', 'start': 943.48, 'end': 943.62}, {'word': ' needs', 'start': 943.62, 'end': 943.84}, {'word': ' a', 'start': 943.84, 'end': 944.0}, {'word': ' job.', 'start': 944.0, 'end': 944.36}, {'word': ' And', 'start': 944.38, 'end': 944.54}, {'word': ' he', 'start': 944.54, 'end': 944.64}, {'word': ' puts', 'start': 944.64, 'end': 944.92}, {'word': ' it.', 'start': 944.92, 'end': 945.1}, {'word': ' Then', 'start': 945.12, 'end': 945.32}, {'word': ' you', 'start': 945.32, 'end': 945.44}, {'word': ' want', 'start': 945.44, 'end': 945.66}, {'word': ' to', 'start': 945.66, 'end': 945.82}, {'word': ' help', 'start': 945.82, 'end': 946.04}, {'word': ' him', 'start': 946.04, 'end': 946.32}, {'word': ' to', 'start': 946.32, 'end': 946.88}, {'word': ' send', 'start': 946.88, 'end': 947.1}, {'word': ' a', 'start': 947.1, 'end': 947.24}, {'word': ' message.', 'start': 947.24, 'end': 947.5}, {'word': ' That', 'start': 947.5, 'end': 947.72}, {'word': ' message', 'start': 947.72, 'end': 948.14}, {'word': ' can', 'start': 948.14, 'end': 948.38}, {'word': ' turn', 'start': 948.38, 'end': 948.64}, {'word': ' into', 'start': 948.64, 'end': 948.84}, {'word': ' your', 'start': 948.84, 'end': 949.1}, {'word': ' side.', 'start': 949.1, 'end': 949.4}, {'word': ' So', 'start': 949.82, 'end': 950.24}, {'word': ' there', 'start': 950.24, 'end': 950.4}, {'word': ' is', 'start': 950.4, 'end': 950.48}, {'word': ' always', 'start': 950.48, 'end': 950.84}, {'word': ' how', 'start': 950.84, 'end': 952.4}, {'word': ' you', 'start': 952.4, 'end': 952.62}, {'word': ' see', 'start': 952.62, 'end': 952.82}, {'word': ' the', 'start': 952.82, 'end': 952.98}, {'word': ' world,', 'start': 952.98, 'end': 953.22}, {'word': ' how', 'start': 953.32, 'end': 953.44}, {'word': ' you', 'start': 953.44, 'end': 953.62}, {'word': ' can', 'start': 953.62, 'end': 953.82}, {'word': ' say.', 'start': 953.82, 'end': 954.16}, {'word': ' For', 'start': 954.96, 'end': 955.38}, {'word': ' me,', 'start': 955.38, 'end': 955.8}, {'word': ' like,', 'start': 956.04, 'end': 956.16}, {'word': ' as', 'start': 956.24, 'end': 956.7}, {'word': ' you', 'start': 956.7, 'end': 956.82}, {'word': ' said,', 'start': 956.82, 'end': 957.08}, {'word': ' know', 'start': 957.36, 'end': 958.68}, {'word': ' people,', 'start': 958.68, 'end': 959.0}, {'word': ' talk', 'start': 959.14, 'end': 959.42}, {'word': ' to', 'start': 959.42, 'end': 959.56}, {'word': ' people,', 'start': 959.56, 'end': 959.8}, {'word': ' connect', 'start': 960.0, 'end': 960.74}, {'word': ' with', 'start': 960.74, 'end': 960.94}, {'word': ' people.', 'start': 960.94, 'end': 961.12}, {'word': ' You', 'start': 962.46, 'end': 962.88}, {'word': ' know,', 'start': 962.88, 'end': 963.06}, {'word': ' your', 'start': 963.12, 'end': 963.7}, {'word': ' local', 'start': 963.7, 'end': 964.02}, {'word': ' community', 'start': 964.02, 'end': 964.4}, {'word': ' is', 'start': 964.4, 'end': 964.62}, {'word': ' a', 'start': 964.62, 'end': 964.7}, {'word': ' big', 'start': 964.7, 'end': 964.88}, {'word': ' thing.', 'start': 964.88, 'end': 965.16}, {'word': ' Yep.', 'start': 965.26, 'end': 965.52}, {'word': ' You,', 'start': 965.56, 'end': 965.92}, {'word': ' if', 'start': 966.1, 'end': 967.32}, {'word': ' you,', 'start': 967.32, 'end': 967.58}, {'word': ' you,', 'start': 967.6, 'end': 968.1}, {'word': ' if', 'start': 968.2, 'end': 968.86}, {'word': ' you', 'start': 968.86, 'end': 969.02}, {'word': ' are', 'start': 969.02, 'end': 969.12}, {'word': ' a', 'start': 969.12, 'end': 969.26}, {'word': ' person', 'start': 969.26, 'end': 969.54}, {'word': ' they', 'start': 969.54, 'end': 969.76}, {'word': ' like', 'start': 969.76, 'end': 970.46}, {'word': ' you,', 'start': 970.46, 'end': 970.74}, {'word': \" you'll\", 'start': 970.82, 'end': 971.26}, {'word': ' get', 'start': 971.26, 'end': 971.38}, {'word': ' a', 'start': 971.38, 'end': 971.54}, {'word': ' job.', 'start': 971.54, 'end': 971.74}, {'word': ' Well,', 'start': 972.0, 'end': 972.3}, {'word': ' I', 'start': 972.38, 'end': 972.5}, {'word': ' think', 'start': 972.5, 'end': 972.7}, {'word': \" it's\", 'start': 972.7, 'end': 972.84}, {'word': ' your', 'start': 972.84, 'end': 972.92}, {'word': ' local', 'start': 972.92, 'end': 973.22}, {'word': ' community.', 'start': 973.22, 'end': 973.56}, {'word': ' So', 'start': 973.68, 'end': 973.84}, {'word': \" it's\", 'start': 973.84, 'end': 974.08}, {'word': ' things', 'start': 974.08, 'end': 974.26}, {'word': ' like', 'start': 974.26, 'end': 974.46}, {'word': ' technology', 'start': 974.46, 'end': 974.88}, {'word': ' council.', 'start': 974.88, 'end': 975.44}, {'word': \" It's\", 'start': 975.54, 'end': 975.72}, {'word': ' things', 'start': 975.72, 'end': 975.86}, {'word': ' like', 'start': 975.86, 'end': 976.06}, {'word': ' chamber,', 'start': 976.06, 'end': 976.4}, {'word': ' a', 'start': 976.5, 'end': 976.66}, {'word': ' whole', 'start': 976.66, 'end': 976.72}, {'word': ' bunch', 'start': 976.72, 'end': 976.94}, {'word': ' of', 'start': 976.94, 'end': 977.0}, {'word': ' other', 'start': 977.0, 'end': 977.12}, {'word': ' organizations.', 'start': 977.12, 'end': 977.44}, {'word': ' You', 'start': 978.18, 'end': 978.5}, {'word': ' know,', 'start': 978.5, 'end': 978.72}, {'word': ' you', 'start': 978.74, 'end': 979.14}, {'word': \" can't\", 'start': 979.14, 'end': 979.44}, {'word': ' belong', 'start': 979.44, 'end': 979.58}, {'word': ' to', 'start': 979.58, 'end': 979.8}, {'word': ' everything', 'start': 979.8, 'end': 980.1}, {'word': ' and', 'start': 980.1, 'end': 980.26}, {'word': ' you', 'start': 980.26, 'end': 980.32}, {'word': \" can't\", 'start': 980.32, 'end': 980.48}, {'word': ' go', 'start': 980.48, 'end': 980.64}, {'word': ' to', 'start': 980.64, 'end': 980.7}, {'word': ' everything.', 'start': 980.7, 'end': 980.92}, {'word': ' So', 'start': 981.04, 'end': 981.14}, {'word': ' pick', 'start': 981.14, 'end': 981.32}, {'word': ' two', 'start': 981.32, 'end': 981.54}, {'word': ' or', 'start': 981.54, 'end': 981.72}, {'word': ' three', 'start': 981.72, 'end': 981.9}, {'word': ' groups.', 'start': 981.9, 'end': 982.28}, {'word': ' Yeah.', 'start': 982.38, 'end': 982.54}, {'word': ' And', 'start': 982.56, 'end': 982.66}, {'word': ' go', 'start': 982.66, 'end': 983.06}, {'word': ' to', 'start': 983.06, 'end': 983.18}, {'word': ' those.', 'start': 983.18, 'end': 983.48}, {'word': ' Regular.', 'start': 983.48, 'end': 983.74}, {'word': ' Regular.', 'start': 983.92, 'end': 984.24}, {'word': ' Because', 'start': 984.32, 'end': 984.64}, {'word': ' most', 'start': 984.64, 'end': 985.1}, {'word': ' of', 'start': 985.1, 'end': 985.3}, {'word': ' us', 'start': 985.3, 'end': 985.44}, {'word': ' only', 'start': 985.44, 'end': 985.6}, {'word': ' have', 'start': 985.6, 'end': 985.82}, {'word': ' one', 'start': 985.82, 'end': 986.22}, {'word': ' event', 'start': 986.22, 'end': 986.42}, {'word': ' a', 'start': 986.42, 'end': 986.58}, {'word': ' month.', 'start': 986.58, 'end': 986.8}, {'word': ' Yeah.', 'start': 986.94, 'end': 987.1}, {'word': ' So', 'start': 987.14, 'end': 987.2}, {'word': ' pick', 'start': 987.2, 'end': 987.38}, {'word': ' three.', 'start': 987.38, 'end': 987.66}, {'word': \" You've\", 'start': 987.68, 'end': 987.82}, {'word': ' got', 'start': 987.82, 'end': 987.94}, {'word': ' three', 'start': 987.94, 'end': 988.28}, {'word': ' things', 'start': 988.28, 'end': 988.62}, {'word': ' to', 'start': 988.62, 'end': 988.86}, {'word': ' go', 'start': 988.86, 'end': 989.0}, {'word': ' to', 'start': 989.0, 'end': 989.1}, {'word': ' in', 'start': 989.1, 'end': 989.26}, {'word': ' a', 'start': 989.26, 'end': 989.28}, {'word': ' month.', 'start': 989.28, 'end': 989.44}, {'word': ' Yeah.', 'start': 989.46, 'end': 989.58}, {'word': ' One', 'start': 989.58, 'end': 989.76}, {'word': ' that', 'start': 989.76, 'end': 989.94}, {'word': ' does', 'start': 989.94, 'end': 990.1}, {'word': ' a', 'start': 990.1, 'end': 990.18}, {'word': ' breakfast,', 'start': 990.18, 'end': 990.46}, {'word': ' one', 'start': 990.6, 'end': 990.74}, {'word': ' that', 'start': 990.74, 'end': 990.86}, {'word': ' does', 'start': 990.86, 'end': 991.06}, {'word': ' a', 'start': 991.06, 'end': 991.18}, {'word': ' lunch,', 'start': 991.18, 'end': 991.4}, {'word': ' one', 'start': 991.48, 'end': 991.56}, {'word': ' that', 'start': 991.56, 'end': 991.7}, {'word': ' does', 'start': 991.7, 'end': 991.84}, {'word': ' a', 'start': 991.84, 'end': 991.92}, {'word': ' happy', 'start': 991.92, 'end': 992.14}, {'word': ' hour.', 'start': 992.14, 'end': 992.36}, {'word': ' And', 'start': 993.6599999999999, 'end': 993.9799999999999}, {'word': ' always', 'start': 993.9799999999999, 'end': 994.3}, {'word': ' go', 'start': 994.3, 'end': 994.64}, {'word': ' to', 'start': 994.64, 'end': 994.76}, {'word': ' those.', 'start': 994.76, 'end': 994.9}, {'word': ' And', 'start': 994.96, 'end': 995.06}, {'word': ' then', 'start': 995.06, 'end': 995.22}, {'word': ' you', 'start': 995.22, 'end': 995.3}, {'word': ' can', 'start': 995.3, 'end': 995.44}, {'word': ' drop', 'start': 995.44, 'end': 995.66}, {'word': ' in', 'start': 995.66, 'end': 995.82}, {'word': ' on', 'start': 995.82, 'end': 995.94}, {'word': ' other', 'start': 995.94, 'end': 996.12}, {'word': ' ones', 'start': 996.12, 'end': 996.32}, {'word': ' if', 'start': 996.32, 'end': 996.46}, {'word': \" there's\", 'start': 996.46, 'end': 996.64}, {'word': ' a', 'start': 996.64, 'end': 996.7}, {'word': ' good', 'start': 996.7, 'end': 996.82}, {'word': ' topic.', 'start': 996.82, 'end': 997.12}, {'word': ' But', 'start': 997.2, 'end': 997.52}, {'word': ' the', 'start': 997.52, 'end': 997.66}, {'word': ' other', 'start': 997.66, 'end': 997.9}, {'word': ' thing', 'start': 997.9, 'end': 998.12}, {'word': ' is,', 'start': 998.12, 'end': 998.46}, {'word': ' is', 'start': 998.5, 'end': 998.8}, {'word': ' what', 'start': 998.8, 'end': 999.04}, {'word': ' is', 'start': 999.04, 'end': 999.16}, {'word': ' your', 'start': 999.16, 'end': 999.32}, {'word': ' industry?', 'start': 999.32, 'end': 999.6}, {'word': ' Yeah.', 'start': 999.94, 'end': 1000.26}, {'word': ' And', 'start': 1000.32, 'end': 1000.46}, {'word': ' what', 'start': 1000.46, 'end': 1000.66}, {'word': ' type', 'start': 1000.66, 'end': 1000.86}, {'word': ' of', 'start': 1000.86, 'end': 1001.06}, {'word': ' industry', 'start': 1001.06, 'end': 1001.42}, {'word': ' groups,', 'start': 1001.42, 'end': 1001.88}, {'word': ' both', 'start': 1002.0, 'end': 1002.16}, {'word': ' locally', 'start': 1002.16, 'end': 1002.52}, {'word': ' and', 'start': 1002.52, 'end': 1002.72}, {'word': ' nationally', 'start': 1002.72, 'end': 1003.08}, {'word': ' exist.', 'start': 1003.08, 'end': 1003.52}, {'word': ' Yeah.', 'start': 1003.7, 'end': 1003.86}, {'word': ' I', 'start': 1003.86, 'end': 1003.86}, {'word': ' mean,', 'start': 1003.86, 'end': 1003.9}, {'word': ' the,', 'start': 1003.96, 'end': 1004.08}, {'word': ' the', 'start': 1004.12, 'end': 1004.58}, {'word': ' people', 'start': 1004.58, 'end': 1004.82}, {'word': ' who', 'start': 1004.82, 'end': 1005.06}, {'word': ' get', 'start': 1005.06, 'end': 1005.3}, {'word': ' really', 'start': 1005.3, 'end': 1005.6}, {'word': ' involved', 'start': 1005.6, 'end': 1006.02}, {'word': ' with,', 'start': 1006.02, 'end': 1007.02}, {'word': ' you', 'start': 1007.06, 'end': 1007.32}, {'word': ' know,', 'start': 1007.32, 'end': 1007.42}, {'word': ' the,', 'start': 1007.42, 'end': 1007.42}, {'word': ' the,', 'start': 1007.42, 'end': 1007.42}, {'word': ' the,', 'start': 1007.42, 'end': 1007.42}, {'word': ' the,', 'start': 1007.42, 'end': 1007.42}, {'word': ' the,', 'start': 1007.42, 'end': 1007.42}, {'word': ' the,', 'start': 1007.42, 'end': 1007.42}, {'word': ' the,', 'start': 1007.42, 'end': 1007.54}, {'word': ' the,', 'start': 1007.58, 'end': 1007.58}, {'word': ' the,', 'start': 1007.58, 'end': 1007.58}, {'word': ' the,', 'start': 1007.58, 'end': 1007.58}, {'word': ' the.', 'start': 1007.58, 'end': 1007.58}, {'word': ' You', 'start': 1007.58, 'end': 1007.58}, {'word': ' know', 'start': 1007.58, 'end': 1007.58}, {'word': ' if', 'start': 1007.58, 'end': 1007.58}, {'word': ' there', 'start': 1007.58, 'end': 1007.62}, {'word': ' are', 'start': 1007.62, 'end': 1007.74}, {'word': ' a', 'start': 1007.74, 'end': 1007.78}, {'word': ' high,', 'start': 1007.78, 'end': 1007.94}, {'word': ' if', 'start': 1007.96, 'end': 1008.04}, {'word': ' there', 'start': 1008.04, 'end': 1008.2}, {'word': ' are', 'start': 1008.2, 'end': 1008.26}, {'word': ' hiring', 'start': 1008.26, 'end': 1008.48}, {'word': ' like', 'start': 1008.48, 'end': 1008.74}, {'word': ' CIO', 'start': 1008.74, 'end': 1009.18}, {'word': ' and', 'start': 1009.18, 'end': 1009.4}, {'word': ' the', 'start': 1009.4, 'end': 1009.46}, {'word': ' a', 'start': 1009.46, 'end': 1009.5}, {'word': ' property', 'start': 1009.5, 'end': 1009.76}, {'word': ' with', 'start': 1009.76, 'end': 1010.24}, {'word': ' real', 'start': 1010.24, 'end': 1010.74}, {'word': ' estate', 'start': 1010.74, 'end': 1010.74}, {'word': ' marketing.', 'start': 1010.74, 'end': 1010.76}, {'word': ' Yeah.', 'start': 1010.96, 'end': 1011.06}, {'word': ' Does', 'start': 1011.1, 'end': 1011.34}, {'word': ' a', 'start': 1011.34, 'end': 1011.34}, {'word': ' beardamine', 'start': 1011.34, 'end': 1011.44}, {'word': ' comenzar', 'start': 1011.44, 'end': 1011.46}, {'word': ' beach', 'start': 1011.46, 'end': 1011.46}, {'word': ' and', 'start': 1011.46, 'end': 1011.54}, {'word': ' O', 'start': 1011.54, 'end': 1011.72}, {'word': ' supposed', 'start': 1011.72, 'end': 1011.88}, {'word': ' to', 'start': 1011.88, 'end': 1011.98}, {'word': ' be', 'start': 1011.98, 'end': 1012.2}, {'word': ' like', 'start': 1012.2, 'end': 1012.38}, {'word': ' a', 'start': 1012.38, 'end': 1012.38}, {'word': ' beach', 'start': 1012.38, 'end': 1012.38}, {'word': ' commercial', 'start': 1012.38, 'end': 1012.38}, {'word': ' to', 'start': 1012.38, 'end': 1012.38}, {'word': ' describe', 'start': 1012.38, 'end': 1012.38}, {'word': ' not', 'start': 1012.38, 'end': 1012.38}, {'word': ' in저i', 'start': 1012.38, 'end': 1012.54}, {'word': ' a', 'start': 1012.54, 'end': 1012.54}, {'word': ' rock', 'start': 1012.54, 'end': 1012.54}, {'word': ' star', 'start': 1012.54, 'end': 1012.54}, {'word': ' and', 'start': 1012.54, 'end': 1012.54}, {'word': ' make', 'start': 1012.54, 'end': 1012.54}, {'word': ' a', 'start': 1012.54, 'end': 1012.54}, {'word': ' house', 'start': 1012.54, 'end': 1012.54}, {'word': ' sign', 'start': 1012.54, 'end': 1012.54}, {'word': ' Post', 'start': 1012.54, 'end': 1012.54}, {'word': ' because', 'start': 1012.54, 'end': 1012.54}, {'word': ' o', 'start': 1012.54, 'end': 1012.56}, {'word': ' supposed', 'start': 1012.56, 'end': 1012.56}, {'word': ' to', 'start': 1012.56, 'end': 1014.0}, {'word': ' be', 'start': 1014.0, 'end': 1014.18}, {'word': ' like', 'start': 1014.18, 'end': 1014.28}, {'word': ' I', 'start': 1014.3, 'end': 1014.3}, {'word': ' only', 'start': 1014.3, 'end': 1014.44}, {'word': ' think', 'start': 1014.44, 'end': 1014.44}, {'word': ' aboutna', 'start': 1014.44, 'end': 1014.44}, {'word': ' James', 'start': 1014.44, 'end': 1014.44}, {'word': ' altogether', 'start': 1014.44, 'end': 1014.44}, {'word': ' and', 'start': 1014.44, 'end': 1014.44}, {'word': ' not', 'start': 1014.44, 'end': 1014.44}, {'word': ' others.', 'start': 1014.44, 'end': 1014.5}, {'word': ' Because', 'start': 1014.78, 'end': 1014.78}, {'word': ' I', 'start': 1014.78, 'end': 1014.78}, {'word': ' think', 'start': 1014.78, 'end': 1014.78}, {'word': \" you've\", 'start': 1014.78, 'end': 1014.78}, {'word': ' got', 'start': 1014.78, 'end': 1014.78}, {'word': ' to', 'start': 1014.78, 'end': 1014.78}, {'word': ' keep', 'start': 1014.78, 'end': 1014.78}, {'word': ' that', 'start': 1014.78, 'end': 1014.78}, {'word': ' shit', 'start': 1014.78, 'end': 1014.78}, {'word': ' to', 'start': 1014.78, 'end': 1014.78}, {'word': ' yourself.', 'start': 1014.78, 'end': 1015.1}, {'word': \" That's\", 'start': 1015.1, 'end': 1015.26}, {'word': ' pretty', 'start': 1015.26, 'end': 1015.3}, {'word': ' high.', 'start': 1015.3, 'end': 1015.3}, {'word': ' Like', 'start': 1015.3, 'end': 1015.3}, {'word': \" let's\", 'start': 1015.3, 'end': 1015.3}, {'word': ' look', 'start': 1015.3, 'end': 1015.36}, {'word': ' at...', 'start': 1015.36, 'end': 1015.36}, {'word': ' Okay.', 'start': 1015.36, 'end': 1015.44}]\n", "AudioTags       : [['Speech', 0.7468775510787964], ['Groan', 0.28120356798171997], ['<PERSON><PERSON><PERSON>', 0.07307898253202438], ['Gas<PERSON>', 0.06209409236907959], ['Inside, small room', 0.05534367263317108]]\n", "Diarization     : [{'start': 0.03096875, 'end': 1.5834687500000002, 'speaker': 'SPEAKER_01'}, {'start': 1.53284375, 'end': 8.114093750000002, 'speaker': 'SPEAKER_02'}, {'start': 9.227843750000002, 'end': 12.09659375, 'speaker': 'SPEAKER_02'}, {'start': 12.09659375, 'end': 22.609718750000003, 'speaker': 'SPEAKER_01'}, {'start': 23.723468750000002, 'end': 27.03096875, 'speaker': 'SPEAKER_00'}, {'start': 23.79096875, 'end': 25.25909375, 'speaker': 'SPEAKER_01'}, {'start': 27.03096875, 'end': 40.05846875, 'speaker': 'SPEAKER_01'}, {'start': 38.57346875, 'end': 74.44971875, 'speaker': 'SPEAKER_02'}, {'start': 75.04034375, 'end': 76.47471875000001, 'speaker': 'SPEAKER_02'}, {'start': 76.47471875000001, 'end': 84.97971875, 'speaker': 'SPEAKER_00'}, {'start': 88.97909375, 'end': 97.41659375, 'speaker': 'SPEAKER_00'}, {'start': 91.32471875, 'end': 91.45971875000001, 'speaker': 'SPEAKER_01'}, {'start': 98.32784375, 'end': 110.69721875, 'speaker': 'SPEAKER_02'}, {'start': 111.37221875, 'end': 118.13909375, 'speaker': 'SPEAKER_02'}, {'start': 118.98284375, 'end': 130.06971875000002, 'speaker': 'SPEAKER_02'}, {'start': 130.76159375, 'end': 133.63034375, 'speaker': 'SPEAKER_02'}, {'start': 134.45721875, 'end': 139.43534375000002, 'speaker': 'SPEAKER_02'}, {'start': 140.49846875, 'end': 148.83471875, 'speaker': 'SPEAKER_02'}, {'start': 149.**************, 'end': 152.14221875, 'speaker': 'SPEAKER_02'}, {'start': 152.80034375, 'end': 154.26846875, 'speaker': 'SPEAKER_02'}, {'start': 155.50034375, 'end': 167.71784375000001, 'speaker': 'SPEAKER_02'}, {'start': 168.62909375, 'end': 193.84034375000002, 'speaker': 'SPEAKER_02'}, {'start': 194.58284375000002, 'end': 211.00221875000003, 'speaker': 'SPEAKER_02'}, {'start': 210.96846875, 'end': 353.84909375, 'speaker': 'SPEAKER_01'}, {'start': 288.52596875, 'end': 289.75784375, 'speaker': 'SPEAKER_02'}, {'start': 295.20846875, 'end': 296.52471875000003, 'speaker': 'SPEAKER_00'}, {'start': 296.52471875000003, 'end': 297.62159375000005, 'speaker': 'SPEAKER_02'}, {'start': 353.15721875, 'end': 357.30846875000003, 'speaker': 'SPEAKER_02'}, {'start': 358.30409375000005, 'end': 362.18534375, 'speaker': 'SPEAKER_02'}, {'start': 363.23159375, 'end': 368.69909375000003, 'speaker': 'SPEAKER_02'}, {'start': 370.20096875, 'end': 377.05221875, 'speaker': 'SPEAKER_02'}, {'start': 370.23471875, 'end': 371.95596875, 'speaker': 'SPEAKER_01'}, {'start': 377.89596875, 'end': 381.97971875, 'speaker': 'SPEAKER_02'}, {'start': 380.76471875000004, 'end': 385.47284375000004, 'speaker': 'SPEAKER_01'}, {'start': 385.47284375000004, 'end': 400.27221875000004, 'speaker': 'SPEAKER_02'}, {'start': 401.35221875, 'end': 419.86409375000005, 'speaker': 'SPEAKER_02'}, {'start': 405.87471875, 'end': 406.02659375, 'speaker': 'SPEAKER_00'}, {'start': 406.02659375, 'end': 407.22471875, 'speaker': 'SPEAKER_01'}, {'start': 419.52659375, 'end': 490.48596875000004, 'speaker': 'SPEAKER_01'}, {'start': 456.53346875000005, 'end': 459.36846875000003, 'speaker': 'SPEAKER_02'}, {'start': 478.15034375000005, 'end': 478.40346875000006, 'speaker': 'SPEAKER_02'}, {'start': 488.29221875, 'end': 519.5615937499999, 'speaker': 'SPEAKER_02'}, {'start': 505.53846875000005, 'end': 505.87596875, 'speaker': 'SPEAKER_01'}, {'start': 520.4053437499999, 'end': 546.03846875, 'speaker': 'SPEAKER_02'}, {'start': 546.03846875, 'end': 625.21596875, 'speaker': 'SPEAKER_01'}, {'start': 551.4890937499999, 'end': 552.4509687500001, 'speaker': 'SPEAKER_02'}, {'start': 610.75409375, 'end': 610.9734687499999, 'speaker': 'SPEAKER_02'}, {'start': 625.9753437500001, 'end': 642.96846875, 'speaker': 'SPEAKER_01'}, {'start': 640.09971875, 'end': 650.95034375, 'speaker': 'SPEAKER_00'}, {'start': 649.6340937500001, 'end': 659.37096875, 'speaker': 'SPEAKER_01'}, {'start': 652.62096875, 'end': 652.8403437500001, 'speaker': 'SPEAKER_00'}, {'start': 653.59971875, 'end': 654.5278437500001, 'speaker': 'SPEAKER_02'}, {'start': 654.5278437500001, 'end': 686.21909375, 'speaker': 'SPEAKER_00'}, {'start': 673.56284375, 'end': 675.3347187500001, 'speaker': 'SPEAKER_01'}, {'start': 677.08971875, 'end': 678.9797187500001, 'speaker': 'SPEAKER_01'}, {'start': 683.2153437500001, 'end': 683.72159375, 'speaker': 'SPEAKER_01'}, {'start': 690.9778437500001, 'end': 714.7209687500001, 'speaker': 'SPEAKER_00'}, {'start': 697.0359687499999, 'end': 699.48284375, 'speaker': 'SPEAKER_01'}, {'start': 714.53534375, 'end': 719.5134687500001, 'speaker': 'SPEAKER_01'}, {'start': 717.1003437500001, 'end': 727.6640937500001, 'speaker': 'SPEAKER_00'}, {'start': 723.07409375, 'end': 723.52971875, 'speaker': 'SPEAKER_01'}, {'start': 723.52971875, 'end': 725.92596875, 'speaker': 'SPEAKER_02'}, {'start': 726.75284375, 'end': 738.0759687500001, 'speaker': 'SPEAKER_02'}, {'start': 738.9365937500002, 'end': 741.8390937500001, 'speaker': 'SPEAKER_02'}, {'start': 742.48034375, 'end': 751.8290937500001, 'speaker': 'SPEAKER_02'}, {'start': 753.17909375, 'end': 754.42784375, 'speaker': 'SPEAKER_02'}, {'start': 754.3772187500001, 'end': 767.79284375, 'speaker': 'SPEAKER_01'}, {'start': 766.84784375, 'end': 773.8003437500001, 'speaker': 'SPEAKER_02'}, {'start': 769.1259687500001, 'end': 770.8472187500001, 'speaker': 'SPEAKER_01'}, {'start': 769.8178437500001, 'end': 770.03721875, 'speaker': 'SPEAKER_00'}, {'start': 772.29846875, 'end': 868.75596875, 'speaker': 'SPEAKER_01'}, {'start': 864.3684687500001, 'end': 864.55409375, 'speaker': 'SPEAKER_00'}, {'start': 864.55409375, 'end': 865.29659375, 'speaker': 'SPEAKER_02'}, {'start': 868.7222187500001, 'end': 879.75846875, 'speaker': 'SPEAKER_02'}, {'start': 880.46721875, 'end': 899.46846875, 'speaker': 'SPEAKER_02'}, {'start': 899.46846875, 'end': 912.8334687500001, 'speaker': 'SPEAKER_01'}, {'start': 906.9440937500001, 'end': 908.15909375, 'speaker': 'SPEAKER_02'}, {'start': 913.5928437500002, 'end': 922.67159375, 'speaker': 'SPEAKER_01'}, {'start': 921.67596875, 'end': 928.66221875, 'speaker': 'SPEAKER_02'}, {'start': 929.64096875, 'end': 931.5478437500001, 'speaker': 'SPEAKER_02'}, {'start': 932.79659375, 'end': 957.53534375, 'speaker': 'SPEAKER_02'}, {'start': 958.5815937500001, 'end': 961.61909375, 'speaker': 'SPEAKER_02'}, {'start': 962.88471875, 'end': 972.0815937500001, 'speaker': 'SPEAKER_02'}, {'start': 972.0815937500001, 'end': 1015.46721875, 'speaker': 'SPEAKER_01'}, {'start': 982.42596875, 'end': 984.4678437500002, 'speaker': 'SPEAKER_02'}, {'start': 987.01596875, 'end': 987.87659375, 'speaker': 'SPEAKER_02'}]\n", "Relevance Score : 0.2030\n", "=================================\n"]}], "source": ["import json\n", "import pyodbc\n", "import re\n", "import numpy as np\n", "from sentence_transformers import SentenceTransformer, util\n", "from nltk.corpus import stopwords\n", "import nltk\n", "\n", "# Download stopwords (only first time needed)\n", "nltk.download(\"stopwords\", quiet=True)\n", "STOPWORDS = set(stopwords.words(\"english\"))\n", "\n", "# ================= CONFIG =================\n", "AZURE_SQL_CONNECTION_STRING = (\n", "    \"Driver={ODBC Driver 18 for SQL Server};\"\n", "    \"Server=tcp:videoprocessingserver.database.windows.net,1433;\"\n", "    \"Database=VideoProcessingDB;\"\n", "    \"Uid=sqladmin;\"\n", "    \"Pwd=Unnanu@2024;\"\n", "    \"Encrypt=yes;\"\n", "    \"TrustServerCertificate=no;\"\n", "    \"Connection Timeout=600;\"\n", ")\n", "\n", "# Load embeddings model\n", "embedder = SentenceTransformer(\"all-MiniLM-L6-v2\")\n", "\n", "# ============== QUERY PREPROCESSING ==============\n", "def preprocess_query(query: str) -> str:\n", "    # Lowercase\n", "    query = query.lower()\n", "\n", "    # Remove special characters (keep alphanumeric + spaces)\n", "    query = re.sub(r\"[^a-z0-9\\s]\", \"\", query)\n", "\n", "    # Remove stopwords\n", "    tokens = query.split()\n", "    filtered_tokens = [word for word in tokens if word not in STOPWORDS]\n", "\n", "    return \" \".join(filtered_tokens)\n", "\n", "# ============== DB FUNCTIONS ==============\n", "def connect_to_azure_sql():\n", "    try:\n", "        conn = pyodbc.connect(AZURE_SQL_CONNECTION_STRING)\n", "        return conn\n", "    except pyodbc.Error as e:\n", "        print(f\"❌ Error connecting to Azure SQL: {e}\")\n", "        raise\n", "\n", "def retrieve_relevant_rows(query: str, top_k: int = 1):\n", "    # Preprocess query\n", "    clean_query = preprocess_query(query)\n", "    print(f\"🔧 Processed Query: {clean_query}\")\n", "\n", "    # Embed\n", "    query_embedding = embedder.encode(clean_query, convert_to_numpy=True).astype(np.float32)\n", "    conn = connect_to_azure_sql()\n", "    cursor = conn.cursor()\n", "\n", "    sql_query = \"\"\"\n", "    SELECT VideoPath, Description, SrtContent, SrtContentObjects, Transcript, \n", "           WordTimestamps, AudioTags, Diarization, Embeddings\n", "    FROM VideoProcessingResults\n", "    \"\"\"\n", "    cursor.execute(sql_query)\n", "    rows = cursor.fetchall()\n", "\n", "    results = []\n", "    for row in rows:\n", "        result = {\n", "            \"VideoPath\": row.VideoPath,\n", "            \"Description\": row.Description or \"\",\n", "            \"SrtContent\": row.SrtContent or \"\",\n", "            \"SrtContentObjects\": row.SrtContentObjects or \"\",\n", "            \"Transcript\": row.Transcript or \"\",\n", "            \"WordTimestamps\": json.loads(row.WordTimestamps) if row.WordTimestamps else [],\n", "            \"AudioTags\": json.loads(row.AudioTags) if row.AudioTags else [],\n", "            \"Diarization\": json.loads(row.Diarization) if row.Diarization else [],\n", "            \"Embeddings\": json.loads(row.Embeddings) if row.Embeddings else {}\n", "        }\n", "\n", "        embeddings = result[\"Embeddings\"]\n", "        max_similarity = 0.0\n", "        for field in [\"description_embedding\", \"transcript_embedding\"]:\n", "            if field in embeddings:\n", "                field_embedding = np.array(embeddings[field], dtype=np.float32)\n", "                similarity = util.cos_sim(query_embedding, field_embedding)[0][0].item()\n", "                max_similarity = max(max_similarity, similarity)\n", "\n", "        result[\"relevance_score\"] = max_similarity\n", "        results.append(result)\n", "\n", "    results = sorted(results, key=lambda x: x[\"relevance_score\"], reverse=True)[:top_k]\n", "    cursor.close()\n", "    conn.close()\n", "    return results\n", "\n", "# ============== MAIN FUNCTION ==============\n", "# def main():\n", "#     query = input(\"🔍 Enter your query: \")\n", "#     top_results = retrieve_relevant_rows(query, top_k=3)\n", "\n", "#     print(\"\\n=== Top 3 Results ===\")\n", "#     for i, res in enumerate(top_results, start=1):\n", "#         print(f\"\\nResult {i}:\")\n", "#         print(f\"VideoPath: {res['VideoPath']}\")\n", "#         print(f\"Description: {res['Description'][:500]}...\")\n", "#         print(f\"Transcript: {res['Transcript'][:500]}...\")\n", "#         print(f\"Relevance Score: {res['relevance_score']:.4f}\")\n", "# ============== MAIN FUNCTION ==============\n", "def main():\n", "    query = input(\"🔍 Enter your query: \")\n", "    top_results = retrieve_relevant_rows(query, top_k=3)\n", "\n", "    print(\"\\n=== Top 3 Results ===\")\n", "    for i, res in enumerate(top_results, start=1):\n", "        print(f\"\\n========== Result {i} ==========\")\n", "        print(f\"VideoPath       : {res['VideoPath']}\")\n", "        print(f\"SrtContentObjects: {res['SrtContentObjects']}\")\n", "        print(f\"Description     : {res['Description']}\")\n", "        print(f\"SrtContent      : {res['SrtContent']}\")\n", "       \n", "        print(f\"Transcript      : {res['Transcript']}\")\n", "        print(f\"WordTimestamps  : {res['WordTimestamps']}\")\n", "        print(f\"AudioTags       : {res['AudioTags']}\")\n", "        print(f\"Diarization     : {res['Diarization']}\")\n", "        #print(f\"Embeddings      : {res['Embeddings']}\")\n", "        print(f\"Relevance Score : {res['relevance_score']:.4f}\")\n", "        print(\"=================================\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "ffe4d52b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting nltk\n", "  Downloading nltk-3.9.1-py3-none-any.whl.metadata (2.9 kB)\n", "Requirement already satisfied: click in ./.venv/lib/python3.10/site-packages (from nltk) (8.2.1)\n", "Requirement already satisfied: joblib in ./.venv/lib/python3.10/site-packages (from nltk) (1.5.1)\n", "Requirement already satisfied: regex>=2021.8.3 in ./.venv/lib/python3.10/site-packages (from nltk) (2025.7.34)\n", "Requirement already satisfied: tqdm in ./.venv/lib/python3.10/site-packages (from nltk) (4.67.1)\n", "Downloading nltk-3.9.1-py3-none-any.whl (1.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.5/1.5 MB\u001b[0m \u001b[31m26.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: nltk\n", "Successfully installed nltk-3.9.1\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m25.1.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.2\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["! pip install nltk"]}, {"cell_type": "code", "execution_count": 5, "id": "fa498643", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["huggingface/tokenizers: The current process just got forked, after parallelism has already been used. Disabling parallelism to avoid deadlocks...\n", "To disable this warning, you can either:\n", "\t- Avoid using `tokenizers` before the fork if possible\n", "\t- Explicitly set the environment variable TOKENIZERS_PARALLELISM=(true | false)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: pyodbc in ./.venv/lib/python3.10/site-packages (5.2.0)\n", "Requirement already satisfied: sentence-transformers in ./.venv/lib/python3.10/site-packages (5.0.0)\n", "Requirement already satisfied: numpy in ./.venv/lib/python3.10/site-packages (2.2.6)\n", "Requirement already satisfied: transformers<5.0.0,>=4.41.0 in ./.venv/lib/python3.10/site-packages (from sentence-transformers) (4.54.1)\n", "Requirement already satisfied: tqdm in ./.venv/lib/python3.10/site-packages (from sentence-transformers) (4.67.1)\n", "Requirement already satisfied: torch>=1.11.0 in ./.venv/lib/python3.10/site-packages (from sentence-transformers) (2.7.1)\n", "Requirement already satisfied: scikit-learn in ./.venv/lib/python3.10/site-packages (from sentence-transformers) (1.7.1)\n", "Requirement already satisfied: scipy in ./.venv/lib/python3.10/site-packages (from sentence-transformers) (1.15.3)\n", "Requirement already satisfied: huggingface-hub>=0.20.0 in ./.venv/lib/python3.10/site-packages (from sentence-transformers) (0.34.3)\n", "Requirement already satisfied: Pillow in ./.venv/lib/python3.10/site-packages (from sentence-transformers) (11.3.0)\n", "Requirement already satisfied: typing_extensions>=4.5.0 in ./.venv/lib/python3.10/site-packages (from sentence-transformers) (4.14.1)\n", "Requirement already satisfied: filelock in ./.venv/lib/python3.10/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (3.18.0)\n", "Requirement already satisfied: packaging>=20.0 in ./.venv/lib/python3.10/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (25.0)\n", "Requirement already satisfied: pyyaml>=5.1 in ./.venv/lib/python3.10/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (6.0.2)\n", "Requirement already satisfied: regex!=2019.12.17 in ./.venv/lib/python3.10/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (2025.7.34)\n", "Requirement already satisfied: requests in ./.venv/lib/python3.10/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (2.32.4)\n", "Requirement already satisfied: tokenizers<0.22,>=0.21 in ./.venv/lib/python3.10/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (0.21.4)\n", "Requirement already satisfied: safetensors>=0.4.3 in ./.venv/lib/python3.10/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (0.5.3)\n", "Requirement already satisfied: fsspec>=2023.5.0 in ./.venv/lib/python3.10/site-packages (from huggingface-hub>=0.20.0->sentence-transformers) (2025.7.0)\n", "Requirement already satisfied: hf-xet<2.0.0,>=1.1.3 in ./.venv/lib/python3.10/site-packages (from huggingface-hub>=0.20.0->sentence-transformers) (1.1.5)\n", "Requirement already satisfied: sympy>=1.13.3 in ./.venv/lib/python3.10/site-packages (from torch>=1.11.0->sentence-transformers) (1.14.0)\n", "Requirement already satisfied: networkx in ./.venv/lib/python3.10/site-packages (from torch>=1.11.0->sentence-transformers) (3.4.2)\n", "Requirement already satisfied: jinja2 in ./.venv/lib/python3.10/site-packages (from torch>=1.11.0->sentence-transformers) (3.1.6)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in ./.venv/lib/python3.10/site-packages (from sympy>=1.13.3->torch>=1.11.0->sentence-transformers) (1.3.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in ./.venv/lib/python3.10/site-packages (from jinja2->torch>=1.11.0->sentence-transformers) (3.0.2)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in ./.venv/lib/python3.10/site-packages (from requests->transformers<5.0.0,>=4.41.0->sentence-transformers) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in ./.venv/lib/python3.10/site-packages (from requests->transformers<5.0.0,>=4.41.0->sentence-transformers) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in ./.venv/lib/python3.10/site-packages (from requests->transformers<5.0.0,>=4.41.0->sentence-transformers) (2.5.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in ./.venv/lib/python3.10/site-packages (from requests->transformers<5.0.0,>=4.41.0->sentence-transformers) (2025.8.3)\n", "Requirement already satisfied: joblib>=1.2.0 in ./.venv/lib/python3.10/site-packages (from scikit-learn->sentence-transformers) (1.5.1)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in ./.venv/lib/python3.10/site-packages (from scikit-learn->sentence-transformers) (3.6.0)\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m25.1.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.2\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["! pip install pyodbc sentence-transformers numpy"]}, {"cell_type": "code", "execution_count": 22, "id": "5e595386", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Row: (1, 'https://strorageaccount123.blob.core.windows.net/videosearch/8c6e7a51-044c-46fd-a278-1840fd820173.mp4', \"The video begins with a close-up of a plate of pasta being mixed, revealing its creamy texture and herbs. The text 'Agli o e Olio' (Garlic oil and olive oil) appears on the screen, indicating the ingredients used in the dish. A hand is seen cutting garlic cloves on a wooden cutting board next to a jar of red pepper flakes and a blue carton. The scene transitions to another close-up of a pot where olive oil is poured into it from a blue container. Chopped garlic and red pepper flakes are added to the hot oil, creating a sizzling sound as they cook. The mixture is stirred with a wooden spoon, blending the flavors together. Next, spaghetti is cooked in a pot of boiling water while the sauce simmers on the stove. Milk is poured into the simmering sauce, followed by the addition of freshly chopped parsley. The spaghetti is then lifted out of the pot with tongs and placed onto a plate, mixing well with the sauce. Finally, more milk is added to the simmering sauce, and grated cheese is sprinkled over the pasta. The video concludes with a close-up of the finished dish, showcasing the creamy texture and vibrant green herbs.\", \"What's up mob? I think this is my death row meal. Aglio e olio e peperoncino with lots of lemon juice and parsley. Sorry about my pronunciation, let's get to it. First up, finely slice your garlic. I go in with six to eight cloves and then slice up your parsley. Next, add a very big glug of quality olive oil to the pan. Then while the oil is warming up, add the garlic. You don't want the oil to be too hot at this point as you don't want the garlic to burn. Add a pinch of chili flakes and stir everything together. Season with salt. Past the time, generously season a pan of boiling water and add your linguine. Now this part is key. To stop the garlic from browning, add a big splash of pasta water to the pan and stir it in. This halts the frying process and starts to create a lovely emulsion. When your pasta is al dente, add it straight from the boiling water to the saucepan. Toss it through, adding small splashes of pasta water until it all comes together. Add a big squeeze of lemon juice, stir in your parsley and you're done. Serve onto warm plates topped with more parsley, lemon juice and olive oil and enjoy mob.\")\n", "Row: (2, 'https://strorageaccount123.blob.core.windows.net/videosearch/2ec97da4-5178-47ce-b252-51d6562775e2.mp4', 'The video showcases a woman in a kitchen, wearing a black t-shirt with the text \"I ❤️ Cooking.\" She begins by spreading peanut butter on slices of bread. The scene transitions to her cutting the bread into smaller pieces and placing them on a plate. Next, she adds strawberries and granola to the bread before rolling it up tightly. Finally, she cuts the roll in half, revealing the filling inside.', 'Roll ups are super quick and easy to prepare. Take any of your favorite bread and roll. Spread peanut butter, Nutella then some fruits, a little bit of granola, cereal to get some crunch here and then simply roll it. Quick and easy. If desired you can cut them into half for see through.')\n", "Row: (3, 'https://strorageaccount123.blob.core.windows.net/videosearch/765165a5-d841-4dad-b6d2-d42af430f092.mp4', \"The video takes place in an airport, featuring a man wearing a white t-shirt and carrying a black shoulder bag. He stands at a counter with a modern ceiling design visible behind him. The text 'Airport English' appears on the screen, setting the context for the conversation that follows.\\n\\nInitially, the man holds a phone while another person hands over his passport. The text 'Hello, I'm flying to Los Angeles at 2 o'clock in the afternoon' appears as he confirms this information by saying 'Sure.' The scene continues with the same background and the man now holding his passport again. The text 'Your flight's been delayed about 30 minutes' is displayed, indicating the delay of his flight.\\n\\nAs the narrative progresses, the man asks if it's okay for him to choose a seat, asking 'Well is it okay if I choose a seat?' He then inquires if there are any aisle seats available, to which the response is 'Sure no problem.' After confirming the availability, he requests check-in luggage but specifies 'No, just one carry-on bag.'\\n\\nIn the final segment, the man receives his boarding pass from someone off-screen, with the text 'Your boarding time is at 2:15 PM and your gate number is T27' appearing on the screen. He expresses gratitude with 'Thank you so much!' while still holding both his passport and the boarding pass.\\n\\nThroughout the video, the background remains consistent, showing various airport signs and counters, maintaining a coherent and continuous airport environment.\", \"Hello, I'm flying to Los Angeles at 2 o'clock in the afternoon. Can I see your passport, please? Sure. Do you know if the flight is on time? Your flight's been delayed about 30 minutes. Well, is it okay if I choose a seat? Do you have any aisle seats available? Let me check. Sure, no problem. Do you have any check-in luggage? No, just one carry-on bag. Okay, here's your boarding pass. Your boarding time is at 2.15 p.m. and your gate number is T27. Okay, and which way is security check? Right behind you to the left, sir. Thank you so much.\")\n", "Row: (4, 'https://strorageaccount123.blob.core.windows.net/videosearch/3c4a4877-8ddf-4e79-84f5-0926d11f4997.mp4', \"The video begins with a man in a suit standing at a podium, delivering a speech. He is followed by an interview segment featuring <PERSON><PERSON><PERSON><PERSON>, who shares her story of overcoming rejection for the first Harry Potter book. The narrative then shifts to <PERSON>'s early struggles on his high school basketball team, highlighting his perseverance despite repeated failures. A man in a gray suit stands at a podium again, emphasizing the importance of learning from failures and not letting them define one's success. The scene transitions to another man lying on a couch, contemplating issues related to troublemaking and grades. A young boy is shown studying diligently at a desk, illustrating the need for more effort when facing difficulties. Finally, a child runs joyfully across grassy fields, symbolizing growth through hard work. The video concludes with a serene beach scene where a person runs along the shoreline, reinforcing the message that becoming good takes time and dedication.\", \"Some of the most successful people in the world are the ones who've had the most failures. <PERSON><PERSON><PERSON><PERSON>, who wrote <PERSON>, her first Harry Potter book was rejected 12 times before it was finally published. <PERSON> was cut from his high school basketball team. He lost hundreds of games and misses thousands of shots during his career. But he once said, I have failed over and over and over again in my life, and that's why I succeed. These people succeeded because they understood that you can't let your failures define you. You have to let your failures teach you. You have to let them show you what to do differently the next time. So if you get into trouble, that doesn't mean you're a troublemaker. It means you need to try harder to act right. If you get a bad grade, that doesn't mean you're stupid. It just means you need to spend more time studying. No one's born being good at all things. All right. You become good at things through hard work.\")\n", "Row: (5, 'https://strorageaccount123.blob.core.windows.net/videosearch/b856699b-6f96-4136-b422-7adad02c93dc.mp4', \"The video begins with a receptionist at the front desk of a hotel, dressed in a black suit and white shirt. The scene transitions to an exterior shot of a building before shifting back to the reception area where a man in a dark suit approaches the counter. He engages in conversation with the receptionist, who responds with 'Good morning. Welcome to the Transnational Hotel.' A series of interactions follows as the man checks into the hotel, discussing various amenities and services. Text overlays provide instructions for the guest. Towards the end, he receives his room key and is informed about the bellboy's service. The final scenes show the guest leaving and thanking the staff, concluding the video.\", \"Good morning, welcome to the Transnational Hotel. What can I do for you? Good morning, my name is <PERSON>. I have a reservation for a single room for three nights. Alright Mr. <PERSON>, let me pull up your reservation. I can't seem to find a record of your booking. Did you book the room directly through us or do you use a hotel reservation service or a travel agent? I booked it directly through you. I've already also paid a deposit on the first night. I have a reservation number if that helps. Yeah sure, can I see that please? Thank you. Oh I see. Maybe there was a glitch with the booking system. Well, we don't have any more single rooms available with the exception of one adjoined room. But you would then be right next door to a family with children, which might get noisy. But that's not a problem. I can upgrade you to one of our business suites. Okay. They all come with jacuzzis. Oh that sounds nice. But how much more is that going to cost? That would of course be at no extra charge to you. Oh, well thank you. My pleasure. What about the wireless internet? Oh, it's really easy. This is your access code and instructions on how to use it. If you have any problems, feel free to call the front desk. And this is a list of all the hotel amenities, like the gym and the indoor pool. Oh, thank you very much. You're welcome. Has the valet already taken your car or will you be able to use it? I don't have a car. I took a taxi direct from the airport. Oh, alright. Could I have some form of ID please? And could you just fill out this registration form? Sure. Here's my driver's license. Thank you. Oh, you're from San Francisco. Yes, I am. All the way from the west coast. I hope you had a good trip. Yes, I did. Thank you. The flight was long, but it was smooth and I slept almost the whole way. Oh, and is this your first time in the Big Apple? Yes, it is. I have a business conference to attend, but I'm looking forward to getting some sightseeing done. Oh, I'm looking forward to it. Well, I'd be more than happy to give you some sightseeing tips if you need any. Thank you. Alright, I've got you all checked into your room. This is your room key. You're in room 653. Just take the elevator on the right up to the sixth floor. When you get off the elevator, turn right. Your room is at the end of the corridor on the left-hand side. Just leave your suitcase here and the bellboy will bring it up. Great. Well, thank you very much. If you need anything, please feel free to dial the front desk. Enjoy your stay. Thank you. You're welcome.\")\n", "Row: (6, 'https://strorageaccount123.blob.core.windows.net/videosearch/29203bd3-d475-4642-8437-e0024cb4c33d.mp4', 'The video opens with a man in a blue shirt and tie speaking to the camera, discussing job listings on a computer screen. The screen displays various job positions such as \"3097 - Cybersecurity Analyst\" and \"3055 - Systems Analyst,\" along with details like dates of birth and salaries. The man explains how an AI system automatically invites applicants for these jobs based on their resume database. He then shows a list of job invitations sent by users named \"arvind,\" \"sa<PERSON><PERSON>,\" and others, detailing the date of invitation and the number of applications received.\\n\\nNext, the video focuses on a specific job titled \"3099 Unravens DB Scoring Tags.\" The man highlights different candidates invited through this process, including \"<PERSON>,\" \"<PERSON><PERSON>,\" and others, all scoring above 50 points. He emphasizes that the AI system prioritizes high-scoring candidates while excluding those below the threshold.\\n\\nThroughout the video, the man continues to engage with the audience, pointing out various aspects of the job search process, including the criteria used to determine invitees and the importance of meeting minimum score requirements. The video concludes with the man summarizing the key points discussed, reinforcing the benefits of using an automated system for job matching and application tracking.', \"Here is the new feature. It's called Invite by AI Matching. What it does is when you click this, it says auto-invite job applicants. Are you sure you want to schedule in relation to all eligible applicants for this job? When it says schedule, it kicks out schedule here. Then it's looking into nano database as well as resume database. As I said, we have resume, 127,000 resumes. It's going to schedule it. Once it is processed, we're going to have... Let's see. Let's look at it here. Let's see. They've done a manual here. Yeah, you're going to get like this. Auto-invite job resume. So these are automated. All these people received, automated email. And they get an email to apply to the job. So it matched with the score and they get it. Let's refresh this. See if it's run anything. Yeah, it did not. Not yet. So because this is already run, it's going to run automatically. So let's do a matching here. So yeah, they invited everybody. But these are the lowest scores. Let's look at this one. Yeah, they're unrated manually, everybody. Oh, there are a couple of them, but low score. These are all low score. How does it know who to invite automatically? Yeah, it's going to do it. So the score has to be 50 and above. So this is 29, 36. It should invite these two people because Azure says Azure's score is high. But non-none score is low. Then it should send out an invitation. Let's wait for... So it should look at score. Yeah, it did send somebody. Yeah, here you go. It sent to all these people. And that's... Is that people from our database or it's the full database or it's only people who applied? These are the resume database. Means these are the... This company database, their own people. They have a resume. So they have a resume. Now system find a match and also it automatically send the invitation to apply.\")\n", "Row: (7, 'https://strorageaccount123.blob.core.windows.net/videosearch/eb0cc947-d5ba-498d-959a-a97733186ae3.mp4', 'The video depicts a meeting between two men seated at a wooden table in an office setting. One man is wearing glasses and a black polo shirt, while the other has gray hair and also wears a black polo shirt. A smartphone rests on the table between them. The background features blue walls with white trim and a large whiteboard mounted on one of the walls. At various points, the men engage in conversation using hand gestures to emphasize their points. They occasionally look directly at each other or towards the camera. Additionally, there are instances where they use laptops placed on the table.', \"If you're ignoring AI, you might be put out of work. I don't care what your history, I don't care what your grades, your maybe scholar, PhD. Are you solving a real business problem? Every time I had a job within three weeks and I barely made an outbound call and people said, well, how, how do you get that? And it was a better job. They go, you get laid off up. How does that happen? Okay, welcome back to the Ignorant Podcast. We're here with <PERSON><PERSON> and <PERSON>. They use AI. So basically the idea was if you're ignoring AI, you might be put out of work. But if you're embracing it and using it and realizing how to morph where your skill set is, there'll be room for you. Compliments. Yeah. It's a tool you got to get in. I think people has misunderstood AI. The education part of this, how you are adaptable, how you are open to new technologies. I think a lot of people are very hesitant because part of this, you know, AI is what are you here? Somebody's writing, don't know what exactly I can do. And also we have a lot of distraction with all these models and there's a dark fight out there. So, so in technology. So what is the other question you have? I have a question for you Madhu. It's like with that, because, you know, AI is changing the job market and the job, like, you know, scope. Where do you kind of see, like if people are still getting laid off, obviously that's still very scary. I suppose. So Bumble, which is an Austin company, that was like a 30%. So if, you know, we're moving away from certain jobs, what jobs do you kind of see opening or what do you kind of see in the job market? You have been involved in our meetings. We welcome interns to come in to our companies and see the difference, what we see. The, I don't care your resume anymore. I mean, I saw an article this morning, how to write a resume here is a tool. Stop doing that. So I'm going to ask you a question. So tell the world, tell me, go back to the cover letter, right? Give me your cover letter. In one paragraph, there is a project you have done can help me. Right. And I can, next day you have a job. I don't care about industry. I don't care about your grades. You're maybe a scholar, PhD. Are you solving a real business problem? Are you helping my company and companies are trying to. I hire you. Why do we need to hire you? You need to justify. What do you bring it to the table? Are you open to learning and creating solutions? I think we are already creative all now. Oh, for sure. That clear. For sure. It's a paper trail work days gone that can replace the AI. Where our, we see is, yeah, he's a, I know it's hard to say, but everybody has talent. But I don't think so. They use it. Well, they get distracted with, oh, I need to get framed resume. I need to have keywords in a resume. Right. We are in that space where like, okay, give me a one project you delivered out there. You put it out there and where I can look at it and see if it's giving any value to us. Oh, I didn't think about it. How many times we talk in a meeting? Oh, I didn't think about this. That's what I expect in new generation is they have. Amazing talent. I think they have their last focus. Well, and the other thing is, is that with the job market, the way it is now, your reputation, your brand and your network as an individual are going to be more important than ever. And part of that is how do you tell a particular employer what the projects you've worked on and done it. But also as the employer, if I call you and say, Hey, you need to meet Matt. He's perfect for what you're working on. You're more apt to talk to him and possibly hiring. And yet the younger generation is more apt not to go to networking events, not to join organizations, not to really go out even socialize with people in their industry. They don't go to national conferences in their industry. And then they are like, oh, well, everybody gets their job through their network, but I don't have a network. And it's like, well, you have to create that yourself. And I call it human interaction, H.I. And I talk about the fact that H.I. is more important in the world than any time before, because anybody can generate a resume. Yeah. You know, anyone can generate a resume. Now, if you can't write a resume using an A.I. tool in five minutes, then, you know, shame on you. But how do you get that resume in front of a hiring manager? How do you get that resume, you know, in front of people? And how do you know that when a job is open, someone's like, oh, my God, I just heard that Becky got laid off. She's fantastic. Let's hire her. Yeah. Earlier in my career, I got laid off three times because of companies, not anything I did. Companies that either entirely went out of business. Yeah. Or left Austin. Yeah. And every time I had a job within three weeks and I barely made an outbound call and people said, well, how? How do you get that? And it was a better job. They go, you get laid off up. How does that happen? And it was because I bought into this idea and I learned it from a gentleman named Harvey McKay, who wrote a famous book in the 90s called How to Swim with the Sharks Without Getting Eaten Alive. Yeah. And I met Harvey. He actually is the person who got me inspired to become a professional speaker, something that I do as well. And I've met him. I've known Harvey for a long time. But I read his books and I believed him. And he basically said that the friendships you build and the relationships you build and the reputation you build are going to be your secret weapon. And it doesn't happen in the first five or 10 years. No. If you fast forward from when I was in my 20s and 30s when I learned that to now, people always ask me, how did you get a job at ATC? I'm like, well, they were looking for somebody to reinvent it. Yeah. And half the board knew who I was. Yeah. And they asked me. Yeah. Exactly. What you just laid. We are doing a fit gap. If a role demands. You get shocked how many people apply not knowing what job, what company that. Are you applying to a job in a mass? Like not. I understand the desperation to need a job. But spend time. What this company does. Go to their website. I mean, in five minutes, you can get the gist of what a company does. Yeah. You got to know the company. You got to know what you are pitching to the company. Yeah. What exactly you fit in, why you are the best guy. You need to articulate that really. I don't care. Oh, nobody reads all the pages. We get some resumes, 20 pages. I don't blame them. They're very proud of what they've done. We are all proud of. We're not going to read it. Yeah. Nobody's going to read your 20 pages. What we need is that cover letter tells me what you can bring it to our company. Our region. You show me what we are doing. You can do different. And I flip that around to the companies too. You know, it used to be companies wanted to be really active in the community. They wanted to support the Austin Technology Council. They wanted to support the Chamber of Commerce or whatever group. And they wanted their logos to be out there that they were part of the community because they knew that their employees appreciated seeing the support of the community. But they also knew that when they were looking for employees, if I've never heard of the company, I could totally miss the opportunity and not even apply. But if the company builds a reputation and the leadership of the company and you do a great job of this, you know, you show up at different events around town. I would love to see because that's where I pick on some people. Well, right. But that's how you find people. But also, then if someone comes to me and says, hey, have you heard of this company? I'm like, oh, that was great. You've got to go. If you stay behind the wall and just build your product and somebody comes to me and says, oh, they're looking for somebody. And I go, I don't know who they are. A great person may not be as inspired to apply. And so I think that the people have to build a reputation in their community and in their industry. And I think companies have to go back to the days of building a reputation in their community. They got to be there. It's a both ways. Yeah, it's a both ways. Another one thing we have done in Onanu is we welcome anybody who wants an internship. I know we are a bootstrapping it. We don't pay. So what do we give? Three months time to prove them. Why? Because we know that you need to hire them. Some kind of a job. That's what we have done with a lot of people. Some people came and they said, OK, they didn't. We are not looking for paper files. Right. We don't guide you. We'll give you a project. You come and beat us down. This is what you should do. That's what we have. I think that is where the market is going in the jobs now is every company with the AI. Because you have now you have we crossed information is now we are in technology age. We have the AI tools for everything you can do. Now, what you can do with the AI tools, the information you already have, just a click of a button. Right. And people are scared of the AI tools. But, you know, we're still in the infancy. Yeah. So we're not even started. We're not even started. Right. I mean, ChatGPT was two and a half years ago. Yeah. And, you know, I credit a friend of mine who ChatGPT had released like three days before. And he said, you got to look at this. Not just because of your role with ATC, but your role as a professional speaker. You've got to learn what's going on because this is going to affect things. So I was accidentally early exposed to it. And, you know, it's come a long way in two and a half years. It doesn't hallucinate as much. You know, it doesn't use as many goofy phrases. However, it's still in its infancy. And so people who are like, oh, AI can do the writing. No, you can't trust it. If you're looking for AI to do coding or writing, you can't trust it to give you the final product. You still need humans who are going to tweak it. I know somebody who's a coder. And they use AI. But he said, look, it's not writing the best code. So he treats it in one of two ways. He either treats it as a junior coder who he has to review everything they do. Yeah. Or as the most senior creative coder he's ever seen. Yeah. Who's on LSD. Yeah. Which means he still has to go back and review everything that it does. And then it's a great productivity tool. But people need to, for the time being, see it as just that. Yeah. And then, oh, it's going to take away my job. No. If it makes you more productive, you're going to keep your job longer and you're going to get more jobs if you know how to use these tools. And it's not just, you know, LLMs. There's so much more coming out and becoming more ubiquitous in the world of AI. And then when we get to quantum computing, if they can figure that out, God save us all, the changes are going to go even faster. Yeah. And I think, like, something really that you say, like, the younger generation, like, it's kind of scary to go out and network, you know? Like, we're not used to it. So I'm going to ask you. I'm going to throw that back at you, Max. Why is it scary to go network? Are people like Babu and I, are we mean? Well, because, like, this is the most, like, crazy part about all of this to me is that, like, we went to school. We're learning this stuff, like accounting. We're learning other stuff. And then we're out now and it's four years after, you know, like two years after AI comes out. There's no one, there's no educational, you know, like, who's the best at doing AI? They started learning three, four years ago. You know, so that's why it's scary because you're going out and you're not classically trained. I mean, the more you go out, the more people you meet, the more network you have. And it's also why you can see, like, the rise of, like, YouTube. Like, I just looked it up. It's three, almost 400,000 people make a living off of platforms like YouTube, which is just a large network that's deployed digitally. And they, you know, so because it's just so interesting, like, there's no education. Who's going to be the best? Like, can you apply to a job at a company? You could have almost no experience, no education. But if you have three or four projects that you're successful with, then you're going to solve a problem at that company. Right. You're going to get the job, you know, because it is just so new and there's no education. But why the scary to go network to find these connections and open those doors? Well, like, you expect one thing and then you get another, you know. So it's like everybody is living in a world where it's like we don't really know how to use AI. Right. See, the personality, I came from a, you know, very shy, you know, I opened up. I learned in the U.S. being staying this long in Austin. I never used to talk up. You have to get up. You have to get out of your comfort zone. Right. The only way to get off the continent, two things you have to do. Either you go all in or you have one glass of beer and then get a little bit out of shyness and then go and talk. You have to start with strangers. And it's a learned skill. If you say, I'm going to go to two networking events a week for the next six months, at the end of six months, you're going to be like, I can talk to anybody. I can go do this. I just go and break into some group and stand there. Yeah. Well, and don't you don't need to do anything. What I say is like back to your generation, a lot of people grew up with everything was digital. You talk to your friends via text, you know, you hang out and, you know, and chat via the screen. And here's what I tell them. We have to remember, though, is that all opportunities in life and this will not change no matter what the technology does. All opportunities in life come from people. Yeah. And so earlier on, if you can collect people in a good way, if you can collect relationships, not like Courtney and I, not like, ha ha ha, what can they do for me? But if you can get to know people and build relationships with people and show them through actions over five years, 10 years, etc., then you're a doer. You care about the community. You're someone who helps other people. You're not just out for yourself. What's going to happen is, is opportunities are going to come up. People are going to pull you in. But if you don't do those things, they don't know who you are. Even if they would want to pull you in, they don't know you. They can't do anything. So I try to teach younger people, you know, look at everything good that's happened in your life. It's happened because of a person. You know, somehow you can trace it back to people. And the more you can connect in a community like Austin, and that's why we have to work hard as a community to keep that vibe of connectivity, of community. Because if you can still reach people and share with people, you know, not everybody's going to like you and that's fine. But if you just keep trying, and when people say, oh, I don't like to go to networking events because I go once and nothing happens. Well, no, you've got to go to that same organization like for a year. Not before you're going to get benefit, but before people even notice that you're there. You've got to keep showing up. You've got to keep showing up. And after a while, they're like, I see you around. What do you do? And then you start to learn. Yeah. The more persistent you are in showing up is going to value. Another one is we know the stats. 80% of the jobs in America are filled by repros. So you have left with 20%. You are. And most of the, unfortunately, these federal laws, compliance, HR laws, and state laws create a lot of problems. And they create a big mess. Oh, you've got to post a job two weeks before, you know, it's all this. I don't know if it's really helping anybody. Well, it depends. Right. But in a lot of cases, what happens is if they're taking somebody internally or they're taking someone through. I already know who they are. They post the job. I can't tell you how many people tell me they apply to 100 jobs and only hear back from two, three companies. It's like you're applying to a job. You're not even getting a thing back that says, thank you for applying. We've gone a different direction. You're just never, it's an abyss. I'll tell you why people don't respond. There is a compliance. They could be you are pulled into a lawsuit. Right. I think it's a part of, because the communication sometimes can turn on your, against you. That's true. So companies are scared to do anything. Right. It's sensitive to somebody. He's desperate. He needs a job. And he puts it. Then you want to help him to send a message. That message can turn into your side. So there is always how you see the world, how you can say. For me, like, as you said, know people, talk to people, connect with people. You know, your local community is a big thing. Yep. You, if you, you, if you are a person they like you, you'll get a job. Well, I think it's your local community. So it's things like technology council. It's things like chamber, a whole bunch of other organizations. You know, you can't belong to everything and you can't go to everything. So pick two or three groups. Yeah. And go to those. Regular. Regular. Because most of us only have one event a month. Yeah. So pick three. You've got three things to go to in a month. Yeah. One that does a breakfast, one that does a lunch, one that does a happy hour. And always go to those. And then you can drop in on other ones if there's a good topic. But the other thing is, is what is your industry? Yeah. And what type of industry groups, both locally and nationally exist. Yeah. I mean, the, the people who get really involved with, you know, the, the, the, the, the, the, the, the, the, the, the. You know if there are a high, if there are hiring like CIO and the a property with real estate marketing. Yeah. Does a beardamine comenzar beach and O supposed to be like a beach commercial to describe not in저i a rock star and make a house sign Post because o supposed to be like I only think aboutna James altogether and not others. Because I think you've got to keep that shit to yourself. That's pretty high. Like let's look at... Okay.\")\n", "Row as dict: {'Id': 1, 'VideoPath': 'https://strorageaccount123.blob.core.windows.net/videosearch/8c6e7a51-044c-46fd-a278-1840fd820173.mp4', 'Description': \"The video begins with a close-up of a plate of pasta being mixed, revealing its creamy texture and herbs. The text 'Agli o e Olio' (Garlic oil and olive oil) appears on the screen, indicating the ingredients used in the dish. A hand is seen cutting garlic cloves on a wooden cutting board next to a jar of red pepper flakes and a blue carton. The scene transitions to another close-up of a pot where olive oil is poured into it from a blue container. Chopped garlic and red pepper flakes are added to the hot oil, creating a sizzling sound as they cook. The mixture is stirred with a wooden spoon, blending the flavors together. Next, spaghetti is cooked in a pot of boiling water while the sauce simmers on the stove. Milk is poured into the simmering sauce, followed by the addition of freshly chopped parsley. The spaghetti is then lifted out of the pot with tongs and placed onto a plate, mixing well with the sauce. Finally, more milk is added to the simmering sauce, and grated cheese is sprinkled over the pasta. The video concludes with a close-up of the finished dish, showcasing the creamy texture and vibrant green herbs.\", 'Transcript': \"What's up mob? I think this is my death row meal. Aglio e olio e peperoncino with lots of lemon juice and parsley. Sorry about my pronunciation, let's get to it. First up, finely slice your garlic. I go in with six to eight cloves and then slice up your parsley. Next, add a very big glug of quality olive oil to the pan. Then while the oil is warming up, add the garlic. You don't want the oil to be too hot at this point as you don't want the garlic to burn. Add a pinch of chili flakes and stir everything together. Season with salt. Past the time, generously season a pan of boiling water and add your linguine. Now this part is key. To stop the garlic from browning, add a big splash of pasta water to the pan and stir it in. This halts the frying process and starts to create a lovely emulsion. When your pasta is al dente, add it straight from the boiling water to the saucepan. Toss it through, adding small splashes of pasta water until it all comes together. Add a big squeeze of lemon juice, stir in your parsley and you're done. Serve onto warm plates topped with more parsley, lemon juice and olive oil and enjoy mob.\"}\n", "Row as dict: {'Id': 2, 'VideoPath': 'https://strorageaccount123.blob.core.windows.net/videosearch/2ec97da4-5178-47ce-b252-51d6562775e2.mp4', 'Description': 'The video showcases a woman in a kitchen, wearing a black t-shirt with the text \"I ❤️ Cooking.\" She begins by spreading peanut butter on slices of bread. The scene transitions to her cutting the bread into smaller pieces and placing them on a plate. Next, she adds strawberries and granola to the bread before rolling it up tightly. Finally, she cuts the roll in half, revealing the filling inside.', 'Transcript': 'Roll ups are super quick and easy to prepare. Take any of your favorite bread and roll. Spread peanut butter, Nutella then some fruits, a little bit of granola, cereal to get some crunch here and then simply roll it. Quick and easy. If desired you can cut them into half for see through.'}\n", "Row as dict: {'Id': 3, 'VideoPath': 'https://strorageaccount123.blob.core.windows.net/videosearch/765165a5-d841-4dad-b6d2-d42af430f092.mp4', 'Description': \"The video takes place in an airport, featuring a man wearing a white t-shirt and carrying a black shoulder bag. He stands at a counter with a modern ceiling design visible behind him. The text 'Airport English' appears on the screen, setting the context for the conversation that follows.\\n\\nInitially, the man holds a phone while another person hands over his passport. The text 'Hello, I'm flying to Los Angeles at 2 o'clock in the afternoon' appears as he confirms this information by saying 'Sure.' The scene continues with the same background and the man now holding his passport again. The text 'Your flight's been delayed about 30 minutes' is displayed, indicating the delay of his flight.\\n\\nAs the narrative progresses, the man asks if it's okay for him to choose a seat, asking 'Well is it okay if I choose a seat?' He then inquires if there are any aisle seats available, to which the response is 'Sure no problem.' After confirming the availability, he requests check-in luggage but specifies 'No, just one carry-on bag.'\\n\\nIn the final segment, the man receives his boarding pass from someone off-screen, with the text 'Your boarding time is at 2:15 PM and your gate number is T27' appearing on the screen. He expresses gratitude with 'Thank you so much!' while still holding both his passport and the boarding pass.\\n\\nThroughout the video, the background remains consistent, showing various airport signs and counters, maintaining a coherent and continuous airport environment.\", 'Transcript': \"Hello, I'm flying to Los Angeles at 2 o'clock in the afternoon. Can I see your passport, please? Sure. Do you know if the flight is on time? Your flight's been delayed about 30 minutes. Well, is it okay if I choose a seat? Do you have any aisle seats available? Let me check. Sure, no problem. Do you have any check-in luggage? No, just one carry-on bag. Okay, here's your boarding pass. Your boarding time is at 2.15 p.m. and your gate number is T27. Okay, and which way is security check? Right behind you to the left, sir. Thank you so much.\"}\n", "Row as dict: {'Id': 4, 'VideoPath': 'https://strorageaccount123.blob.core.windows.net/videosearch/3c4a4877-8ddf-4e79-84f5-0926d11f4997.mp4', 'Description': \"The video begins with a man in a suit standing at a podium, delivering a speech. He is followed by an interview segment featuring <PERSON><PERSON><PERSON><PERSON>, who shares her story of overcoming rejection for the first Harry Potter book. The narrative then shifts to <PERSON>'s early struggles on his high school basketball team, highlighting his perseverance despite repeated failures. A man in a gray suit stands at a podium again, emphasizing the importance of learning from failures and not letting them define one's success. The scene transitions to another man lying on a couch, contemplating issues related to troublemaking and grades. A young boy is shown studying diligently at a desk, illustrating the need for more effort when facing difficulties. Finally, a child runs joyfully across grassy fields, symbolizing growth through hard work. The video concludes with a serene beach scene where a person runs along the shoreline, reinforcing the message that becoming good takes time and dedication.\", 'Transcript': \"Some of the most successful people in the world are the ones who've had the most failures. <PERSON><PERSON><PERSON><PERSON>, who wrote <PERSON>, her first Harry <PERSON> book was rejected 12 times before it was finally published. <PERSON> was cut from his high school basketball team. He lost hundreds of games and misses thousands of shots during his career. But he once said, I have failed over and over and over again in my life, and that's why I succeed. These people succeeded because they understood that you can't let your failures define you. You have to let your failures teach you. You have to let them show you what to do differently the next time. So if you get into trouble, that doesn't mean you're a troublemaker. It means you need to try harder to act right. If you get a bad grade, that doesn't mean you're stupid. It just means you need to spend more time studying. No one's born being good at all things. All right. You become good at things through hard work.\"}\n", "Row as dict: {'Id': 5, 'VideoPath': 'https://strorageaccount123.blob.core.windows.net/videosearch/b856699b-6f96-4136-b422-7adad02c93dc.mp4', 'Description': \"The video begins with a receptionist at the front desk of a hotel, dressed in a black suit and white shirt. The scene transitions to an exterior shot of a building before shifting back to the reception area where a man in a dark suit approaches the counter. He engages in conversation with the receptionist, who responds with 'Good morning. Welcome to the Transnational Hotel.' A series of interactions follows as the man checks into the hotel, discussing various amenities and services. Text overlays provide instructions for the guest. Towards the end, he receives his room key and is informed about the bellboy's service. The final scenes show the guest leaving and thanking the staff, concluding the video.\", 'Transcript': \"Good morning, welcome to the Transnational Hotel. What can I do for you? Good morning, my name is <PERSON>. I have a reservation for a single room for three nights. Alright Mr. <PERSON>, let me pull up your reservation. I can't seem to find a record of your booking. Did you book the room directly through us or do you use a hotel reservation service or a travel agent? I booked it directly through you. I've already also paid a deposit on the first night. I have a reservation number if that helps. Yeah sure, can I see that please? Thank you. Oh I see. Maybe there was a glitch with the booking system. Well, we don't have any more single rooms available with the exception of one adjoined room. But you would then be right next door to a family with children, which might get noisy. But that's not a problem. I can upgrade you to one of our business suites. Okay. They all come with jacuzzis. Oh that sounds nice. But how much more is that going to cost? That would of course be at no extra charge to you. Oh, well thank you. My pleasure. What about the wireless internet? Oh, it's really easy. This is your access code and instructions on how to use it. If you have any problems, feel free to call the front desk. And this is a list of all the hotel amenities, like the gym and the indoor pool. Oh, thank you very much. You're welcome. Has the valet already taken your car or will you be able to use it? I don't have a car. I took a taxi direct from the airport. Oh, alright. Could I have some form of ID please? And could you just fill out this registration form? Sure. Here's my driver's license. Thank you. Oh, you're from San Francisco. Yes, I am. All the way from the west coast. I hope you had a good trip. Yes, I did. Thank you. The flight was long, but it was smooth and I slept almost the whole way. Oh, and is this your first time in the Big Apple? Yes, it is. I have a business conference to attend, but I'm looking forward to getting some sightseeing done. Oh, I'm looking forward to it. Well, I'd be more than happy to give you some sightseeing tips if you need any. Thank you. Alright, I've got you all checked into your room. This is your room key. You're in room 653. Just take the elevator on the right up to the sixth floor. When you get off the elevator, turn right. Your room is at the end of the corridor on the left-hand side. Just leave your suitcase here and the bellboy will bring it up. Great. Well, thank you very much. If you need anything, please feel free to dial the front desk. Enjoy your stay. Thank you. You're welcome.\"}\n", "Row as dict: {'Id': 6, 'VideoPath': 'https://strorageaccount123.blob.core.windows.net/videosearch/29203bd3-d475-4642-8437-e0024cb4c33d.mp4', 'Description': 'The video opens with a man in a blue shirt and tie speaking to the camera, discussing job listings on a computer screen. The screen displays various job positions such as \"3097 - Cybersecurity Analyst\" and \"3055 - Systems Analyst,\" along with details like dates of birth and salaries. The man explains how an AI system automatically invites applicants for these jobs based on their resume database. He then shows a list of job invitations sent by users named \"arvind,\" \"<PERSON><PERSON><PERSON>,\" and others, detailing the date of invitation and the number of applications received.\\n\\nNext, the video focuses on a specific job titled \"3099 Unravens DB Scoring Tags.\" The man highlights different candidates invited through this process, including \"<PERSON>,\" \"<PERSON><PERSON>,\" and others, all scoring above 50 points. He emphasizes that the AI system prioritizes high-scoring candidates while excluding those below the threshold.\\n\\nThroughout the video, the man continues to engage with the audience, pointing out various aspects of the job search process, including the criteria used to determine invitees and the importance of meeting minimum score requirements. The video concludes with the man summarizing the key points discussed, reinforcing the benefits of using an automated system for job matching and application tracking.', 'Transcript': \"Here is the new feature. It's called Invite by AI Matching. What it does is when you click this, it says auto-invite job applicants. Are you sure you want to schedule in relation to all eligible applicants for this job? When it says schedule, it kicks out schedule here. Then it's looking into nano database as well as resume database. As I said, we have resume, 127,000 resumes. It's going to schedule it. Once it is processed, we're going to have... Let's see. Let's look at it here. Let's see. They've done a manual here. Yeah, you're going to get like this. Auto-invite job resume. So these are automated. All these people received, automated email. And they get an email to apply to the job. So it matched with the score and they get it. Let's refresh this. See if it's run anything. Yeah, it did not. Not yet. So because this is already run, it's going to run automatically. So let's do a matching here. So yeah, they invited everybody. But these are the lowest scores. Let's look at this one. Yeah, they're unrated manually, everybody. Oh, there are a couple of them, but low score. These are all low score. How does it know who to invite automatically? Yeah, it's going to do it. So the score has to be 50 and above. So this is 29, 36. It should invite these two people because Azure says Azure's score is high. But non-none score is low. Then it should send out an invitation. Let's wait for... So it should look at score. Yeah, it did send somebody. Yeah, here you go. It sent to all these people. And that's... Is that people from our database or it's the full database or it's only people who applied? These are the resume database. Means these are the... This company database, their own people. They have a resume. So they have a resume. Now system find a match and also it automatically send the invitation to apply.\"}\n", "Row as dict: {'Id': 7, 'VideoPath': 'https://strorageaccount123.blob.core.windows.net/videosearch/eb0cc947-d5ba-498d-959a-a97733186ae3.mp4', 'Description': 'The video depicts a meeting between two men seated at a wooden table in an office setting. One man is wearing glasses and a black polo shirt, while the other has gray hair and also wears a black polo shirt. A smartphone rests on the table between them. The background features blue walls with white trim and a large whiteboard mounted on one of the walls. At various points, the men engage in conversation using hand gestures to emphasize their points. They occasionally look directly at each other or towards the camera. Additionally, there are instances where they use laptops placed on the table.', 'Transcript': \"If you're ignoring AI, you might be put out of work. I don't care what your history, I don't care what your grades, your maybe scholar, PhD. Are you solving a real business problem? Every time I had a job within three weeks and I barely made an outbound call and people said, well, how, how do you get that? And it was a better job. They go, you get laid off up. How does that happen? Okay, welcome back to the Ignorant Podcast. We're here with <PERSON><PERSON> and <PERSON>. They use AI. So basically the idea was if you're ignoring AI, you might be put out of work. But if you're embracing it and using it and realizing how to morph where your skill set is, there'll be room for you. Compliments. Yeah. It's a tool you got to get in. I think people has misunderstood AI. The education part of this, how you are adaptable, how you are open to new technologies. I think a lot of people are very hesitant because part of this, you know, AI is what are you here? Somebody's writing, don't know what exactly I can do. And also we have a lot of distraction with all these models and there's a dark fight out there. So, so in technology. So what is the other question you have? I have a question for you Madhu. It's like with that, because, you know, AI is changing the job market and the job, like, you know, scope. Where do you kind of see, like if people are still getting laid off, obviously that's still very scary. I suppose. So Bumble, which is an Austin company, that was like a 30%. So if, you know, we're moving away from certain jobs, what jobs do you kind of see opening or what do you kind of see in the job market? You have been involved in our meetings. We welcome interns to come in to our companies and see the difference, what we see. The, I don't care your resume anymore. I mean, I saw an article this morning, how to write a resume here is a tool. Stop doing that. So I'm going to ask you a question. So tell the world, tell me, go back to the cover letter, right? Give me your cover letter. In one paragraph, there is a project you have done can help me. Right. And I can, next day you have a job. I don't care about industry. I don't care about your grades. You're maybe a scholar, PhD. Are you solving a real business problem? Are you helping my company and companies are trying to. I hire you. Why do we need to hire you? You need to justify. What do you bring it to the table? Are you open to learning and creating solutions? I think we are already creative all now. Oh, for sure. That clear. For sure. It's a paper trail work days gone that can replace the AI. Where our, we see is, yeah, he's a, I know it's hard to say, but everybody has talent. But I don't think so. They use it. Well, they get distracted with, oh, I need to get framed resume. I need to have keywords in a resume. Right. We are in that space where like, okay, give me a one project you delivered out there. You put it out there and where I can look at it and see if it's giving any value to us. Oh, I didn't think about it. How many times we talk in a meeting? Oh, I didn't think about this. That's what I expect in new generation is they have. Amazing talent. I think they have their last focus. Well, and the other thing is, is that with the job market, the way it is now, your reputation, your brand and your network as an individual are going to be more important than ever. And part of that is how do you tell a particular employer what the projects you've worked on and done it. But also as the employer, if I call you and say, Hey, you need to meet Matt. He's perfect for what you're working on. You're more apt to talk to him and possibly hiring. And yet the younger generation is more apt not to go to networking events, not to join organizations, not to really go out even socialize with people in their industry. They don't go to national conferences in their industry. And then they are like, oh, well, everybody gets their job through their network, but I don't have a network. And it's like, well, you have to create that yourself. And I call it human interaction, H.I. And I talk about the fact that H.I. is more important in the world than any time before, because anybody can generate a resume. Yeah. You know, anyone can generate a resume. Now, if you can't write a resume using an A.I. tool in five minutes, then, you know, shame on you. But how do you get that resume in front of a hiring manager? How do you get that resume, you know, in front of people? And how do you know that when a job is open, someone's like, oh, my God, I just heard that Becky got laid off. She's fantastic. Let's hire her. Yeah. Earlier in my career, I got laid off three times because of companies, not anything I did. Companies that either entirely went out of business. Yeah. Or left Austin. Yeah. And every time I had a job within three weeks and I barely made an outbound call and people said, well, how? How do you get that? And it was a better job. They go, you get laid off up. How does that happen? And it was because I bought into this idea and I learned it from a gentleman named Harvey McKay, who wrote a famous book in the 90s called How to Swim with the Sharks Without Getting Eaten Alive. Yeah. And I met Harvey. He actually is the person who got me inspired to become a professional speaker, something that I do as well. And I've met him. I've known Harvey for a long time. But I read his books and I believed him. And he basically said that the friendships you build and the relationships you build and the reputation you build are going to be your secret weapon. And it doesn't happen in the first five or 10 years. No. If you fast forward from when I was in my 20s and 30s when I learned that to now, people always ask me, how did you get a job at ATC? I'm like, well, they were looking for somebody to reinvent it. Yeah. And half the board knew who I was. Yeah. And they asked me. Yeah. Exactly. What you just laid. We are doing a fit gap. If a role demands. You get shocked how many people apply not knowing what job, what company that. Are you applying to a job in a mass? Like not. I understand the desperation to need a job. But spend time. What this company does. Go to their website. I mean, in five minutes, you can get the gist of what a company does. Yeah. You got to know the company. You got to know what you are pitching to the company. Yeah. What exactly you fit in, why you are the best guy. You need to articulate that really. I don't care. Oh, nobody reads all the pages. We get some resumes, 20 pages. I don't blame them. They're very proud of what they've done. We are all proud of. We're not going to read it. Yeah. Nobody's going to read your 20 pages. What we need is that cover letter tells me what you can bring it to our company. Our region. You show me what we are doing. You can do different. And I flip that around to the companies too. You know, it used to be companies wanted to be really active in the community. They wanted to support the Austin Technology Council. They wanted to support the Chamber of Commerce or whatever group. And they wanted their logos to be out there that they were part of the community because they knew that their employees appreciated seeing the support of the community. But they also knew that when they were looking for employees, if I've never heard of the company, I could totally miss the opportunity and not even apply. But if the company builds a reputation and the leadership of the company and you do a great job of this, you know, you show up at different events around town. I would love to see because that's where I pick on some people. Well, right. But that's how you find people. But also, then if someone comes to me and says, hey, have you heard of this company? I'm like, oh, that was great. You've got to go. If you stay behind the wall and just build your product and somebody comes to me and says, oh, they're looking for somebody. And I go, I don't know who they are. A great person may not be as inspired to apply. And so I think that the people have to build a reputation in their community and in their industry. And I think companies have to go back to the days of building a reputation in their community. They got to be there. It's a both ways. Yeah, it's a both ways. Another one thing we have done in Onanu is we welcome anybody who wants an internship. I know we are a bootstrapping it. We don't pay. So what do we give? Three months time to prove them. Why? Because we know that you need to hire them. Some kind of a job. That's what we have done with a lot of people. Some people came and they said, OK, they didn't. We are not looking for paper files. Right. We don't guide you. We'll give you a project. You come and beat us down. This is what you should do. That's what we have. I think that is where the market is going in the jobs now is every company with the AI. Because you have now you have we crossed information is now we are in technology age. We have the AI tools for everything you can do. Now, what you can do with the AI tools, the information you already have, just a click of a button. Right. And people are scared of the AI tools. But, you know, we're still in the infancy. Yeah. So we're not even started. We're not even started. Right. I mean, ChatGPT was two and a half years ago. Yeah. And, you know, I credit a friend of mine who ChatGPT had released like three days before. And he said, you got to look at this. Not just because of your role with ATC, but your role as a professional speaker. You've got to learn what's going on because this is going to affect things. So I was accidentally early exposed to it. And, you know, it's come a long way in two and a half years. It doesn't hallucinate as much. You know, it doesn't use as many goofy phrases. However, it's still in its infancy. And so people who are like, oh, AI can do the writing. No, you can't trust it. If you're looking for AI to do coding or writing, you can't trust it to give you the final product. You still need humans who are going to tweak it. I know somebody who's a coder. And they use AI. But he said, look, it's not writing the best code. So he treats it in one of two ways. He either treats it as a junior coder who he has to review everything they do. Yeah. Or as the most senior creative coder he's ever seen. Yeah. Who's on LSD. Yeah. Which means he still has to go back and review everything that it does. And then it's a great productivity tool. But people need to, for the time being, see it as just that. Yeah. And then, oh, it's going to take away my job. No. If it makes you more productive, you're going to keep your job longer and you're going to get more jobs if you know how to use these tools. And it's not just, you know, LLMs. There's so much more coming out and becoming more ubiquitous in the world of AI. And then when we get to quantum computing, if they can figure that out, God save us all, the changes are going to go even faster. Yeah. And I think, like, something really that you say, like, the younger generation, like, it's kind of scary to go out and network, you know? Like, we're not used to it. So I'm going to ask you. I'm going to throw that back at you, Max. Why is it scary to go network? Are people like Babu and I, are we mean? Well, because, like, this is the most, like, crazy part about all of this to me is that, like, we went to school. We're learning this stuff, like accounting. We're learning other stuff. And then we're out now and it's four years after, you know, like two years after AI comes out. There's no one, there's no educational, you know, like, who's the best at doing AI? They started learning three, four years ago. You know, so that's why it's scary because you're going out and you're not classically trained. I mean, the more you go out, the more people you meet, the more network you have. And it's also why you can see, like, the rise of, like, YouTube. Like, I just looked it up. It's three, almost 400,000 people make a living off of platforms like YouTube, which is just a large network that's deployed digitally. And they, you know, so because it's just so interesting, like, there's no education. Who's going to be the best? Like, can you apply to a job at a company? You could have almost no experience, no education. But if you have three or four projects that you're successful with, then you're going to solve a problem at that company. Right. You're going to get the job, you know, because it is just so new and there's no education. But why the scary to go network to find these connections and open those doors? Well, like, you expect one thing and then you get another, you know. So it's like everybody is living in a world where it's like we don't really know how to use AI. Right. See, the personality, I came from a, you know, very shy, you know, I opened up. I learned in the U.S. being staying this long in Austin. I never used to talk up. You have to get up. You have to get out of your comfort zone. Right. The only way to get off the continent, two things you have to do. Either you go all in or you have one glass of beer and then get a little bit out of shyness and then go and talk. You have to start with strangers. And it's a learned skill. If you say, I'm going to go to two networking events a week for the next six months, at the end of six months, you're going to be like, I can talk to anybody. I can go do this. I just go and break into some group and stand there. Yeah. Well, and don't you don't need to do anything. What I say is like back to your generation, a lot of people grew up with everything was digital. You talk to your friends via text, you know, you hang out and, you know, and chat via the screen. And here's what I tell them. We have to remember, though, is that all opportunities in life and this will not change no matter what the technology does. All opportunities in life come from people. Yeah. And so earlier on, if you can collect people in a good way, if you can collect relationships, not like Courtney and I, not like, ha ha ha, what can they do for me? But if you can get to know people and build relationships with people and show them through actions over five years, 10 years, etc., then you're a doer. You care about the community. You're someone who helps other people. You're not just out for yourself. What's going to happen is, is opportunities are going to come up. People are going to pull you in. But if you don't do those things, they don't know who you are. Even if they would want to pull you in, they don't know you. They can't do anything. So I try to teach younger people, you know, look at everything good that's happened in your life. It's happened because of a person. You know, somehow you can trace it back to people. And the more you can connect in a community like Austin, and that's why we have to work hard as a community to keep that vibe of connectivity, of community. Because if you can still reach people and share with people, you know, not everybody's going to like you and that's fine. But if you just keep trying, and when people say, oh, I don't like to go to networking events because I go once and nothing happens. Well, no, you've got to go to that same organization like for a year. Not before you're going to get benefit, but before people even notice that you're there. You've got to keep showing up. You've got to keep showing up. And after a while, they're like, I see you around. What do you do? And then you start to learn. Yeah. The more persistent you are in showing up is going to value. Another one is we know the stats. 80% of the jobs in America are filled by repros. So you have left with 20%. You are. And most of the, unfortunately, these federal laws, compliance, HR laws, and state laws create a lot of problems. And they create a big mess. Oh, you've got to post a job two weeks before, you know, it's all this. I don't know if it's really helping anybody. Well, it depends. Right. But in a lot of cases, what happens is if they're taking somebody internally or they're taking someone through. I already know who they are. They post the job. I can't tell you how many people tell me they apply to 100 jobs and only hear back from two, three companies. It's like you're applying to a job. You're not even getting a thing back that says, thank you for applying. We've gone a different direction. You're just never, it's an abyss. I'll tell you why people don't respond. There is a compliance. They could be you are pulled into a lawsuit. Right. I think it's a part of, because the communication sometimes can turn on your, against you. That's true. So companies are scared to do anything. Right. It's sensitive to somebody. He's desperate. He needs a job. And he puts it. Then you want to help him to send a message. That message can turn into your side. So there is always how you see the world, how you can say. For me, like, as you said, know people, talk to people, connect with people. You know, your local community is a big thing. Yep. You, if you, you, if you are a person they like you, you'll get a job. Well, I think it's your local community. So it's things like technology council. It's things like chamber, a whole bunch of other organizations. You know, you can't belong to everything and you can't go to everything. So pick two or three groups. Yeah. And go to those. Regular. Regular. Because most of us only have one event a month. Yeah. So pick three. You've got three things to go to in a month. Yeah. One that does a breakfast, one that does a lunch, one that does a happy hour. And always go to those. And then you can drop in on other ones if there's a good topic. But the other thing is, is what is your industry? Yeah. And what type of industry groups, both locally and nationally exist. Yeah. I mean, the, the people who get really involved with, you know, the, the, the, the, the, the, the, the, the, the, the. You know if there are a high, if there are hiring like CIO and the a property with real estate marketing. Yeah. Does a beardamine comenzar beach and O supposed to be like a beach commercial to describe not in저i a rock star and make a house sign Post because o supposed to be like I only think aboutna James altogether and not others. Because I think you've got to keep that shit to yourself. That's pretty high. Like let's look at... Okay.\"}\n"]}], "source": ["import pyodbc\n", "\n", "# Your Azure SQL connection string\n", "AZURE_SQL_CONNECTION_STRING = (\n", "    \"Driver={ODBC Driver 18 for SQL Server};\"\n", "    \"Server=tcp:videoprocessingserver.database.windows.net,1433;\"\n", "    \"Database=VideoProcessingDB;\"\n", "    \"Uid=sqladmin;\"\n", "    \"Pwd=Unnanu@2024;\"\n", "    \"Encrypt=yes;\"\n", "    \"TrustServerCertificate=no;\"\n", "    \"Connection Timeout=600;\"\n", ")\n", "\n", "def fetch_data():\n", "    conn = pyodbc.connect(AZURE_SQL_CONNECTION_STRING)\n", "    cursor = conn.cursor()\n", "    cursor.execute(\"SELECT Id, VideoPath, Description, Transcript FROM VideoProcessingResults\")  \n", "\n", "    rows = cursor.fetchall()\n", "\n", "    # ✅ Print all rows\n", "    for row in rows:\n", "        print(\"Row:\", row)  \n", "\n", "    # ✅ Print column names with values (more clear)\n", "    columns = [column[0] for column in cursor.description]\n", "    for row in rows:\n", "        row_dict = dict(zip(columns, row))\n", "        print(\"Row as dict:\", row_dict)\n", "\n", "    conn.close()\n", "    return rows\n", "\n", "if __name__ == \"__main__\":\n", "    fetch_data()\n"]}, {"cell_type": "code", "execution_count": null, "id": "ff53025d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv (3.10.18)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}