<!-- <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Query Application</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f9;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .query-form {
            margin-bottom: 20px;
        }
        textarea {
            width: 100%;
            height: 100px;
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            resize: vertical;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .response {
            background-color: white;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Video Query Application</h1>
    <form method="POST" class="query-form">
        <textarea name="query" placeholder="Enter your query here">{{ query }}</textarea>
        <button type="submit">Submit Query</button>
    </form>
    {% if response %}
    <div class="response">
        <h2>Response:</h2>
        <p>{{ response }}</p>
    </div>
    {% endif %}
    {% if video_url %}
    <video width="640" height="360" controls>
        <source src="{{ video_url }}#t={{ timestamp }}" type="video/mp4">
        Your browser does not support the video tag.
    </video>
    {% else %}
    <p>No video available for this query.</p>
    {% endif %}
</body>
</html> -->

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Query Application</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; background-color: #f4f4f9; }
        h1 { color: #333; text-align: center; }
        textarea { width: 100%; height: 100px; padding: 10px; margin-bottom: 10px; border: 1px solid #ccc; border-radius: 4px; }
        button { background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        .response { background-color: white; padding: 15px; border: 1px solid #ddd; border-radius: 4px; white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>Video Query Application</h1>
    <form method="POST">
        <textarea name="query" placeholder="Enter your query here">{{ query }}</textarea>
        <button type="submit">Submit Query</button>
    </form>
    {% if response %}
    <div class="response">
        <h2>Response:</h2>
        <pre>{{ response }}</pre>
    </div>
    {% endif %}
    {% if video_url %}
    <video width="640" height="360" controls>
        <source src="{{ video_url }}#t={{ timestamp }}" type="video/mp4">
        Your browser does not support the video tag.
    </video>
    {% else %}
    <p>No video available for this query.</p>
    {% endif %}
</body>
</html>
