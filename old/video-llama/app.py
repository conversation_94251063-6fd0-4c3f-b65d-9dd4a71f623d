import json
import pyodbc
import re
import numpy as np
import requests
from fastapi import FastAP<PERSON>, Form, Request
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from sentence_transformers import SentenceTransformer, util
from nltk.corpus import stopwords
import nltk

# Download stopwords (only first time needed)
nltk.download("stopwords", quiet=True)
STOPWORDS = set(stopwords.words("english"))

# ================= CONFIG =================
AZURE_SQL_CONNECTION_STRING = (
    "Driver={ODBC Driver 18 for SQL Server};"
    "Server=tcp:videoprocessingserver.database.windows.net,1433;"
    "Database=VideoProcessingDB;"
    "Uid=sqladmin;"
    "Pwd=Unnanu@2024;"
    "Encrypt=yes;"
    "TrustServerCertificate=no;"
    "Connection Timeout=600;"
)

GROQ_API_KEY = "********************************************************"
GROQ_API_URL = "https://api.groq.com/openai/v1/chat/completions"

# Load embeddings model
embedder = SentenceTransformer("all-MiniLM-L6-v2")

# FastAPI setup
app = FastAPI()
#app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# ============== QUERY PREPROCESSING ==============
def preprocess_query(query: str) -> str:
    # Lowercase
    query = query.lower()
    # Remove special characters (keep alphanumeric + spaces)
    query = re.sub(r"[^a-z0-9\s]", "", query)
    # Remove stopwords
    tokens = query.split()
    filtered_tokens = [word for word in tokens if word not in STOPWORDS]
    return " ".join(filtered_tokens)

# ============== DB FUNCTIONS ==============
def connect_to_azure_sql():
    try:
        conn = pyodbc.connect(AZURE_SQL_CONNECTION_STRING)
        return conn
    except pyodbc.Error as e:
        print(f"❌ Error connecting to Azure SQL: {e}")
        raise

def retrieve_relevant_rows(query: str, top_k: int = 1):
    # Preprocess query
    clean_query = preprocess_query(query)
    print(f"🔧 Processed Query: {clean_query}")

    # Embed
    query_embedding = embedder.encode(clean_query, convert_to_numpy=True).astype(np.float32)
    conn = connect_to_azure_sql()
    cursor = conn.cursor()

    sql_query = """
    SELECT VideoPath, Description, SrtContent, SrtContentObjects, Transcript, 
           WordTimestamps, AudioTags, Diarization, Embeddings
    FROM VideoProcessingResults
    """
    cursor.execute(sql_query)
    rows = cursor.fetchall()

    results = []
    for row in rows:
        result = {
            "VideoPath": row.VideoPath,
            "Description": row.Description or "",
            "SrtContent": row.SrtContent or "",
            "SrtContentObjects": row.SrtContentObjects or "",
            "Transcript": row.Transcript or "",
            "WordTimestamps": json.loads(row.WordTimestamps) if row.WordTimestamps else [],
            "AudioTags": json.loads(row.AudioTags) if row.AudioTags else [],
            "Diarization": json.loads(row.Diarization) if row.Diarization else [],
            "Embeddings": json.loads(row.Embeddings) if row.Embeddings else {}
        }

        # Calculate cosine similarity
        embeddings = result["Embeddings"]
        max_similarity = 0.0
        for field in ["description_embedding", "transcript_embedding"]:
            if field in embeddings:
                field_embedding = np.array(embeddings[field], dtype=np.float32)
                similarity = util.cos_sim(query_embedding, field_embedding)[0][0].item()
                max_similarity = max(max_similarity, similarity)

        result["relevance_score"] = max_similarity
        results.append(result)

    results = sorted(results, key=lambda x: x["relevance_score"], reverse=True)[:top_k]
    cursor.close()
    conn.close()
    return results

# ============== LLM PROMPT FUNCTION ==============
def generate_response(query: str, retrieved_data):
    if not retrieved_data:
        return {"answer": "No relevant data found.", "timestamp": "", "video_path": ""}

    video_data = retrieved_data[0]  # Most relevant
    # Format JSON data separately to avoid f-string issues
    video_data_json = json.dumps(video_data, ensure_ascii=False)

    # Define the prompt template
    prompt_template = """
You are a Video Question Answering Assistant. You are provided with structured video metadata, including:
- VideoPath (path to the video file)
- Description (overall summary of the video content)
- SrtContent (subtitles with timestamps)
- SrtContentObjects (visual object detections with timestamps)
- Transcript (spoken narration)
- WordTimestamps (word-level timings)
- AudioTags (audio event tags)
- Diarization (speaker segments and timings)

Your task is to answer user questions solely using the provided metadata, following these guidelines:
1. Consult all data sources (VideoPath, Description, SrtContent, SrtContentObjects, Transcript, WordTimestamps, AudioTags, Diarization) to identify the most relevant information for the question.
2. Prioritize extracting answers and timestamps from **SrtContentObjects** for questions about visible elements (objects, actions, people, etc.).
3. For questions about dialogue, narration, or spoken content, prioritize information from **SrtContent**, **Transcript**, and **WordTimestamps**.
4. When questions involve timing, include precise timestamps from **SrtContentObjects** or other relevant sources in the format HH:MM:SS,mmm.
5. If multiple relevant references exist, synthesize them into a concise answer.
6. If the requested information is not explicitly present in the provided data, return: "Answer not found in provided video data."
7. Ensure answers are factual, clear, and concise, avoiding any assumptions.
8. Return the response in the following JSON format:
   {{
     "answer": "<short one-line direct answer>",
     "timestamp": "<HH:MM:SS,mmm from SrtContentObjects or other source>",
     "video_path": "<VideoPath>"
   }}
9. If no answer is found, return:
   {{
     "answer": "Answer not found in provided video data.",
     "timestamp": "",
     "video_path": "<VideoPath>"
   }}

Here is the video data:
{video_data_json}

User Question: {query}

Return only the JSON response based on the provided video metadata.
"""

    # Format the prompt with video data and query
    prompt = prompt_template.format(video_data_json=video_data_json, query=query)

    headers = {
        "Authorization": f"Bearer {GROQ_API_KEY}",
        "Content-Type": "application/json"
    }
    payload = {
        "model": "llama-3.1-8b-instant",
        "messages": [{"role": "user", "content": prompt}],
        "temperature": 0
    }

    response = requests.post(GROQ_API_URL, headers=headers, json=payload)
    response.raise_for_status()
    generated_text = response.json()["choices"][0]["message"]["content"].strip()

    try:
        return json.loads(generated_text)
    except json.JSONDecodeError:
        return {"answer": "Error parsing model output", "timestamp": "", "video_path": video_data["VideoPath"]}

# ============== ROUTES ==============
@app.get("/", response_class=HTMLResponse)
async def get_form(request: Request):
    return templates.TemplateResponse("index.html", {"request": request, "query": "", "response": None, "video_url": None, "timestamp": ""})

@app.post("/", response_class=HTMLResponse)
async def submit_query(request: Request, query: str = Form(...)):
    retrieved_data = retrieve_relevant_rows(query)
    result = generate_response(query, retrieved_data)

    video_url = result.get("video_path", "")
    timestamp = ""
    if result.get("timestamp"):
        timestamp = result["timestamp"].split(" ")[0]  # Extract "HH:MM:SS,mmm"

    return templates.TemplateResponse(
        "index.html",
        {
            "request": request,
            "query": query,
            "response": json.dumps(result, indent=2),
            "video_url": video_url,
            "timestamp": timestamp
        }
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app:app", host="0.0.0.0", port=8000, reload=True)