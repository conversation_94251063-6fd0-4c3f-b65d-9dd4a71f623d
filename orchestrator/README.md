# Azure AI Foundry Orchestrator (azure-ai-projects)

This FastAPI service wraps an Agentic Orchestrator created via `azure-ai-projects` for use with Azure AI Foundry.

- Uses the Tool Use Pattern
- Connects to Azure AI Search and Azure Blob Storage via Project Connections
- Accepts optional image data as `data:` URL and forwards as an attachment

## Configure

Set one of:
- `AZURE_AI_PROJECT_CONNECTION_STRING` (recommended)
- `AZURE_AI_PROJECT_ENDPOINT` (and configure `DefaultAzureCredential` access)

Other variables:
- `AZURE_AI_ORCHESTRATOR_MODEL` (e.g., `gpt-4o-mini`)
- `AZURE_AI_SEARCH_CONNECTION` (Project connection name or ID)
- `AZURE_BLOB_CONNECTION` (Project connection name or ID)
- `AZURE_AI_AGENT_NAME` (optional custom agent name, defaults to `enterprise-orchestrator`)

## Install and run locally

```
python -m venv .venv
source .venv/bin/activate
pip install -r orchestrator/requirements.txt
python -m orchestrator.app
```

The API listens on `http://localhost:8080/orchestrate`.

## Deploy to Azure AI Foundry

- Create or select a Project in Azure AI Foundry
- Create Connections for Azure AI Search and Azure Blob Storage
- Provision an Agent (this code `create`s on first use). Ensure the model deployment exists
- Deploy this FastAPI app as a service/container (e.g., via Azure Container Apps or Web App for Containers) and grant it the same Managed Identity used by the Project (or provide the connection string)

## Contract

POST `/orchestrate`

```
{
  "message": "Find latest policies",
  "context": "[S1] Policy handbook 2024 update...",
  "image_data_url": "data:image/png;base64,..."
}
```

Response

```
{
  "text": "...final assistant answer..."
}
```

This service is optional in local development: the Node server contains a built-in orchestrator with tools and `context7` retrieval.
