import base64
import os
from typing import Any, Dict, Optional

from fastapi import FastAP<PERSON>
from pydantic import BaseModel

from azure_ai_foundry_orchestrator import AzureAIAgenticOrchestrator


class OrchestrateRequest(BaseModel):
    message: str
    context: Optional[str] = None
    image_data_url: Optional[str] = None


class OrchestrateResponse(BaseModel):
    text: str


app = FastAPI()


def _parse_data_url(data_url: str) -> Optional[Dict[str, Any]]:
    try:
        if not data_url.startswith('data:'):
            return None
        header, b64 = data_url.split(',', 1)
        mime = header.split(';')[0].split(':')[1]
        return {'type': 'image', 'image': {'mime_type': mime, 'data': base64.b64decode(b64)}}
    except Exception:
        return None


@app.post('/orchestrate', response_model=OrchestrateResponse)
def orchestrate(req: OrchestrateRequest) -> OrchestrateResponse:
    orch = AzureAIAgenticOrchestrator()
    attachments = []
    if req.image_data_url:
        att = _parse_data_url(req.image_data_url)
        if att:
            attachments.append(att)
    text = orch.run(req.message, attachments=attachments, context=req.context)
    return OrchestrateResponse(text=text)


def main() -> None:  # pragma: no cover
    import uvicorn

    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('PORT', '8080'))
    uvicorn.run('app:app', host=host, port=port, reload=False)


if __name__ == '__main__':  # pragma: no cover
    main()
