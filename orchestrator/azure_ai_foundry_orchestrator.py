import os
from typing import Any, Dict, Iterable, List, Optional

try:
    # The azure-ai-projects package provides a client for Azure AI Foundry projects
    from azure.ai.projects import AIProjectClient
    from azure.identity import DefaultAzureCredential
except Exception:  # pragma: no cover - available only after user installs deps
    AIProjectClient = None  # type: ignore
    DefaultAzureCredential = None  # type: ignore


class AzureAIAgenticOrchestrator:
    """
    Thin wrapper over Azure AI Projects (AI Foundry) agents using the Tool Use Pattern.

    Notes:
    - This code assumes an existing Azure AI Project with connections configured
      for Azure AI Search and Azure Blob Storage.
    - Set one of the following to authenticate:
        AZURE_AI_PROJECT_CONNECTION_STRING (recommended)
        or AZURE_AI_PROJECT_ENDPOINT + use DefaultAzureCredential
    - Environment variables:
        AZURE_AI_ORCHESTRATOR_MODEL (e.g., 'gpt-4o-mini')
        AZURE_AI_SEARCH_CONNECTION (e.g., 'azaisearch-conn')
        AZURE_BLOB_CONNECTION (e.g., 'blob-conn')
    """

    def __init__(self) -> None:
        if AIProjectClient is None:
            raise RuntimeError(
                'azure-ai-projects not installed. Please `pip install -r orchestrator/requirements.txt`.'
            )

        conn_str = os.getenv('AZURE_AI_PROJECT_CONNECTION_STRING')
        endpoint = os.getenv('AZURE_AI_PROJECT_ENDPOINT')
        if conn_str:
            self.client = AIProjectClient.from_connection_string(conn_str)
        elif endpoint:
            cred = DefaultAzureCredential()
            self.client = AIProjectClient(endpoint=endpoint, credential=cred)
        else:
            raise RuntimeError('Missing AZURE_AI_PROJECT_CONNECTION_STRING or AZURE_AI_PROJECT_ENDPOINT')

        self.model = os.getenv('AZURE_AI_ORCHESTRATOR_MODEL', 'gpt-4o-mini')
        self.search_conn = os.getenv('AZURE_AI_SEARCH_CONNECTION')
        self.blob_conn = os.getenv('AZURE_BLOB_CONNECTION')
        self.agent_name = os.getenv('AZURE_AI_AGENT_NAME', 'enterprise-orchestrator')

        # Agent will be lazily created
        self._agent_id: Optional[str] = None

    def _list_agents(self) -> Iterable[Any]:
        """Helper to list all agents. Isolated for easier testing."""
        return self.client.agents.list()

    def _ensure_agent(self) -> str:
        if self._agent_id:
            return self._agent_id

        # Try to reuse an existing agent to avoid creating duplicates on restart
        try:
            for agent in self._list_agents():
                if getattr(agent, 'name', '').lower() == self.agent_name.lower():
                    self._agent_id = getattr(agent, 'id')
                    break
        except Exception:
            # Listing can fail if permissions are limited; fall back to create
            self._agent_id = None

        if self._agent_id:
            return self._agent_id

        # Define tools using connections configured in the project
        tools: List[Dict[str, Any]] = []
        if self.search_conn:
            tools.append({
                'type': 'azure_ai_search',
                'connection': self.search_conn,
                'parameters': {},
            })
        if self.blob_conn:
            tools.append({
                'type': 'azure_blob_storage',
                'connection': self.blob_conn,
                'parameters': {},
            })

        instructions = (
            'You are an enterprise AI search orchestrator that routes to the correct modality-specific tools. '
            'Use the available tools to gather and synthesize context from Azure AI Search, Blob Storage, '
            'and downstream services. Always ground answers in citations such as [S1], [S2], etc., and '
            'call tools before responding when additional context is needed.'
        )

        # Create or get agent. The exact API surface may differ by package version.
        agent = self.client.agents.create(
            model=self.model,
            name=self.agent_name,
            instructions=instructions,
            tools=tools or None,
        )
        self._agent_id = agent.id  # type: ignore[attr-defined]
        return self._agent_id

    @staticmethod
    def _build_user_prompt(message: str, context: Optional[str]) -> str:
        if context:
            prefix = (
                'Use the provided enterprise context to answer the user question. '
                'Only rely on the context when it is relevant and cite the supporting snippets.'
            )
            return f"{prefix}\n\nContext:\n{context}\n\nQuestion:\n{message}"
        return message

    def run(
        self,
        message: str,
        attachments: Optional[List[Dict[str, Any]]] = None,
        context: Optional[str] = None,
    ) -> str:
        """
        Creates a thread, runs the agent with the user message, and returns the final text.
        """
        agent_id = self._ensure_agent()
        thread = self.client.agents.threads.create()
        msg_content: List[Dict[str, Any]] = [
            {'type': 'text', 'text': self._build_user_prompt(message, context)}
        ]
        if attachments:
            msg_content.extend(attachments)

        self.client.agents.messages.create(
            thread_id=thread.id,  # type: ignore[attr-defined]
            role='user',
            content=msg_content,
        )

        run = self.client.agents.runs.create(
            thread_id=thread.id,  # type: ignore[attr-defined]
            agent_id=agent_id,
        )

        # Wait until run completes
        self.client.agents.runs.wait(thread_id=thread.id, run_id=run.id)  # type: ignore[attr-defined]

        messages = list(self.client.agents.messages.list(thread_id=thread.id))
        # Return last assistant message text
        for m in reversed(messages):
            if getattr(m, 'role', None) == 'assistant':
                parts = getattr(m, 'content', [])
                texts = [p.get('text') for p in parts if p.get('type') == 'text']
                return '\n'.join([t for t in texts if t])
        return ''
