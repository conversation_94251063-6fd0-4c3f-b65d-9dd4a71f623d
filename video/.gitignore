# Python
__pycache__/
*.py[cod]
*$py.class

# Virtual Environment
.venv/
venv/
ENV/
env/

# Video files (large video files)
*.mp4
*.avi
*.mkv
*.mov
*.wmv
*.flv
*.webm
*.m4v
*.mpg
*.mpeg
!sample*.mp4
!example*.mp4

# Extracted frames
frames/
extracted_frames/
thumbnails/

# Processed data
processed/
output/
temp_video/
transcriptions/
captions/

# Model files
models/
checkpoints/
*.pt
*.pth
*.onnx
*.pb
*.h5

# Environment variables
.env
.env.*
!.env.example

# Logs
*.log
logs/

# OS
.DS_Store
Thumbs.db

# IDE
.vscode/
.idea/

# Project specific
data/
uploads/
cache/
tmp/
embeddings/