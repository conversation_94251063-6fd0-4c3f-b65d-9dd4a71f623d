# Python
__pycache__/
*.py[cod]
*$py.class

# Virtual Environment
.venv/
venv/
ENV/
env/

# Image files (large image datasets)
dataset/
datasets/
raw_images/
processed_images/
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.svg
!sample*.jpg
!sample*.png
!example*.jpg
!example*.png

# Model files
models/
checkpoints/
*.pt
*.pth
*.onnx
*.pb
*.h5
*.pkl
*.pickle

# Vector databases
vector_db/
embeddings/
*.faiss
*.index

# Environment variables
.env
.env.*
!.env.example

# Logs
*.log
logs/

# OS
.DS_Store
Thumbs.db

# IDE
.vscode/
.idea/

# Project specific
data/
uploads/
cache/
tmp/
output/