Model & AI Components:

Vision Model: Meta LLaMA-4 Scout (via Groq API) for image captioning
Embedding Model: SentenceTransformer "all-MiniLM-L6-v2" for semantic similarity
Caption Format: Structured output with keywords, detailed description, and summary

Storage Architecture:

Image Storage: Azure Blob Storage (container: "image-search")
Database: Azure SQL Database with two tables:

ImageCatalog: stores image metadata, descriptions, embeddings
ImageQueries: tracks search history and extracted keywords


Local Fallback: CSV file (image_catalog.csv) for development

Embeddings & Search:

Generation: Text captions converted to vector embeddings using SentenceTransformer
Storage: Embeddings serialized as JSON strings in database
Search: Cosine similarity matching with configurable threshold (0.4 default)

Preprocessing Pipeline

Image Upload → Azure Blob Storage (with public URL generation)
Caption Generation → LLaMA model creates structured descriptions
Embedding Creation → SentenceTransformer encodes captions
Database Storage → Metadata saved to Azure SQL

User Interface (Streamlit)

Upload Section: Multi-file image upload with real-time processing
Search Section: Natural language query input
Results Display: Grid layout with similarity scores
Admin Tools: Catalog clearing, search history viewing

Complete Flow

User uploads images → Stored in Azure Blob → URLs generated
AI generates captions → Structured descriptions created
Embeddings computed → Vector representations stored
User searches → Keywords extracted → Semantic similarity matching
Results returned → Ranked by similarity scores → Displayed in grid