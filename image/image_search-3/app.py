import streamlit as st
import pandas as pd
import json
from datetime import datetime
from config import CATALOG_CSV
from utils.image_utils import upload_and_save_image
from utils.caption_utils import generate_caption, model
from utils.search_utils import search_similar_images, load_catalog
from utils.db_utils import insert_image_catalog, insert_search_query, fetch_search_history

# 🚀 Page setup
st.set_page_config(page_title="Fashion Visual Search", layout="centered")
st.title("👗 Fashion Visual Search Engine")

# --- Sidebar: Admin Tools ---
st.sidebar.header("🛠️ Admin Tools")
if st.sidebar.button("🧹 Clear Entire Catalog"):
    pd.DataFrame(columns=["image_path", "description", "timestamp"]).to_csv(CATALOG_CSV, index=False)
    st.sidebar.success("✅ Catalog cleared!")

# Sidebar Search History
st.sidebar.subheader("📜 Search History")
if st.sidebar.button("Show Recent Searches"):
    history = fetch_search_history(limit=20)
    if history:
        st.sidebar.write(pd.DataFrame(history))
    else:
        st.sidebar.info("No search history found.")

# --- Upload Section ---
st.header("📄 Upload Fashion Images")
uploaded_files = st.file_uploader("Upload images", type=["jpg", "jpeg", "png"], accept_multiple_files=True)

if uploaded_files:
    st.subheader("📸 Uploaded Images")
    uploaded_image_urls = []

    for file in uploaded_files:
        image_url = upload_and_save_image(file, upload_to_azure=True)  # Upload to Azure Blob

        try:
            caption = generate_caption(image_url, is_url=True)
            embedding = model.encode(caption).tolist()

            insert_image_catalog(
                filename=file.name,
                blob_link=image_url,
                description=caption,
                upload_date=datetime.now(),
                tags="",  # Optional tags
                embedding=json.dumps(embedding)
            )

            uploaded_image_urls.append(image_url)

        except Exception as e:
            st.error(f"❌ Failed to process {file.name}: {e}")
            continue

    cols = st.columns(4)
    for idx, img_url in enumerate(uploaded_image_urls):
        with cols[idx % 4]:
            st.image(img_url, width=200)

    st.success("✅ All uploaded images have been processed and saved to the catalog.")

st.markdown("---")

# --- Search Section ---
st.header("🔍 Search Fashion Catalog")
query = st.text_input("Search something (e.g., 'woman in blue scarf', 'show me a red dress')")

if query:
    try:
        # Extract keywords (simple split — replace with advanced NLP if needed)
        extracted_keywords = ", ".join([word for word in query.split() if len(word) > 2])

        # Save query to DB before search
        #insert_search_query(query=query, extracted_keywords=extracted_keywords, search_datetime=datetime.now())

        results = search_similar_images(query)  # Already handles embeddings
        if results:
            st.subheader("🔎 Search Results")
            search_image_urls = [res[0] for res in results]
            scores = [res[2] for res in results]

            cols = st.columns(4)
            for idx, (img_url, score) in enumerate(zip(search_image_urls, scores)):
                with cols[idx % 4]:
                    st.image(img_url, width=200)
                    st.caption(f"Similarity Score: {score:.2f}")

            st.info("📌 Your search has been logged for tracking in ImageQueries.")

        else:
            st.warning("No matching images found.")

    except Exception as e:
        st.error(f"❌ Search failed: {e}")

# --- Optional: Show Full Catalog Table ---
if st.checkbox("📋 Show Catalog"):
    df = load_catalog()
    st.dataframe(df[["image_path", "timestamp"]])
