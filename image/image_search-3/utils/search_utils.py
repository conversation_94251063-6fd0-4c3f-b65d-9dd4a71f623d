import os
import pandas as pd
import json
import re
import torch
from sentence_transformers import SentenceTransformer, util
from config import CATALOG_CSV
from datetime import datetime, timedelta
from utils.db_utils import insert_search_query  # ✅ For logging searches

model = SentenceTransformer("all-MiniLM-L6-v2")

def load_catalog():
    if not os.path.exists(CATALOG_CSV):
        return pd.DataFrame(columns=["image_path", "description", "timestamp", "embedding"])

    df = pd.read_csv(CATALOG_CSV)
    df["timestamp"] = pd.to_datetime(df["timestamp"])
    return df  # ← Returns all images regardless of age

def extract_keywords(query):
    """
    Extracts keywords from a natural language query.
    Removes common stopwords and punctuation.
    Example: 'show me red dress' -> ['red', 'dress']
    """
    stopwords = {"show", "me", "find", "get", "display", "please", "want", "see", "give", "looking", "for", "a", "the", "is", "are", "in", "on", "at", "to", "with"}
    words = re.findall(r'\w+', query.lower())
    return [w for w in words if w not in stopwords]

def search_similar_images(query, top_k=1, score_threshold=0.4):
    df = load_catalog()
    if df.empty:
        return []

    # 1️⃣ Extract keywords
    keywords = extract_keywords(query)

    # 2️⃣ Store search in ImageQueries table
    insert_search_query(
        query=query,
        extracted_keywords=json.dumps(keywords),  # Store as JSON string
        search_datetime=datetime.now()
    )

    # 3️⃣ Use keywords text for embedding (fallback: use full query if no keywords found)
    keywords_text = " ".join(keywords) if keywords else query

    # 4️⃣ Optional: filter catalog by keyword match first
    if keywords:
        df = df[df["description"].str.contains("|".join(keywords), case=False, na=False)]
        if df.empty:
            return []

    # 5️⃣ Semantic similarity search (on keywords_text)
    query_embedding = model.encode(keywords_text, convert_to_tensor=True)
    embeddings = [json.loads(e) for e in df["embedding"].tolist()]
    corpus_embeddings = torch.tensor(embeddings)

    scores = util.cos_sim(query_embedding, corpus_embeddings)[0]
    top_results = scores.topk(k=min(top_k, len(df)))

    results = []
    for score, idx in zip(top_results.values, top_results.indices):
        if float(score) >= score_threshold:
            row = df.iloc[int(idx)]
            results.append((row["image_path"], row["description"], float(score)))

    return results
