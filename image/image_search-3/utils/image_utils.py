#image_utils.py
import os
import uuid
import mimetypes
import requests
from azure.storage.blob import BlobServiceClient, BlobClient, ContentSettings
from urllib.parse import quote_plus
from config import IMAGE_DIR

# Azure Blob Configuration
AZURE_CONNECTION_STRING = "DefaultEndpointsProtocol=https;AccountName=datauat;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
AZURE_CONTAINER_NAME = "image-search"
AZURE_ACCOUNT_NAME = "datauat"

def is_url_publicly_accessible(url):
    try:
        response = requests.head(url, timeout=5)
        return response.status_code == 200
    except Exception:
        return False

def upload_image_to_azure(file_data, filename):
    blob_service_client = BlobServiceClient.from_connection_string(AZURE_CONNECTION_STRING)
    container_client = blob_service_client.get_container_client(AZURE_CONTAINER_NAME)

    # Generate unique filename
    file_ext = os.path.splitext(filename)[1]
    unique_filename = f"{uuid.uuid4()}{file_ext}"

    # Detect MIME type
    mime_type, _ = mimetypes.guess_type(filename)
    mime_type = mime_type or "application/octet-stream"

    # Create a BlobClient for uploading with extended timeout
    blob_client = container_client.get_blob_client(unique_filename)
    blob_client.upload_blob(
        data=file_data,
        overwrite=True,
        timeout=300,  # Extend timeout to avoid write timeouts
        content_settings=ContentSettings(content_type=mime_type)
    )

    # Create public blob URL
    encoded_blob_name = quote_plus(unique_filename).replace("+", "%20")
    blob_url = f"https://{AZURE_ACCOUNT_NAME}.blob.core.windows.net/{AZURE_CONTAINER_NAME}/{encoded_blob_name}"
    return blob_url

def upload_and_save_image(uploaded_file, upload_to_azure=True):
    os.makedirs(IMAGE_DIR, exist_ok=True)
    file_path = os.path.join(IMAGE_DIR, uploaded_file.name)

    file_bytes = uploaded_file.read()
    with open(file_path, "wb") as f:
        f.write(file_bytes)

    if upload_to_azure:
        blob_url = upload_image_to_azure(file_bytes, uploaded_file.name)

        # ✅ Validate public URL before passing to Groq
        if not is_url_publicly_accessible(blob_url):
            raise Exception(f"Azure Blob URL is not publicly accessible: {blob_url}\n"
                            "🔒 Make sure your container 'image-search' is set to allow anonymous blob access.")

        return blob_url
    else:
        return file_path
