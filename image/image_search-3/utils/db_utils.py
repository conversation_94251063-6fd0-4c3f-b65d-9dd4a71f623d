import pyodbc
import os
from dotenv import load_dotenv

load_dotenv()

def get_db_connection():
    """
    Create and return a connection to the Azure SQL Database.
    """
    conn_str = (
        f"DRIVER={os.getenv('DB_DRIVER')};"
        f"SERVER={os.getenv('DB_HOST')},{os.getenv('DB_PORT')};"
        f"DATABASE={os.getenv('DB_NAME')};"
        f"UID={os.getenv('DB_USER')};"
        f"PWD={os.getenv('DB_PASSWORD')};"
        "Encrypt=yes;TrustServerCertificate=no;Connection Timeout=30;"
    )
    return pyodbc.connect(conn_str)

def insert_image_catalog(filename, blob_link, description, upload_date, tags, embedding):
    """
    Insert image details into ImageCatalog table.
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute(
        """
        INSERT INTO ImageCatalog (filename, blob_link, description, upload_date, tags, embedding)
        VALUES (?, ?, ?, ?, ?, ?)
        """,
        filename, blob_link, description, upload_date, tags, embedding
    )
    conn.commit()
    cursor.close()
    conn.close()

def insert_search_query(query, extracted_keywords, search_datetime):
    """
    Insert a search query into the ImageQueries table for tracking.

    Args:
        query (str): The natural language search query from the user.
        extracted_keywords (str): A JSON string or comma-separated keywords.
        search_datetime (datetime): When the search was performed.
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute(
        """
        INSERT INTO ImageQueries (query, extracted_keywords, datetime)
        VALUES (?, ?, ?)
        """,
        query, extracted_keywords, search_datetime
    )
    conn.commit()
    cursor.close()
    conn.close()

def fetch_search_history(limit=20):
    """
    Fetch recent search queries from the ImageQueries table.
    Args:
        limit (int): Number of recent queries to return.
    Returns:
        list[dict]: List of search history records.
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute(
        f"""
        SELECT TOP {limit} query, extracted_keywords, datetime
        FROM ImageQueries
        ORDER BY datetime DESC
        """
    )
    rows = cursor.fetchall()
    cursor.close()
    conn.close()

    # Convert rows to list of dictionaries
    history = []
    for row in rows:
        history.append({
            "Query": row[0],
            "Extracted Keywords": row[1],
            "Datetime": row[2]
        })
    return history
