import os
import pandas as pd
import json
from datetime import datetime
from config import GROQ_API_KEY, CATALOG_CSV
from groq import Groq
from sentence_transformers import SentenceTransformer

client = Groq(api_key=GROQ_API_KEY)
model = SentenceTransformer("all-MiniLM-L6-v2")

def generate_caption(image_source, is_url=False):
    if is_url:
        image_input = {"type": "image_url", "image_url": {"url": image_source}}
    else:
        with open(image_source, "rb") as f:
            image_data = f.read()
        image_input = {"type": "image_url", "image_url": {"image": image_data}}

    response = client.chat.completions.create(
        model="meta-llama/llama-4-scout-17b-16e-instruct",
        messages=[
            {
                "role": "system",
                "content": (
                    "You are an expert visual captioning assistant for fashion product search.\n"
                    "Given an image, describe it in the following format:\n\n"
                    "1️⃣ Keywords: A short, comma-separated list of the most important visual elements in simple words (e.g., 'red dress, woman, outdoor, summer'). Use the most common terms people would search for.\n"
                    "2️⃣ Detailed Description: A structured list of important objects, clothing items, accessories, background elements (bullet points).\n"
                    "3️⃣ Summary: A short, two-line natural language description summarizing the scene.\n\n"
                    "Format your output exactly as:\n"
                    "Keywords: keyword1, keyword2, keyword3\n"
                    "* Item 1\n* Item 2\n...\n"
                    "Your summary here."

                )
            },
            {"role": "user", "content": [image_input]}
        ],
        temperature=0.5,
        max_tokens=300
    )

    caption = response.choices[0].message.content.strip()
    embedding = model.encode(caption).tolist()  # ⚡ cache embedding as JSON list

    # Load existing catalog or create new
    df = pd.read_csv(CATALOG_CSV) if os.path.exists(CATALOG_CSV) else pd.DataFrame(columns=["image_path", "description", "timestamp", "embedding"])
    df = df[df["image_path"] != image_source]  # Remove duplicate if exists

    new_row = pd.DataFrame([{
        "image_path": image_source,
        "description": caption,
        "timestamp": datetime.now().isoformat(),
        "embedding": json.dumps(embedding)  # Serialize as JSON string
    }])

    df = pd.concat([df, new_row], ignore_index=True)
    df.to_csv(CATALOG_CSV, index=False)

    return caption
