GROQ_API_KEY = "********************************************************"
IMAGE_DIR = "data/fashion_images" # Directory to save uploaded images
CATALOG_CSV = "data/image_catalog.csv"   # CSV file to store image paths and captions will created by code
IMGBB_API_KEY = "632f7b9010b488a6984d01b55118dc30" #pass that URL to your model or API → avoid JSON errors and keep your system clean, scalable, and streamlit-friendly.
#if not used igbb TypeError: Object of type bytes is not JSON serializable → because the API you’re calling (like Groq or OpenAI chat endpoint) expects text or URLs, not raw image bytes.
#LLM APIs (like client.chat.completions.create) can’t handle binary files inside a JSON request — they expect strings, URLs, or base64 within a JSON-safe format.