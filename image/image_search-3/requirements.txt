# Core Framework
streamlit>=1.28.0

# AI/ML Models
groq>=0.4.1
sentence-transformers>=2.2.2
torch>=2.0.0
transformers>=4.30.0

# Data Processing
pandas>=2.0.0
numpy>=1.24.0

# Azure Cloud Services
azure-storage-blob>=12.17.0
pyodbc>=4.0.39

# Environment & Configuration
python-dotenv>=1.0.0

# HTTP Requests
requests>=2.31.0

# Image Processing (if needed for local handling)
Pillow>=10.0.0

# JSON handling (built-in, but explicit for clarity)
# json - built-in module

# Date/Time handling (built-in, but dependencies might need it)
# datetime - built-in module

# Utility Libraries
mimetypes>=0.1.4  # Usually built-in, but listed for completeness

# Optional: For better performance
scikit-learn>=1.3.0  # Sometimes used by sentence-transformers

# Optional: For debugging/logging
# logging - built-in module